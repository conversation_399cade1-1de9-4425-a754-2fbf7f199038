# AR客户端防护设置检查测试

## 测试信息
```yaml
test_name: "AR客户端防护设置检查测试"
test_description: "检查AR客户端防护设置的显示和功能"
test_timeout: 180
expected_result: "客户端防护设置正确显示，与控制中心策略一致"
test_environment: "Windows/Linux客户端"
test_priority: "P1"
test_category: "客户端功能测试"
```

## 前置条件
- AR客户端已安装并运行

## 测试步骤

### 步骤1：打开AR客户端
**操作描述**：启动AR客户端应用程序

**目标图片**：
![打开AR客户端](images/open_ar_client_protection.png)

**操作类型**：启动应用
**等待时间**：5秒
**预期结果**：成功打开AR客户端

---

### 步骤2：进入防护设置标签页
**操作描述**：查看客户端界面"防护设置"标签页

**目标图片**：
![防护设置标签页](images/protection_settings_tab.png)

**操作类型**：点击标签
**等待时间**：3秒
**预期结果**：成功进入防护设置页面

---

### 步骤3：查看文件实时防护设置
**操作描述**：查看文件实时防护的配置显示

**目标图片**：
![文件实时防护设置](images/realtime_protection_settings.png)

**操作类型**：查看
**等待时间**：5秒
**预期结果**：正确显示文件实时防护设置

---

### 步骤4：查看恶意行为监控设置
**操作描述**：查看恶意行为监控的配置显示

**目标图片**：
![恶意行为监控设置](images/behavior_monitoring_settings.png)

**操作类型**：查看
**等待时间**：5秒
**预期结果**：正确显示恶意行为监控设置

---

### 步骤5：查看勒索诱捕设置
**操作描述**：查看勒索诱捕功能的配置显示

**目标图片**：
![勒索诱捕设置](images/ransomware_honeypot_settings.png)

**操作类型**：查看
**等待时间**：5秒
**预期结果**：正确显示勒索诱捕设置

---

### 步骤6：查看扫描策略设置
**操作描述**：查看扫描策略的配置显示

**目标图片**：
![扫描策略设置](images/scan_policy_settings.png)

**操作类型**：查看
**等待时间**：5秒
**预期结果**：正确显示扫描策略设置

---

### 步骤7：验证设置不可修改
**操作描述**：尝试修改防护设置，验证客户端界面的防护设置不可修改

**目标图片**：
![设置不可修改](images/settings_not_modifiable.png)

**操作类型**：尝试修改
**等待时间**：5秒
**预期结果**：客户端界面的防护设置不可修改

---

### 步骤8：对比控制中心策略
**操作描述**：登录控制中心，查看该终端所在组的策略配置

**目标图片**：
![控制中心策略对比](images/center_policy_comparison.png)

**操作类型**：对比验证
**等待时间**：30秒
**预期结果**：防护设置显示的内容，与控制中心中当前终端所在组的策略一致

---

### 步骤9：验证策略同步
**操作描述**：在控制中心修改策略，验证客户端设置是否同步更新

**目标图片**：
![策略同步验证](images/policy_sync_verification.png)

**操作类型**：同步验证
**等待时间**：60秒
**预期结果**：客户端防护设置与控制中心策略保持同步

---

### 步骤10：查看策略生效状态
**操作描述**：查看各项防护功能的生效状态

**目标图片**：
![策略生效状态](images/policy_effective_status.png)

**操作类型**：状态查看
**等待时间**：10秒
**预期结果**：各项防护功能按策略设置正确生效

---

### 步骤11：截图保存测试结果
**操作描述**：对客户端防护设置测试进行截图保存

**操作类型**：截图
**保存文件名**：ar_client_protection_settings_test_{timestamp}.png
**预期结果**：测试结果截图保存成功

## 测试验证点
```yaml
verification_points:
  - name: "防护设置页面正常"
    description: "防护设置页面能够正常显示"
    image: "images/protection_settings_tab.png"
    critical: true
  
  - name: "文件实时防护设置正确"
    description: "文件实时防护设置正确显示"
    image: "images/realtime_protection_settings.png"
    critical: true
  
  - name: "恶意行为监控设置正确"
    description: "恶意行为监控设置正确显示"
    image: "images/behavior_monitoring_settings.png"
    critical: true
    
  - name: "勒索诱捕设置正确"
    description: "勒索诱捕设置正确显示"
    image: "images/ransomware_honeypot_settings.png"
    critical: true
    
  - name: "扫描策略设置正确"
    description: "扫描策略设置正确显示"
    image: "images/scan_policy_settings.png"
    critical: true
    
  - name: "设置不可修改"
    description: "客户端防护设置不可修改"
    image: "images/settings_not_modifiable.png"
    critical: true
    
  - name: "与控制中心策略一致"
    description: "客户端设置与控制中心策略一致"
    image: "images/center_policy_comparison.png"
    critical: true
    
  - name: "策略同步正常"
    description: "策略修改后客户端正确同步"
    image: "images/policy_sync_verification.png"
    critical: true
```

## 测试数据
```yaml
test_data:
  protection_components:
    - "文件实时防护"
    - "恶意行为监控"
    - "勒索诱捕"
    - "病毒扫描"
    - "自动升级"
    
  setting_fields:
    realtime_protection:
      - "启用状态"
      - "处置方式"
      - "扫描类型"
      - "排除路径"
    
    behavior_monitoring:
      - "启用状态"
      - "监控级别"
      - "处置方式"
      - "白名单"
    
    ransomware_honeypot:
      - "启用状态"
      - "诱捕路径"
      - "处置方式"
    
    scan_policy:
      - "定时扫描"
      - "扫描范围"
      - "扫描深度"
      
  ui_characteristics:
    - "设置项只读显示"
    - "无修改按钮"
    - "灰色不可编辑状态"
    
  sync_verification:
    sync_time: "60秒内"
    sync_trigger: "策略修改后"
    
  policy_sources:
    - "分组策略"
    - "默认策略"
    - "自定义策略"
```
