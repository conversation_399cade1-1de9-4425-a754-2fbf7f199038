# AR升级任务状态检查测试

## 测试信息
```yaml
test_name: "AR控制中心升级任务状态检查测试"
test_description: "检查控制中心升级任务的状态显示和详情"
test_timeout: 300
expected_result: "升级任务状态正确显示，任务详情完整准确"
test_environment: "Windows/Linux控制中心"
test_priority: "P1"
test_category: "终端管理/升级任务"
```

## 前置条件
- 成功执行过升级任务

## 测试步骤

### 步骤1：登录控制中心
**操作描述**：使用管理员账号登录AR控制中心

**目标图片**：
![登录控制中心](images/login_center_upgrade_task_status.png)

**操作类型**：登录
**等待时间**：5秒
**预期结果**：成功登录控制中心

---

### 步骤2：进入升级任务
**操作描述**：进入控制中心-终端管理-升级任务，查看列表与详情

**目标图片**：
![进入升级任务](images/enter_upgrade_task_management.png)

**操作类型**：导航
**等待时间**：3秒
**预期结果**：成功进入升级任务管理页面

---

### 步骤3：查看升级任务列表
**操作描述**：查看升级任务列表的显示内容

**目标图片**：
![升级任务列表](images/upgrade_task_list.png)

**操作类型**：查看
**等待时间**：5秒
**预期结果**：准确显示所下发的任务、以及执行状态

---

### 步骤4：查看任务基本信息
**操作描述**：查看升级任务的基本信息

**目标图片**：
![升级任务基本信息](images/upgrade_task_basic_info.png)

**操作类型**：查看
**等待时间**：3秒
**预期结果**：升级任务基本信息显示完整

---

### 步骤5：查看升级类型信息
**操作描述**：查看升级类型（终端升级、病毒库升级等）

**目标图片**：
![升级类型信息](images/upgrade_type_info.png)

**操作类型**：查看
**等待时间**：3秒
**预期结果**：升级类型信息显示正确

---

### 步骤6：查看目标终端信息
**操作描述**：查看升级任务的目标终端信息

**目标图片**：
![升级目标终端](images/upgrade_target_terminals.png)

**操作类型**：查看
**等待时间**：3秒
**预期结果**：目标终端信息显示正确

---

### 步骤7：查看升级进度
**操作描述**：查看正在执行的升级任务进度

**目标图片**：
![升级任务进度](images/upgrade_task_progress.png)

**操作类型**：查看
**等待时间**：10秒
**预期结果**：升级进度信息实时更新

---

### 步骤8：查看升级状态
**操作描述**：查看各终端的升级状态（下载中、安装中、已完成、失败等）

**目标图片**：
![升级任务状态](images/upgrade_task_status.png)

**操作类型**：查看
**等待时间**：5秒
**预期结果**：升级状态显示准确

---

### 步骤9：查看升级结果
**操作描述**：查看已完成升级任务的结果详情

**目标图片**：
![升级任务结果](images/upgrade_task_result.png)

**操作类型**：查看
**等待时间**：5秒
**预期结果**：升级结果显示详细

---

### 步骤10：查看版本变化信息
**操作描述**：查看升级前后的版本变化信息

**目标图片**：
![版本变化信息](images/version_change_info.png)

**操作类型**：查看
**等待时间**：5秒
**预期结果**：版本变化信息显示准确

---

### 步骤11：查看升级失败详情
**操作描述**：查看升级失败任务的详细错误信息

**目标图片**：
![升级失败详情](images/upgrade_failure_details.png)

**操作类型**：查看
**等待时间**：5秒
**预期结果**：失败原因显示详细

---

### 步骤12：筛选升级任务
**操作描述**：使用筛选功能按状态或类型筛选升级任务

**目标图片**：
![筛选升级任务](images/filter_upgrade_tasks.png)

**操作类型**：筛选
**等待时间**：5秒
**预期结果**：筛选功能正常工作

---

### 步骤13：截图保存测试结果
**操作描述**：对升级任务状态检查测试进行截图保存

**操作类型**：截图
**保存文件名**：ar_upgrade_task_status_test_{timestamp}.png
**预期结果**：测试结果截图保存成功

## 测试验证点
```yaml
verification_points:
  - name: "升级任务页面正常"
    description: "升级任务管理页面能够正常加载"
    image: "images/enter_upgrade_task_management.png"
    critical: true
  
  - name: "任务列表显示正确"
    description: "升级任务列表正确显示所有任务"
    image: "images/upgrade_task_list.png"
    critical: true
  
  - name: "升级类型显示正确"
    description: "升级类型信息显示正确"
    image: "images/upgrade_type_info.png"
    critical: true
    
  - name: "升级进度实时更新"
    description: "升级进度信息能够实时更新"
    image: "images/upgrade_task_progress.png"
    critical: true
    
  - name: "升级状态显示准确"
    description: "各终端升级状态显示准确"
    image: "images/upgrade_task_status.png"
    critical: true
    
  - name: "升级结果显示详细"
    description: "升级结果显示详细"
    image: "images/upgrade_task_result.png"
    critical: true
    
  - name: "版本变化信息准确"
    description: "版本变化信息显示准确"
    image: "images/version_change_info.png"
    critical: true
    
  - name: "失败详情显示完整"
    description: "升级失败详情显示完整"
    image: "images/upgrade_failure_details.png"
    critical: true
```

## 测试数据
```yaml
test_data:
  upgrade_status_types:
    - "待执行"
    - "下载中"
    - "安装中"
    - "已完成"
    - "执行失败"
    - "已取消"
    
  upgrade_types:
    - "终端升级"
    - "病毒库升级"
    - "终端升级+病毒库升级"
    
  task_info_fields:
    basic_info:
      - "任务ID"
      - "任务名称"
      - "创建时间"
      - "升级类型"
      - "创建用户"
    
    target_info:
      - "目标终端数量"
      - "终端列表"
      - "分组信息"
    
    progress_info:
      - "总体进度"
      - "下载进度"
      - "安装进度"
      - "完成数量"
    
    result_info:
      - "成功数量"
      - "失败数量"
      - "升级前版本"
      - "升级后版本"
      
  status_indicators:
    downloading: "下载中"
    installing: "安装中"
    completed: "已完成"
    failed: "失败"
    
  version_info:
    - "升级前软件版本"
    - "升级后软件版本"
    - "升级前病毒库版本"
    - "升级后病毒库版本"
    
  failure_reasons:
    - "下载失败"
    - "安装失败"
    - "网络连接超时"
    - "磁盘空间不足"
    - "权限不足"
    - "版本不兼容"
```
