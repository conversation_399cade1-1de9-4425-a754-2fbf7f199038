# AR风险状态复位测试

## 测试信息
```yaml
test_name: "AR控制中心风险状态复位功能测试"
test_description: "测试控制中心复位终端风险状态的功能"
test_timeout: 180
expected_result: "风险状态复位功能正常工作，风险终端状态正确重置"
test_environment: "Windows/Linux控制中心"
test_priority: "P1"
test_category: "控制中心/终端列表"
```

## 前置条件
- 触发过恶意行为监控告警，且未复位

## 测试步骤

### 步骤1：登录控制中心
**操作描述**：使用浏览器登录AR控制中心

**目标图片**：
![登录控制中心](images/login_center_risk_reset.png)

**操作类型**：登录
**等待时间**：5秒
**预期结果**：成功登录控制中心

---

### 步骤2：进入终端列表
**操作描述**：进入控制中心-终端管理-终端列表

**目标图片**：
![进入终端列表](images/enter_terminal_list_risk.png)

**操作类型**：导航
**等待时间**：3秒
**预期结果**：成功进入终端列表页面

---

### 步骤3：识别风险终端
**操作描述**：在终端列表中识别风险状态的终端（红色别名）

**目标图片**：
![识别风险终端](images/identify_risk_terminals.png)

**操作类型**：查看
**等待时间**：5秒
**预期结果**：能够识别出风险状态的终端（别名为红色）

---

### 步骤4：选择风险终端
**操作描述**：选择若干风险状态的客户端

**目标图片**：
![选择风险终端](images/select_risk_terminals.png)

**操作类型**：选择
**等待时间**：3秒
**预期结果**：成功选择风险状态的终端

---

### 步骤5：选择正常终端（对照组）
**操作描述**：同时选择一些正常状态的终端作为对照

**目标图片**：
![选择正常终端](images/select_normal_terminals.png)

**操作类型**：选择
**等待时间**：3秒
**预期结果**：成功选择正常状态的终端

---

### 步骤6：点击复位风险状态按钮
**操作描述**：点击"复位风险状态"按钮

**目标图片**：
![点击复位按钮](images/click_reset_risk_button.png)

**操作类型**：点击
**等待时间**：3秒
**预期结果**：弹出确认对话框

---

### 步骤7：确认复位操作
**操作描述**：在确认对话框中点击确认

**目标图片**：
![确认复位操作](images/confirm_reset_operation.png)

**操作类型**：确认
**等待时间**：5秒
**预期结果**：复位操作开始执行

---

### 步骤8：验证风险终端状态变化
**操作描述**：检查原来的风险终端状态是否变为绿色

**目标图片**：
![验证风险终端变化](images/verify_risk_terminal_change.png)

**操作类型**：验证
**等待时间**：10秒
**预期结果**：确认后，选中的风险终端（别名为红色）被重置为绿色

---

### 步骤9：验证正常终端状态不变
**操作描述**：检查原来正常状态的终端是否保持不变

**目标图片**：
![验证正常终端不变](images/verify_normal_terminal_unchanged.png)

**操作类型**：验证
**等待时间**：5秒
**预期结果**：选中的无风险终端（其它颜色）会忽略（不会变色）

---

### 步骤10：刷新终端列表
**操作描述**：刷新终端列表确认状态变化持久化

**目标图片**：
![刷新终端列表](images/refresh_terminal_list.png)

**操作类型**：刷新
**等待时间**：5秒
**预期结果**：刷新后状态变化保持

---

### 步骤11：检查操作日志
**操作描述**：查看中心操作日志中的复位操作记录

**目标图片**：
![检查操作日志](images/check_operation_logs.png)

**操作类型**：查看
**等待时间**：10秒
**预期结果**：操作日志正确记录复位操作

---

### 步骤12：验证风险状态清除
**操作描述**：确认终端的风险状态已被清除

**目标图片**：
![验证风险状态清除](images/verify_risk_status_cleared.png)

**操作类型**：验证
**等待时间**：5秒
**预期结果**：终端风险状态标记被清除

---

### 步骤13：截图保存测试结果
**操作描述**：对风险状态复位测试进行截图保存

**操作类型**：截图
**保存文件名**：ar_risk_status_reset_test_{timestamp}.png
**预期结果**：测试结果截图保存成功

## 测试验证点
```yaml
verification_points:
  - name: "正确识别风险终端"
    description: "能够正确识别风险状态的终端"
    image: "images/identify_risk_terminals.png"
    critical: true
  
  - name: "复位功能正常触发"
    description: "复位风险状态功能能够正常触发"
    image: "images/click_reset_risk_button.png"
    critical: true
  
  - name: "风险终端状态正确重置"
    description: "风险终端状态正确从红色重置为绿色"
    image: "images/verify_risk_terminal_change.png"
    critical: true
    
  - name: "正常终端状态不受影响"
    description: "正常状态终端不受复位操作影响"
    image: "images/verify_normal_terminal_unchanged.png"
    critical: true
    
  - name: "操作日志正确记录"
    description: "复位操作被正确记录到操作日志"
    image: "images/check_operation_logs.png"
    critical: true
    
  - name: "状态变化持久化"
    description: "状态变化在刷新后保持"
    image: "images/refresh_terminal_list.png"
    critical: true
```

## 测试数据
```yaml
test_data:
  terminal_status_colors:
    risk: "红色"
    normal: "绿色"
    offline: "灰色"
    scanning: "黄色"
    
  risk_triggers:
    - "恶意行为监控告警"
    - "勒索软件检测"
    - "文件实时防护告警"
    
  reset_operation:
    button_name: "复位风险状态"
    confirmation_required: true
    
  expected_behavior:
    risk_terminals: "红色变为绿色"
    normal_terminals: "颜色保持不变"
    
  log_verification:
    log_location: "中心操作日志"
    expected_fields:
      - "操作时间"
      - "操作用户"
      - "操作类型"
      - "操作对象"
      - "操作结果"
      
  test_scenarios:
    - scenario: "仅选择风险终端"
      expected: "风险终端状态重置"
    - scenario: "仅选择正常终端"
      expected: "终端状态不变"
    - scenario: "混合选择风险和正常终端"
      expected: "仅风险终端状态重置"
```
