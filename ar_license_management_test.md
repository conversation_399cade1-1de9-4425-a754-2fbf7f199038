# AR授权许可管理测试

## 测试信息
```yaml
test_name: "AR控制中心授权许可管理测试"
test_description: "测试AR控制中心的授权许可管理功能"
test_timeout: 600
expected_result: "授权许可管理功能正常，许可信息准确显示"
test_environment: "Windows/Linux控制中心"
test_priority: "P1"
test_category: "系统管理测试"
```

## 前置条件
- AR控制中心已安装
- 拥有有效的授权许可

## 测试步骤

### 步骤1：登录控制中心
**操作描述**：使用管理员账号登录AR控制中心

**目标图片**：
![登录控制中心](images/login_center_license_mgmt.png)

**操作类型**：登录
**等待时间**：5秒
**预期结果**：成功登录控制中心

---

### 步骤2：进入授权管理
**操作描述**：进入控制中心-系统管理-授权管理

**目标图片**：
![进入授权管理](images/enter_license_management.png)

**操作类型**：导航
**等待时间**：3秒
**预期结果**：成功进入授权管理页面

---

### 步骤3：查看当前授权信息
**操作描述**：查看当前系统的授权许可信息

**目标图片**：
![当前授权信息](images/current_license_info.png)

**操作类型**：信息查看
**等待时间**：5秒
**预期结果**：显示当前授权许可的详细信息

---

### 步骤4：查看授权点数统计
**操作描述**：查看各类终端的授权点数统计

**目标图片**：
![授权点数统计](images/license_points_statistics.png)

**操作类型**：统计查看
**等待时间**：5秒
**预期结果**：显示Windows/Linux/信创终端的授权点数

---

### 步骤5：查看授权使用情况
**操作描述**：查看授权点数的使用情况

**目标图片**：
![授权使用情况](images/license_usage_status.png)

**操作类型**：使用情况查看
**等待时间**：5秒
**预期结果**：显示已使用和剩余的授权点数

---

### 步骤6：测试授权导入功能
**操作描述**：测试导入新的授权许可文件

**目标图片**：
![授权导入功能](images/license_import_function.png)

**操作类型**：授权导入
**等待时间**：30秒
**预期结果**：授权许可导入成功

---

### 步骤7：验证授权更新
**操作描述**：验证导入后授权信息是否更新

**目标图片**：
![授权更新验证](images/license_update_verification.png)

**操作类型**：更新验证
**等待时间**：10秒
**预期结果**：授权信息成功更新

---

### 步骤8：测试授权导出功能
**操作描述**：测试导出当前授权信息

**目标图片**：
![授权导出功能](images/license_export_function.png)

**操作类型**：授权导出
**等待时间**：15秒
**预期结果**：授权信息导出成功

---

### 步骤9：查看授权历史记录
**操作描述**：查看授权变更的历史记录

**目标图片**：
![授权历史记录](images/license_history_records.png)

**操作类型**：历史查看
**等待时间**：10秒
**预期结果**：显示授权变更历史记录

---

### 步骤10：测试授权到期提醒
**操作描述**：测试授权即将到期的提醒功能

**目标图片**：
![授权到期提醒](images/license_expiry_reminder.png)

**操作类型**：到期提醒测试
**等待时间**：30秒
**预期结果**：授权到期提醒功能正常

---

### 步骤11：验证授权限制
**操作描述**：验证超出授权点数时的限制机制

**目标图片**：
![授权限制验证](images/license_restriction_verification.png)

**操作类型**：限制验证
**等待时间**：60秒
**预期结果**：超出授权时正确限制新终端接入

---

### 步骤12：测试授权备份
**操作描述**：测试授权信息的备份功能

**目标图片**：
![授权备份测试](images/license_backup_test.png)

**操作类型**：备份测试
**等待时间**：30秒
**预期结果**：授权信息备份成功

---

### 步骤13：生成授权报告
**操作描述**：生成授权使用情况报告

**目标图片**：
![授权使用报告](images/license_usage_report.png)

**操作类型**：报告生成
**等待时间**：30秒
**预期结果**：授权使用报告生成成功

---

### 步骤14：截图保存测试结果
**操作描述**：对授权许可管理测试进行截图保存

**操作类型**：截图
**保存文件名**：ar_license_management_test_{timestamp}.png
**预期结果**：测试结果截图保存成功

## 测试验证点
```yaml
verification_points:
  - name: "授权信息显示正确"
    description: "当前授权许可信息显示正确"
    image: "images/current_license_info.png"
    critical: true
  
  - name: "授权点数统计准确"
    description: "各类终端授权点数统计准确"
    image: "images/license_points_statistics.png"
    critical: true
  
  - name: "使用情况显示正确"
    description: "授权使用情况显示正确"
    image: "images/license_usage_status.png"
    critical: true
    
  - name: "授权导入功能正常"
    description: "授权许可导入功能正常"
    image: "images/license_import_function.png"
    critical: true
    
  - name: "授权更新成功"
    description: "导入后授权信息成功更新"
    image: "images/license_update_verification.png"
    critical: true
    
  - name: "授权导出功能正常"
    description: "授权信息导出功能正常"
    image: "images/license_export_function.png"
    critical: false
    
  - name: "历史记录完整"
    description: "授权变更历史记录完整"
    image: "images/license_history_records.png"
    critical: false
    
  - name: "到期提醒正常"
    description: "授权到期提醒功能正常"
    image: "images/license_expiry_reminder.png"
    critical: true
    
  - name: "授权限制有效"
    description: "超出授权时限制机制有效"
    image: "images/license_restriction_verification.png"
    critical: true
    
  - name: "使用报告准确"
    description: "授权使用报告准确完整"
    image: "images/license_usage_report.png"
    critical: false
```

## 测试数据
```yaml
test_data:
  license_types:
    - "试用版授权"
    - "标准版授权"
    - "企业版授权"
    - "永久授权"
    - "订阅授权"
    
  terminal_types:
    - "Windows终端"
    - "Linux终端"
    - "信创终端"
    
  license_info_fields:
    - "授权类型"
    - "序列号"
    - "授权点数"
    - "有效期"
    - "授权机构"
    - "联系信息"
    
  usage_statistics:
    - "总授权点数"
    - "已使用点数"
    - "剩余点数"
    - "使用率"
    
  import_formats:
    - ".lic文件"
    - ".key文件"
    - "授权码"
    
  export_formats:
    - "PDF报告"
    - "Excel表格"
    - "文本文件"
    
  reminder_settings:
    - "到期前30天提醒"
    - "到期前7天提醒"
    - "到期当天提醒"
    
  restriction_behaviors:
    - "禁止新终端接入"
    - "显示授权不足警告"
    - "限制部分功能"
    
  backup_content:
    - "授权文件"
    - "配置信息"
    - "使用记录"
    
  report_content:
    - "授权概况"
    - "使用统计"
    - "终端分布"
    - "历史变更"
```
