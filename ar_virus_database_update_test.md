# AR病毒库更新测试

## 测试信息
```yaml
test_name: "AR病毒库更新测试"
test_description: "测试AR客户端病毒库的更新功能"
test_timeout: 600
expected_result: "病毒库能够成功更新到最新版本"
test_environment: "Windows/Linux客户端"
test_priority: "P0"
test_category: "客户端功能测试"
```

## 前置条件
- 已安装AR客户端
- 网络连接正常

## 测试步骤

### 步骤1：检查当前病毒库版本
**操作描述**：查看当前客户端的病毒库版本

**目标图片**：
![当前病毒库版本](images/current_virus_db_version.png)

**操作类型**：版本查看
**等待时间**：5秒
**预期结果**：显示当前病毒库版本信息

---

### 步骤2：检查更新设置
**操作描述**：查看病毒库自动更新设置

**目标图片**：
![更新设置检查](images/update_settings_check.png)

**操作类型**：设置查看
**等待时间**：3秒
**预期结果**：显示病毒库更新相关设置

---

### 步骤3：手动检查更新
**操作描述**：手动触发病毒库更新检查

**目标图片**：
![手动检查更新](images/manual_update_check.png)

**操作类型**：更新检查
**等待时间**：30秒
**预期结果**：系统检查是否有可用的病毒库更新

---

### 步骤4：开始更新下载
**操作描述**：如果有更新可用，开始下载病毒库更新

**目标图片**：
![开始更新下载](images/start_update_download.png)

**操作类型**：下载更新
**等待时间**：10秒
**预期结果**：开始下载病毒库更新文件

---

### 步骤5：监控下载进度
**操作描述**：监控病毒库更新的下载进度

**目标图片**：
![下载进度监控](images/download_progress_monitoring.png)

**操作类型**：进度监控
**等待时间**：300秒
**预期结果**：显示下载进度，下载过程正常

---

### 步骤6：验证下载完成
**操作描述**：验证病毒库更新文件下载完成

**目标图片**：
![下载完成验证](images/download_completion_verification.png)

**操作类型**：完成验证
**等待时间**：30秒
**预期结果**：病毒库更新文件下载完成

---

### 步骤7：执行更新安装
**操作描述**：执行病毒库更新的安装过程

**目标图片**：
![更新安装执行](images/update_installation_execution.png)

**操作类型**：安装更新
**等待时间**：120秒
**预期结果**：病毒库更新安装过程正常执行

---

### 步骤8：验证更新完成
**操作描述**：验证病毒库更新是否成功完成

**目标图片**：
![更新完成验证](images/update_completion_verification.png)

**操作类型**：完成验证
**等待时间**：30秒
**预期结果**：病毒库更新成功完成

---

### 步骤9：检查新版本号
**操作描述**：检查更新后的病毒库版本号

**目标图片**：
![新版本号检查](images/new_version_number_check.png)

**操作类型**：版本验证
**等待时间**：10秒
**预期结果**：病毒库版本号已更新到最新版本

---

### 步骤10：验证控制中心同步
**操作描述**：在控制中心验证客户端病毒库版本是否同步更新

**目标图片**：
![控制中心版本同步](images/center_version_sync.png)

**操作类型**：同步验证
**等待时间**：60秒
**预期结果**：控制中心显示客户端病毒库版本已更新

---

### 步骤11：测试自动更新功能
**操作描述**：测试病毒库自动更新功能（如果启用）

**目标图片**：
![自动更新测试](images/automatic_update_test.png)

**操作类型**：自动更新测试
**等待时间**：600秒
**预期结果**：自动更新功能正常工作

---

### 步骤12：检查更新日志
**操作描述**：查看病毒库更新相关的日志记录

**目标图片**：
![更新日志检查](images/update_log_check.png)

**操作类型**：日志查看
**等待时间**：10秒
**预期结果**：更新过程被正确记录到日志

---

### 步骤13：验证更新后功能
**操作描述**：验证病毒库更新后扫描功能是否正常

**目标图片**：
![更新后功能验证](images/post_update_function_verification.png)

**操作类型**：功能验证
**等待时间**：60秒
**预期结果**：病毒库更新后扫描功能正常工作

---

### 步骤14：截图保存测试结果
**操作描述**：对病毒库更新测试进行截图保存

**操作类型**：截图
**保存文件名**：ar_virus_database_update_test_{timestamp}.png
**预期结果**：测试结果截图保存成功

## 测试验证点
```yaml
verification_points:
  - name: "当前版本显示正确"
    description: "能够正确显示当前病毒库版本"
    image: "images/current_virus_db_version.png"
    critical: true
  
  - name: "更新检查功能正常"
    description: "手动更新检查功能正常工作"
    image: "images/manual_update_check.png"
    critical: true
  
  - name: "下载功能正常"
    description: "病毒库更新下载功能正常"
    image: "images/start_update_download.png"
    critical: true
    
  - name: "下载进度显示正确"
    description: "下载进度能够正确显示"
    image: "images/download_progress_monitoring.png"
    critical: true
    
  - name: "安装过程正常"
    description: "病毒库更新安装过程正常"
    image: "images/update_installation_execution.png"
    critical: true
    
  - name: "版本更新成功"
    description: "病毒库版本成功更新"
    image: "images/new_version_number_check.png"
    critical: true
    
  - name: "控制中心同步正常"
    description: "控制中心能够同步显示新版本"
    image: "images/center_version_sync.png"
    critical: true
    
  - name: "自动更新功能正常"
    description: "自动更新功能正常工作"
    image: "images/automatic_update_test.png"
    critical: false
    
  - name: "更新日志记录完整"
    description: "更新过程被正确记录"
    image: "images/update_log_check.png"
    critical: false
    
  - name: "更新后功能正常"
    description: "更新后扫描功能正常工作"
    image: "images/post_update_function_verification.png"
    critical: true
```

## 测试数据
```yaml
test_data:
  version_format: "YYYYMMDD.HH"
  
  update_sources:
    - "官方更新服务器"
    - "控制中心分发"
    - "本地更新包"
    
  update_methods:
    - "手动更新"
    - "自动更新"
    - "计划更新"
    - "远程推送更新"
    
  update_settings:
    auto_update: "启用/禁用"
    update_frequency: "每小时/每天/每周"
    update_time: "指定时间"
    
  download_verification:
    - "下载进度显示"
    - "下载速度显示"
    - "剩余时间估算"
    - "下载完整性校验"
    
  installation_steps:
    - "备份旧版本"
    - "安装新版本"
    - "验证安装"
    - "清理临时文件"
    
  update_log_fields:
    - "更新时间"
    - "更新类型"
    - "旧版本号"
    - "新版本号"
    - "更新结果"
    - "错误信息"
    
  failure_scenarios:
    - "网络连接失败"
    - "下载中断"
    - "安装失败"
    - "版本验证失败"
    
  rollback_mechanism:
    - "自动回滚"
    - "手动回滚"
    - "版本恢复"
    
  update_frequency_test:
    manual_check: "立即检查"
    scheduled_check: "定时检查"
    automatic_check: "自动检查"
```
