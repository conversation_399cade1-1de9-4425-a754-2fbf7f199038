# AR控制中心安装测试

## 测试信息
```yaml
test_name: "AR控制中心手动安装测试"
test_description: "在测试服务器中手动安装AR控制中心"
test_timeout: 600
expected_result: "AR控制中心安装成功并可正常访问"
test_environment: "Linux测试服务器"
test_priority: "P0"
test_category: "控制中心/安装卸载"
```

## 前置条件
- 已上传AR控制中心安装包于测试服务器中

## 测试步骤

### 步骤1：SSH连接测试服务器
**操作描述**：通过MobaXterm使用ssh连接一体机IP地址（**************）连接到测试服务器，账户root，密码Mp2010.18

**目标图片**：
![SSH连接界面](images/ssh_connection.png)

**操作类型**：连接
**等待时间**：20秒
**预期结果**：ssh连接一体机成功

---

### 步骤2：解压安装包
**操作描述**：cd到存有安装包的路径，解压安装包

**目标图片**：
![解压安装包](images/extract_package.png)

**操作类型**：命令执行
**等待时间**：10秒
**预期结果**：安装包解压成功

---

### 步骤3：赋权并运行安装程序
**操作描述**：chmod赋权，然后运行setup文件进行安装

**目标图片**：
![运行安装程序](images/run_setup.png)

**操作类型**：命令执行
**等待时间**：60秒
**预期结果**：显示AR控制中心安装成功

---

### 步骤4：验证Web访问
**操作描述**：使用浏览器访问 http://AR中心IP:8088，使用默认账号密码admin/Admin.2022登录

**目标图片**：
![AR控制中心登录界面](images/ar_center_login.png)

**操作类型**：验证
**等待时间**：10秒
**预期结果**：登录成功，进入AR控制中心主界面

---

### 步骤5：放置客户端安装包
**操作描述**：把客户端安装包放入 /opt/apps/mredr_center/mrLinuxCenter/download/ 目录下

**目标图片**：
![客户端安装包目录](images/client_package_directory.png)

**操作类型**：文件操作
**等待时间**：5秒
**预期结果**：控制中心可下载客户端安装包

---

### 步骤6：截图保存测试结果
**操作描述**：对AR控制中心主界面进行截图保存

**操作类型**：截图
**保存文件名**：ar_control_center_install_test_{timestamp}.png
**预期结果**：测试结果截图保存成功

## 测试验证点
```yaml
verification_points:
  - name: "SSH连接成功"
    description: "能够成功连接到测试服务器"
    critical: true
  
  - name: "安装程序执行成功"
    description: "AR控制中心安装程序正常执行完成"
    image: "images/install_success.png"
    critical: true
  
  - name: "Web界面可访问"
    description: "能够通过浏览器正常访问AR控制中心"
    image: "images/ar_center_login.png"
    critical: true
    
  - name: "默认账号登录成功"
    description: "使用默认账号密码能够成功登录"
    image: "images/ar_center_dashboard.png"
    critical: true
```

## 测试数据
```yaml
test_data:
  server_info:
    ip: "测试服务器IP"
    os: "Linux"
    
  ar_center:
    default_username: "admin"
    default_password: "Admin.2022"
    web_port: "8088"
    install_path: "/opt/apps/mredr_center/"
    
  client_package_path: "/opt/apps/mredr_center/mrLinuxCenter/download/"
```
