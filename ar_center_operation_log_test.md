# AR中心操作日志测试

## 测试信息
```yaml
test_name: "AR控制中心中心操作日志检查测试"
test_description: "检查控制中心中心操作日志的显示和功能"
test_timeout: 180
expected_result: "中心操作日志正确显示各类管理操作记录"
test_environment: "Windows/Linux控制中心"
test_priority: "P1"
test_category: "事件日志/中心操作"
```

## 前置条件
- 执行过中心管理操作（用户管理、策略管理、系统配置等）

## 测试步骤

### 步骤1：登录控制中心
**操作描述**：使用管理员账号登录AR控制中心

**目标图片**：
![登录控制中心](images/login_center_operation_log.png)

**操作类型**：登录
**等待时间**：5秒
**预期结果**：成功登录控制中心

---

### 步骤2：进入事件日志
**操作描述**：进入控制中心-事件日志菜单

**目标图片**：
![进入事件日志](images/enter_event_logs_center_op.png)

**操作类型**：导航
**等待时间**：3秒
**预期结果**：成功进入事件日志页面

---

### 步骤3：点击中心操作日志
**操作描述**：点击事件日志-中心操作

**目标图片**：
![点击中心操作日志](images/click_center_operation_logs.png)

**操作类型**：点击
**等待时间**：3秒
**预期结果**：进入中心操作日志页面

---

### 步骤4：查看中心操作日志列表
**操作描述**：查看中心操作日志的列表显示

**目标图片**：
![中心操作日志列表](images/center_operation_log_list.png)

**操作类型**：查看
**等待时间**：5秒
**预期结果**：有对应的中心操作的日志

---

### 步骤5：查看用户管理操作日志
**操作描述**：查看用户管理相关操作的日志记录

**目标图片**：
![用户管理操作日志](images/user_management_operation_logs.png)

**操作类型**：查看
**等待时间**：5秒
**预期结果**：用户管理操作日志显示完整

---

### 步骤6：查看策略管理操作日志
**操作描述**：查看策略管理相关操作的日志记录

**目标图片**：
![策略管理操作日志](images/policy_management_operation_logs.png)

**操作类型**：查看
**等待时间**：5秒
**预期结果**：策略管理操作日志显示完整

---

### 步骤7：查看白名单管理操作日志
**操作描述**：查看白名单管理相关操作的日志记录

**目标图片**：
![白名单管理操作日志](images/whitelist_management_operation_logs.png)

**操作类型**：查看
**等待时间**：5秒
**预期结果**：白名单管理操作日志显示完整

---

### 步骤8：查看系统配置操作日志
**操作描述**：查看系统配置相关操作的日志记录

**目标图片**：
![系统配置操作日志](images/system_config_operation_logs.png)

**操作类型**：查看
**等待时间**：5秒
**预期结果**：系统配置操作日志显示完整

---

### 步骤9：查看登录操作日志
**操作描述**：查看用户登录相关操作的日志记录

**目标图片**：
![登录操作日志](images/login_operation_logs.png)

**操作类型**：查看
**等待时间**：5秒
**预期结果**：登录操作日志显示完整

---

### 步骤10：查看日志详细信息
**操作描述**：点击某条日志，查看详细信息

**目标图片**：
![中心操作日志详情](images/center_operation_log_details.png)

**操作类型**：查看详情
**等待时间**：5秒
**预期结果**：日志详细信息显示完整

---

### 步骤11：验证日志字段完整性
**操作描述**：验证日志中各字段信息的完整性

**目标图片**：
![中心日志字段完整性](images/center_log_fields_completeness.png)

**操作类型**：验证
**等待时间**：5秒
**预期结果**：操作时间、操作用户、功能模块、操作类型、操作对象、操作结果等字段显示完整

---

### 步骤12：测试日志筛选功能
**操作描述**：使用时间、用户、操作类型等条件筛选中心操作日志

**目标图片**：
![中心操作日志筛选](images/center_operation_log_filter.png)

**操作类型**：筛选
**等待时间**：5秒
**预期结果**：筛选功能正常工作

---

### 步骤13：测试日志搜索功能
**操作描述**：使用关键词搜索特定的中心操作日志

**目标图片**：
![中心操作日志搜索](images/center_operation_log_search.png)

**操作类型**：搜索
**等待时间**：5秒
**预期结果**：搜索功能正常工作

---

### 步骤14：截图保存测试结果
**操作描述**：对中心操作日志测试进行截图保存

**操作类型**：截图
**保存文件名**：ar_center_operation_log_test_{timestamp}.png
**预期结果**：测试结果截图保存成功

## 测试验证点
```yaml
verification_points:
  - name: "中心操作日志页面正常"
    description: "中心操作日志页面能够正常加载"
    image: "images/click_center_operation_logs.png"
    critical: true
  
  - name: "操作日志记录完整"
    description: "有对应的中心操作的日志"
    image: "images/center_operation_log_list.png"
    critical: true
  
  - name: "用户管理操作日志正确"
    description: "用户管理操作日志显示完整"
    image: "images/user_management_operation_logs.png"
    critical: true
    
  - name: "策略管理操作日志正确"
    description: "策略管理操作日志显示完整"
    image: "images/policy_management_operation_logs.png"
    critical: true
    
  - name: "白名单管理操作日志正确"
    description: "白名单管理操作日志显示完整"
    image: "images/whitelist_management_operation_logs.png"
    critical: true
    
  - name: "系统配置操作日志正确"
    description: "系统配置操作日志显示完整"
    image: "images/system_config_operation_logs.png"
    critical: true
    
  - name: "登录操作日志正确"
    description: "登录操作日志显示完整"
    image: "images/login_operation_logs.png"
    critical: true
    
  - name: "日志字段完整"
    description: "日志各字段信息显示完整"
    image: "images/center_log_fields_completeness.png"
    critical: true
    
  - name: "筛选搜索功能正常"
    description: "日志筛选和搜索功能正常"
    image: "images/center_operation_log_filter.png"
    critical: false
```

## 测试数据
```yaml
test_data:
  operation_modules:
    - "用户管理"
    - "策略管理"
    - "白名单管理"
    - "系统配置"
    - "终端管理"
    - "任务管理"
    - "日志管理"
    
  operation_types:
    user_management:
      - "新增用户"
      - "编辑用户"
      - "删除用户"
      - "重置密码"
      - "修改权限"
    
    policy_management:
      - "新增策略"
      - "编辑策略"
      - "删除策略"
      - "分配策略"
    
    whitelist_management:
      - "添加白名单"
      - "删除白名单"
      - "编辑白名单"
    
    system_config:
      - "修改系统配置"
      - "更新授权"
      - "修改时区"
      
  log_fields:
    required_fields:
      - "操作时间"
      - "操作用户"
      - "功能模块"
      - "操作类型"
      - "操作对象"
      - "操作结果"
    
    optional_fields:
      - "操作详情"
      - "IP地址"
      - "浏览器信息"
      - "错误信息"
      
  operation_results:
    - "成功"
    - "失败"
    - "部分成功"
    
  filter_options:
    time_range:
      - "今天"
      - "最近7天"
      - "最近30天"
      - "自定义时间"
    
    module_filter:
      - "用户管理"
      - "策略管理"
      - "白名单管理"
      - "系统配置"
    
    user_filter:
      - "admin"
      - "普通管理员"
      - "审计员"
    
    result_filter:
      - "成功"
      - "失败"
      - "全部"
      
  sample_log_entries:
    user_operation:
      time: "2024-01-15 14:30:25"
      user: "admin"
      module: "用户管理"
      operation: "新增用户"
      object: "testuser001"
      result: "成功"
      
    policy_operation:
      time: "2024-01-15 15:00:00"
      user: "admin"
      module: "策略管理"
      operation: "编辑策略"
      object: "默认策略"
      result: "成功"
```
