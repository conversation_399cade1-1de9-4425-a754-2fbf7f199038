# AR控制中心系统开机自动启动测试

## 测试信息
```yaml
test_name: "AR控制中心系统开机自动启动测试"
test_description: "测试AR控制中心服务器重启后自动启动功能"
test_timeout: 600
expected_result: "服务器重启后AR控制中心能够自动启动并正常访问"
test_environment: "Linux控制中心服务器"
test_priority: "P1"
test_category: "控制中心/系统功能"
```

## 前置条件
- 已安装控制中心

## 测试步骤

### 步骤1：记录重启前状态
**操作描述**：记录重启前AR控制中心的运行状态

**目标图片**：
![重启前状态](images/center_status_before_reboot.png)

**操作类型**：状态记录
**等待时间**：5秒
**预期结果**：成功记录重启前的运行状态

---

### 步骤2：检查控制中心服务状态
**操作描述**：使用systemctl检查AR控制中心相关服务状态

**目标图片**：
![检查服务状态](images/check_center_service_status.png)

**操作类型**：命令执行
**等待时间**：3秒
**预期结果**：显示AR控制中心服务正在运行

---

### 步骤3：验证Web访问正常
**操作描述**：通过浏览器访问AR控制中心，确认服务正常

**目标图片**：
![重启前Web访问](images/web_access_before_reboot.png)

**操作类型**：Web访问
**等待时间**：10秒
**预期结果**：能够正常访问AR控制中心Web界面

---

### 步骤4：执行服务器重启
**操作描述**：重启控制中心服务器

**目标图片**：
![执行服务器重启](images/execute_server_reboot.png)

**操作类型**：系统重启
**等待时间**：10秒
**预期结果**：服务器开始重启过程

---

### 步骤5：等待服务器重启完成
**操作描述**：等待服务器完全重启完成

**目标图片**：
![服务器重启完成](images/server_reboot_completed.png)

**操作类型**：等待
**等待时间**：300秒
**预期结果**：服务器重启完成，能够SSH连接

---

### 步骤6：检查系统启动日志
**操作描述**：查看系统启动日志，确认AR控制中心服务启动情况

**目标图片**：
![系统启动日志](images/system_startup_logs.png)

**操作类型**：日志查看
**等待时间**：10秒
**预期结果**：启动日志显示AR控制中心服务正常启动

---

### 步骤7：验证服务自动启动
**操作描述**：检查AR控制中心相关服务是否自动启动

**目标图片**：
![服务自动启动验证](images/service_auto_startup_verification.png)

**操作类型**：命令执行
**等待时间**：5秒
**预期结果**：AR控制中心相关服务已自动启动

---

### 步骤8：测试Web界面访问
**操作描述**：尝试通过浏览器访问AR控制中心

**目标图片**：
![重启后Web访问](images/web_access_after_reboot.png)

**操作类型**：Web访问
**等待时间**：30秒
**预期结果**：服务器重启后，AR控制中心仍能登录成功

---

### 步骤9：验证登录功能
**操作描述**：使用默认账号密码登录AR控制中心

**目标图片**：
![重启后登录验证](images/login_verification_after_reboot.png)

**操作类型**：登录验证
**等待时间**：10秒
**预期结果**：能够成功登录AR控制中心

---

### 步骤10：检查功能完整性
**操作描述**：检查重启后AR控制中心各项功能是否正常

**目标图片**：
![功能完整性检查](images/functionality_check_after_reboot.png)

**操作类型**：功能验证
**等待时间**：30秒
**预期结果**：所有主要功能正常工作

---

### 步骤11：验证终端连接状态
**操作描述**：检查已连接的终端是否仍然在线

**目标图片**：
![终端连接状态](images/terminal_connection_status.png)

**操作类型**：状态验证
**等待时间**：60秒
**预期结果**：终端逐渐重新连接到控制中心

---

### 步骤12：截图保存测试结果
**操作描述**：对重启后的状态进行截图保存

**操作类型**：截图
**保存文件名**：ar_center_auto_startup_test_{timestamp}.png
**预期结果**：测试结果截图保存成功

## 测试验证点
```yaml
verification_points:
  - name: "服务器成功重启"
    description: "控制中心服务器能够成功重启"
    image: "images/server_reboot_completed.png"
    critical: true
  
  - name: "服务自动启动"
    description: "AR控制中心服务在重启后自动启动"
    image: "images/service_auto_startup_verification.png"
    critical: true
  
  - name: "Web界面可访问"
    description: "重启后能够正常访问Web界面"
    image: "images/web_access_after_reboot.png"
    critical: true
    
  - name: "登录功能正常"
    description: "重启后登录功能正常工作"
    image: "images/login_verification_after_reboot.png"
    critical: true
    
  - name: "功能完整性保持"
    description: "重启后所有功能保持完整"
    image: "images/functionality_check_after_reboot.png"
    critical: true
    
  - name: "终端重新连接"
    description: "终端能够重新连接到控制中心"
    image: "images/terminal_connection_status.png"
    critical: false
```

## 测试数据
```yaml
test_data:
  ar_center_services:
    - "mredr_center"
    - "nginx"
    - "mysql"
    - "redis"
    
  web_access:
    url: "http://AR中心IP:8088"
    default_username: "admin"
    default_password: "Admin.2022"
    
  verification_commands:
    check_services: "systemctl status mredr_center"
    check_processes: "ps aux | grep mredr"
    check_ports: "netstat -tlnp | grep 8088"
    
  startup_logs:
    system_log: "/var/log/messages"
    center_log: "/opt/apps/mredr_center/logs/"
    
  expected_startup_time: "5分钟内"
  
  functionality_checks:
    - "首页加载"
    - "终端列表显示"
    - "用户登录"
    - "菜单导航"
    - "数据库连接"
```
