#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终复选框解决方案
基于深度分析结果，使用Element UI的正确方法选择终端
"""

import time
import os
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options

class FinalCheckboxSolution:
    def __init__(self):
        """初始化解决方案"""
        self.driver = None
        self.wait = None
        
        # 测试配置
        self.config = {
            'url': 'http://172.16.103.202:8088',
            'username': 'admin',
            'password': 'Admin.2022',
            'timeout': 5
        }
        
        # 创建截图目录
        self.screenshot_dir = "screenshots"
        if not os.path.exists(self.screenshot_dir):
            os.makedirs(self.screenshot_dir)
    
    def setup_driver(self):
        """设置Chrome浏览器驱动"""
        try:
            chrome_options = Options()
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--window-size=1920,1080")
            
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.maximize_window()
            self.wait = WebDriverWait(self.driver, self.config['timeout'])
            
            print("✅ 浏览器驱动设置成功")
            return True
            
        except Exception as e:
            print(f"❌ 浏览器驱动设置失败: {e}")
            return False
    
    def take_screenshot(self, step_name, description=""):
        """截图功能"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{step_name}_{timestamp}.png"
            filepath = os.path.join(self.screenshot_dir, filename)
            self.driver.save_screenshot(filepath)
            print(f"📸 截图已保存: {filepath} - {description}")
            return filepath
        except Exception as e:
            print(f"❌ 截图失败: {e}")
            return None
    
    def login_and_navigate(self):
        """登录并导航到终端列表"""
        try:
            print("\n🔄 登录控制中心...")
            
            # 登录
            self.driver.get(self.config['url'])
            time.sleep(3)
            
            username_input = self.wait.until(EC.element_to_be_clickable((By.NAME, "username")))
            username_input.clear()
            username_input.send_keys(self.config['username'])
            
            password_input = self.driver.find_element(By.ID, "password")
            password_input.clear()
            password_input.send_keys(self.config['password'])
            
            login_button = self.driver.find_element(By.XPATH, "//button[@type='submit']")
            login_button.click()
            time.sleep(5)
            
            self.take_screenshot("step1_login_success", "登录成功")
            
            # 导航到终端列表
            print("\n🔄 导航到终端列表...")
            
            terminal_menu = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, "//span[contains(text(), '终端管理')]"))
            )
            terminal_menu.click()
            time.sleep(2)
            
            terminal_list = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, "//span[contains(text(), '终端列表')]"))
            )
            terminal_list.click()
            time.sleep(3)
            
            self.take_screenshot("step2_terminal_list", "进入终端列表")
            
            print("✅ 成功登录并导航到终端列表")
            return True
            
        except Exception as e:
            print(f"❌ 登录或导航失败: {e}")
            return False
    
    def select_terminals_using_element_ui(self):
        """使用Element UI的方法选择终端"""
        try:
            print("\n🖱️ 使用Element UI方法选择终端...")
            
            # 方法1: 点击可见的el-checkbox组件
            print("\n方法1: 点击可见的el-checkbox组件")
            
            # 查找表格行中的复选框
            checkbox_selectors = [
                "//div[contains(@class, 'el-checkbox') and not(contains(@class, 'is-disabled'))]",
                "//span[contains(@class, 'el-checkbox__inner')]",
                "//label[contains(@class, 'el-checkbox')]"
            ]
            
            success_count = 0
            
            for selector in checkbox_selectors:
                try:
                    checkboxes = self.driver.find_elements(By.XPATH, selector)
                    print(f"  选择器 '{selector}' 找到 {len(checkboxes)} 个元素")
                    
                    for i, checkbox in enumerate(checkboxes[:3]):  # 只选择前3个
                        try:
                            if checkbox.is_displayed() and checkbox.is_enabled():
                                print(f"    尝试点击复选框 {i+1}")
                                
                                # 滚动到元素可见
                                self.driver.execute_script("arguments[0].scrollIntoView(true);", checkbox)
                                time.sleep(1)
                                
                                # 点击复选框
                                self.driver.execute_script("arguments[0].click();", checkbox)
                                time.sleep(1)
                                
                                success_count += 1
                                print(f"    ✅ 成功点击复选框 {i+1}")
                                
                                # 截图记录
                                self.take_screenshot(f"checkbox_selected_{success_count}", f"选中第{success_count}个复选框")
                                
                        except Exception as e:
                            print(f"    ❌ 点击复选框 {i+1} 失败: {e}")
                            
                    if success_count > 0:
                        break  # 如果有成功的，就不尝试其他选择器了
                        
                except Exception as e:
                    print(f"  选择器失败: {e}")
            
            # 方法2: 使用Vue组件的toggleRowSelection方法
            if success_count == 0:
                print("\n方法2: 使用Vue组件方法")
                
                js_result = self.driver.execute_script("""
                    try {
                        // 查找表格的Vue实例
                        var tables = document.querySelectorAll('.el-table');
                        var results = [];
                        
                        for (var i = 0; i < tables.length; i++) {
                            var table = tables[i];
                            if (table.__vue__) {
                                var vue = table.__vue__;
                                
                                // 获取表格数据
                                var data = vue.data || vue.tableData || [];
                                results.push('表格' + (i+1) + '有' + data.length + '行数据');
                                
                                // 尝试选择第一行
                                if (data.length > 0 && vue.toggleRowSelection) {
                                    vue.toggleRowSelection(data[0], true);
                                    results.push('已选择第一行');
                                }
                            }
                        }
                        
                        return results;
                    } catch (e) {
                        return ['JavaScript执行失败: ' + e.message];
                    }
                """)
                
                print(f"  Vue方法执行结果: {js_result}")
                
                if js_result and '已选择第一行' in str(js_result):
                    success_count += 1
                    self.take_screenshot("vue_method_selection", "使用Vue方法选择")
            
            # 方法3: 直接操作隐藏的input复选框
            if success_count == 0:
                print("\n方法3: 直接操作隐藏的input复选框")
                
                hidden_checkboxes = self.driver.find_elements(By.XPATH, "//input[@type='checkbox']")
                print(f"  找到 {len(hidden_checkboxes)} 个隐藏复选框")
                
                for i, checkbox in enumerate(hidden_checkboxes[:3]):
                    try:
                        # 强制显示并点击
                        self.driver.execute_script("""
                            arguments[0].style.opacity = '1';
                            arguments[0].style.position = 'static';
                            arguments[0].style.visibility = 'visible';
                            arguments[0].checked = true;
                            arguments[0].dispatchEvent(new Event('change', { bubbles: true }));
                        """, checkbox)
                        
                        success_count += 1
                        print(f"    ✅ 成功操作隐藏复选框 {i+1}")
                        
                        self.take_screenshot(f"hidden_checkbox_{i+1}", f"操作隐藏复选框{i+1}")
                        
                    except Exception as e:
                        print(f"    ❌ 操作隐藏复选框 {i+1} 失败: {e}")
            
            print(f"\n📊 总共成功选择了 {success_count} 个终端")
            
            # 最终截图
            self.take_screenshot("final_selection_result", "最终选择结果")
            
            return success_count > 0
            
        except Exception as e:
            print(f"❌ 选择终端失败: {e}")
            return False
    
    def run_solution(self):
        """运行解决方案"""
        print("🎯 最终复选框解决方案启动")
        print("=" * 50)
        
        if not self.setup_driver():
            return False
        
        try:
            if not self.login_and_navigate():
                return False
            
            if not self.select_terminals_using_element_ui():
                print("❌ 所有选择方法都失败了")
                return False
            
            print("\n🎉 解决方案执行成功！")
            
            # 保持浏览器打开以便观察结果
            print("浏览器将在20秒后关闭，请观察选择结果...")
            time.sleep(20)
            
            return True
            
        except Exception as e:
            print(f"❌ 解决方案执行失败: {e}")
            return False
        finally:
            if self.driver:
                self.driver.quit()
                print("🔚 浏览器已关闭")

def main():
    """主函数"""
    solution = FinalCheckboxSolution()
    success = solution.run_solution()
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())
