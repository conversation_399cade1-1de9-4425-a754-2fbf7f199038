# AR勒索诱捕测试

## 测试信息
```yaml
test_name: "AR勒索诱捕功能测试"
test_description: "测试AR客户端勒索诱捕功能的检测和处置能力"
test_timeout: 300
expected_result: "勒索诱捕功能成功检测勒索行为并按策略处置"
test_environment: "Windows/Linux客户端"
test_priority: "P0"
test_category: "客户端功能测试"
```

## 前置条件
- 准备一份真实勒索病毒样本（最好是漏报的）

## 测试步骤

### 步骤1：开启勒索诱捕功能
**操作描述**：在控制中心或客户端开启勒索诱捕功能

**目标图片**：
![开启勒索诱捕功能](images/enable_ransomware_honeypot.png)

**操作类型**：配置修改
**等待时间**：5秒
**预期结果**：勒索诱捕功能成功开启

---

### 步骤2：检查诱捕文件生成
**操作描述**：检查指定路径下是否生成了诱捕文件（路径详见安装目录下的tagfile_cfg.json）

**目标图片**：
![检查诱捕文件](images/check_honeypot_files.png)

**操作类型**：验证
**等待时间**：10秒
**预期结果**：自动生成了诱捕文件

---

### 步骤3：查看tagfile_cfg.json配置
**操作描述**：打开安装目录下的tagfile_cfg.json文件，查看诱捕文件配置

**目标图片**：
![查看诱捕配置文件](images/view_tagfile_config.png)

**操作类型**：文件查看
**等待时间**：3秒
**预期结果**：成功查看诱捕文件配置信息

---

### 步骤4：执行真实勒索病毒
**操作描述**：运行准备好的真实勒索病毒样本

**目标图片**：
![执行勒索病毒](images/execute_real_ransomware.png)

**操作类型**：程序执行
**等待时间**：60秒
**预期结果**：勒索病毒开始执行

---

### 步骤5：等待诱捕文件被加密
**操作描述**：等待勒索病毒加密到诱捕文件

**目标图片**：
![诱捕文件被加密](images/honeypot_files_encrypted.png)

**操作类型**：等待
**等待时间**：120秒
**预期结果**：诱捕文件被勒索病毒加密

---

### 步骤6：验证策略处置（仅记录）
**操作描述**：如果策略设置为"仅记录"，验证是否只记录不处置

**目标图片**：
![仅记录处置](images/log_only_action.png)

**操作类型**：验证
**等待时间**：10秒
**预期结果**：按策略中的处置方式处置（仅记录）

---

### 步骤7：验证策略处置（杀进程）
**操作描述**：如果策略设置为"杀进程"，验证是否成功终止勒索进程

**目标图片**：
![杀进程处置](images/kill_process_action.png)

**操作类型**：验证
**等待时间**：10秒
**预期结果**：按策略中的处置方式处置（杀进程）

---

### 步骤8：验证策略处置（杀进程+删文件）
**操作描述**：如果策略设置为"杀进程+删文件"，验证是否成功终止进程并删除文件

**目标图片**：
![杀进程删文件处置](images/kill_and_delete_action.png)

**操作类型**：验证
**等待时间**：10秒
**预期结果**：按策略中的处置方式处置（杀进程+删文件）

---

### 步骤9：检查告警日志
**操作描述**：查看客户端和控制中心的勒索诱捕告警日志

**目标图片**：
![勒索诱捕告警日志](images/honeypot_alert_logs.png)

**操作类型**：验证
**等待时间**：5秒
**预期结果**：触发告警日志

---

### 步骤10：关闭勒索诱捕功能
**操作描述**：在控制中心关闭勒索诱捕功能

**目标图片**：
![关闭勒索诱捕功能](images/disable_ransomware_honeypot.png)

**操作类型**：配置修改
**等待时间**：5秒
**预期结果**：勒索诱捕功能成功关闭

---

### 步骤11：验证诱捕文件清理
**操作描述**：检查诱饵文件、文件夹是否被自动删除

**目标图片**：
![诱捕文件清理](images/honeypot_files_cleanup.png)

**操作类型**：验证
**等待时间**：10秒
**预期结果**：诱饵文件、文件夹被自动删除

---

### 步骤12：截图保存测试结果
**操作描述**：对测试过程和结果进行截图保存

**操作类型**：截图
**保存文件名**：ar_ransomware_honeypot_test_{timestamp}.png
**预期结果**：测试结果截图保存成功

## 测试验证点
```yaml
verification_points:
  - name: "勒索诱捕功能开启成功"
    description: "勒索诱捕功能能够正常开启"
    image: "images/enable_ransomware_honeypot.png"
    critical: true
  
  - name: "诱捕文件自动生成"
    description: "开启功能后自动生成诱捕文件"
    image: "images/check_honeypot_files.png"
    critical: true
  
  - name: "勒索病毒触发诱捕"
    description: "真实勒索病毒成功触发诱捕机制"
    image: "images/honeypot_files_encrypted.png"
    critical: true
    
  - name: "策略处置正确执行"
    description: "按照设定的策略正确处置勒索行为"
    critical: true
    
  - name: "告警日志正确记录"
    description: "勒索诱捕事件被正确记录到日志"
    image: "images/honeypot_alert_logs.png"
    critical: true
    
  - name: "诱捕文件自动清理"
    description: "关闭功能后诱捕文件被自动清理"
    image: "images/honeypot_files_cleanup.png"
    critical: true
```

## 测试数据
```yaml
test_data:
  honeypot_config:
    config_file: "tagfile_cfg.json"
    install_directory: "/opt/apps/mredrclient/"  # Linux
    install_directory_windows: "C:\\Program Files (x86)\\mredrclient\\"  # Windows
    
  policy_actions:
    - "仅记录"
    - "杀进程"
    - "杀进程+删文件"
    
  ransomware_sample:
    type: "真实勒索病毒样本"
    requirement: "最好是漏报的样本"
    
  expected_log_fields:
    - "检测时间"
    - "勒索行为类型"
    - "诱捕文件路径"
    - "勒索进程信息"
    - "处置动作"
    - "处置结果"
```
