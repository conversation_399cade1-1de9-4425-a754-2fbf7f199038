# AR客户端升级任务测试

## 测试信息
```yaml
test_name: "AR控制中心下发客户端升级任务测试"
test_description: "测试从控制中心向客户端下发升级任务"
test_timeout: 600
expected_result: "升级任务成功下发并执行，客户端升级到最新版本"
test_environment: "Windows/Linux控制中心和客户端"
test_priority: "P0"
test_category: "控制中心/终端列表"
```

## 前置条件
- 已安装客户端
- 有可升级的版本

## 测试步骤

### 步骤1：登录控制中心
**操作描述**：使用浏览器登录AR控制中心

**目标图片**：
![登录控制中心](images/login_center_upgrade_task.png)

**操作类型**：登录
**等待时间**：5秒
**预期结果**：成功登录控制中心

---

### 步骤2：进入终端列表
**操作描述**：进入控制中心-终端管理-终端列表

**目标图片**：
![进入终端列表](images/enter_terminal_list_upgrade.png)

**操作类型**：导航
**等待时间**：3秒
**预期结果**：成功进入终端列表页面

---

### 步骤3：识别需要升级的客户端
**操作描述**：查看终端列表，识别需要升级的客户端（红色版本号）

**目标图片**：
![识别需升级客户端](images/identify_upgrade_needed_clients.png)

**操作类型**：查看
**等待时间**：5秒
**预期结果**：能够识别出需要升级的客户端

---

### 步骤4：选择需要升级的客户端
**操作描述**：选择需要升级的客户端

**目标图片**：
![选择升级客户端](images/select_clients_for_upgrade.png)

**操作类型**：选择
**等待时间**：3秒
**预期结果**：成功选择需要升级的客户端

---

### 步骤5：点击终端升级按钮
**操作描述**：点击"终端升级"按钮

**目标图片**：
![点击终端升级](images/click_terminal_upgrade_button.png)

**操作类型**：点击
**等待时间**：3秒
**预期结果**：弹出升级任务配置对话框

---

### 步骤6：配置升级任务
**操作描述**：选择终端升级和病毒库升级选项

**目标图片**：
![配置升级任务](images/configure_upgrade_task.png)

**操作类型**：配置
**等待时间**：5秒
**预期结果**：可选择终端升级和病毒库升级

---

### 步骤7：下发升级任务
**操作描述**：点击确认按钮下发升级任务

**目标图片**：
![下发升级任务](images/distribute_upgrade_task.png)

**操作类型**：点击确认
**等待时间**：5秒
**预期结果**：升级任务成功下发

---

### 步骤8：查看升级任务状态
**操作描述**：进入终端管理-升级任务，查看任务状态

**目标图片**：
![查看升级任务状态](images/view_upgrade_task_status.png)

**操作类型**：查看
**等待时间**：10秒
**预期结果**：选择需要升级的客户端，下发终端升级任务

---

### 步骤9：监控升级进度
**操作描述**：监控升级任务的执行进度

**目标图片**：
![监控升级进度](images/monitor_upgrade_progress.png)

**操作类型**：监控
**等待时间**：300秒
**预期结果**：能够看到升级任务执行进度

---

### 步骤10：等待升级完成
**操作描述**：等待升级任务执行完成

**目标图片**：
![升级任务完成](images/upgrade_task_completed.png)

**操作类型**：等待
**等待时间**：300秒
**预期结果**：升级完成之后为最新版本

---

### 步骤11：验证版本更新
**操作描述**：在终端列表中验证客户端版本是否更新

**目标图片**：
![验证版本更新](images/verify_version_update.png)

**操作类型**：验证
**等待时间**：10秒
**预期结果**：客户端版本显示为最新版本（黑色字体）

---

### 步骤12：检查升级日志
**操作描述**：查看事件日志-终端升级日志中的升级记录

**目标图片**：
![检查升级日志](images/check_upgrade_logs.png)

**操作类型**：查看
**等待时间**：10秒
**预期结果**：升级日志正确记录升级过程和结果

---

### 步骤13：验证客户端功能
**操作描述**：验证升级后客户端功能是否正常

**目标图片**：
![验证客户端功能](images/verify_client_functionality.png)

**操作类型**：验证
**等待时间**：30秒
**预期结果**：升级后客户端功能正常工作

---

### 步骤14：截图保存测试结果
**操作描述**：对升级任务过程和结果进行截图保存

**操作类型**：截图
**保存文件名**：ar_client_upgrade_task_test_{timestamp}.png
**预期结果**：测试结果截图保存成功

## 测试验证点
```yaml
verification_points:
  - name: "识别需升级客户端"
    description: "能够正确识别需要升级的客户端"
    image: "images/identify_upgrade_needed_clients.png"
    critical: true
  
  - name: "升级任务创建成功"
    description: "能够成功创建和配置升级任务"
    image: "images/configure_upgrade_task.png"
    critical: true
  
  - name: "任务下发成功"
    description: "升级任务能够成功下发到目标客户端"
    image: "images/distribute_upgrade_task.png"
    critical: true
    
  - name: "升级进度可监控"
    description: "能够监控升级任务的执行进度"
    image: "images/monitor_upgrade_progress.png"
    critical: true
    
  - name: "版本成功更新"
    description: "客户端版本成功更新到最新版本"
    image: "images/verify_version_update.png"
    critical: true
    
  - name: "升级日志正确记录"
    description: "升级过程正确记录到日志"
    image: "images/check_upgrade_logs.png"
    critical: true
    
  - name: "升级后功能正常"
    description: "升级后客户端功能正常工作"
    image: "images/verify_client_functionality.png"
    critical: true
```

## 测试数据
```yaml
test_data:
  upgrade_types:
    - "终端升级"
    - "病毒库升级"
    - "终端升级+病毒库升级"
    
  version_indicators:
    latest_version: "黑色字体"
    need_upgrade: "红色字体"
    
  task_status_values:
    - "待执行"
    - "下载中"
    - "安装中"
    - "已完成"
    - "执行失败"
    
  expected_log_fields:
    - "升级时间"
    - "任务序号"
    - "终端别名\\IP"
    - "终端分组"
    - "升级类型"
    - "升级前版本"
    - "升级后版本"
    - "升级结果"
    
  upgrade_components:
    client_software:
      windows: "mredrclient-x.x.x.x.x86.msi"
      linux: "mredrclient_x.x.x.xxx_amd64.rpm/deb"
    virus_database:
      version_format: "YYYYMMDD.HH"
      
  verification_items:
    - "客户端版本号更新"
    - "病毒库版本更新"
    - "客户端服务正常运行"
    - "防护功能正常工作"
    - "与控制中心通信正常"
```
