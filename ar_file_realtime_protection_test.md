# AR文件实时防护测试

## 测试信息
```yaml
test_name: "AR文件实时防护功能测试"
test_description: "测试AR客户端文件实时防护功能的检测和处理能力"
test_timeout: 300
expected_result: "文件实时防护成功检测并处理恶意文件"
test_environment: "Windows/Linux客户端"
test_priority: "P0"
test_category: "客户端功能测试"
```

## 前置条件
- 已开启文件实时防护策略
- 准备几份黑客工具样本（注意每个样本大小不要超过16M）

## 测试步骤

### 步骤1：启动勒索软件模拟器
**操作描述**：双击桌面上的勒索软件模拟器，启动恶意文件测试程序

**目标图片**：
![勒索软件模拟器图标](images/ransomware_simulator_icon.png)

**操作类型**：点击
**等待时间**：3秒
**预期结果**：程序成功启动，显示主界面

---

### 步骤2：选择测试文件目录
**操作描述**：在程序界面中选择要测试的文件目录，通常选择用户文档目录

**目标图片**：
![选择目录按钮](images/select_directory_button.png)

**操作类型**：点击
**等待时间**：2秒
**预期结果**：弹出文件夹选择对话框

---

### 步骤3：开始文件加密模拟
**操作描述**：点击"开始测试"按钮，启动文件加密行为模拟

**目标图片**：
![开始测试按钮](images/start_test_button.png)

**操作类型**：点击
**等待时间**：2秒
**预期结果**：程序开始模拟恶意文件加密行为

---

### 步骤4：等待AR实时保护触发
**操作描述**：等待AR安全软件的文件实时保护功能检测到恶意行为并触发警报

**目标图片**：
![AR实时保护警报](images/ar_realtime_protection_alert.png)

**操作类型**：等待
**等待时间**：30秒
**预期结果**：AR软件弹出实时保护警报窗口

---

### 步骤5：验证恶意行为被阻止
**操作描述**：确认AR软件显示"恶意文件操作已被阻止"的提示信息

**目标图片**：
![恶意行为已阻止提示](images/malicious_behavior_blocked.png)

**操作类型**：验证
**等待时间**：5秒
**预期结果**：显示恶意行为被成功阻止的消息

---

### 步骤6：检查安全日志
**操作描述**：打开AR安全软件的安全日志，查看实时保护记录

**目标图片**：
![安全日志按钮](images/security_log_button.png)

**操作类型**：点击
**等待时间**：3秒
**预期结果**：安全日志中记录了本次检测事件

---

### 步骤7：截图保存测试结果
**操作描述**：对当前AR安全日志界面进行截图，保存测试证据

**操作类型**：截图
**保存文件名**：ar_realtime_protection_test_{timestamp}.png
**预期结果**：截图文件保存成功

## 测试验证点
```yaml
verification_points:
  - name: "实时保护触发"
    description: "AR软件是否及时检测到恶意文件操作"
    image: "images/ar_realtime_protection_alert.png"
    critical: true
  
  - name: "恶意行为阻止"
    description: "AR软件是否成功阻止了恶意文件操作"
    image: "images/malicious_behavior_blocked.png"
    critical: true
  
  - name: "安全日志记录"
    description: "安全日志是否正确记录了检测事件"
    image: "images/security_log_entry.png"
    critical: true
```

## 测试数据
```yaml
test_data:
  target_files:
    - "C:\\Users\\<USER>\\Documents\\test_document.txt"
    - "C:\\Users\\<USER>\\Pictures\\test_image.jpg"
    - "C:\\Users\\<USER>\\Desktop\\important_file.pdf"
  
  ar_software:
    name: "AR安全软件"
    version: "最新版本"
    
  system_info:
    os: "Windows 11"
    architecture: "x64"
    
  expected_behaviors:
    - "文件实时保护警报触发"
    - "恶意操作被阻止"
    - "安全日志正确记录时间、操作类型、文件路径等信息"
```
