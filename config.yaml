# AR扫描任务自动化测试配置文件

# 系统配置
system:
  url: "http://172.16.103.202:8088"
  username: "admin"
  password: "Admin.2022"
  timeout: 10

# 浏览器配置
browser:
  # Chrome驱动路径（如果为空则使用系统PATH中的chromedriver）
  chrome_driver_path: ""
  # 浏览器选项
  options:
    - "--start-maximized"
    - "--disable-blink-features=AutomationControlled"
  # 是否显示浏览器界面（false为无头模式）
  show_browser: true

# 测试配置
test:
  # 步骤间等待时间（秒）
  step_delay: 2
  # 页面加载等待时间（秒）
  page_load_timeout: 10
  # 截图保存目录
  screenshot_dir: "screenshots"
  # 测试报告保存目录
  report_dir: "reports"

# 扫描任务配置
scan_task:
  # 扫描类型：快速扫描、全盘扫描、自定义扫描
  scan_type: "快速扫描"
  # 任务名称前缀
  task_name_prefix: "自动化测试_"
  # 预期的终端信息
  expected_terminals:
    - name: "DELL2024"
      ip: "*************"
    - name: "DESKTOP-JFACIG"
      ip: "***************"

# 验证配置
verification:
  # 需要验证的任务详情字段
  required_fields:
    - "终端名称"
    - "扫描类型"
    - "任务状态"
    - "IP地址"
  # 预期的任务状态
  expected_status: ["已下发", "执行中", "已完成"]

# 日志配置
logging:
  level: "INFO"
  format: "%(asctime)s - %(levelname)s - %(message)s"
  file: "ar_scan_test.log"
