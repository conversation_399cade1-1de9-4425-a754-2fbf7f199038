# AR Windows客户端安装测试

## 测试信息
```yaml
test_name: "AR Windows客户端手动安装测试"
test_description: "在Windows系统上手动安装AR客户端"
test_timeout: 300
expected_result: "AR客户端安装成功并在控制中心显示在线"
test_environment: "Windows生产机"
test_priority: "P0"
test_category: "客户端/安装卸载"
```

## 前置条件
- 已上传客户端安装包于Windows系统生产机上(例如安装包为:mredrclient-********.x86.msi)

## 测试步骤

### 步骤1：打开命令提示符
**操作描述**：在生产机上调起cmd命令窗口

**目标图片**：
![CMD命令窗口](images/cmd_window.png)

**操作类型**：点击
**等待时间**：3秒
**预期结果**：CMD命令窗口成功打开

---

### 步骤2：执行安装命令
**操作描述**：执行命令:msiexec.exe /i mredrclient-********.x86.msi的存放路径 ServerIp=一体机控制中心IP地址

**目标图片**：
![执行安装命令](images/msiexec_install_command.png)

**操作类型**：命令执行
**等待时间**：60秒
**预期结果**：安装命令执行完成

---

### 步骤3：验证控制中心终端列表
**操作描述**：检查AR控制中心终端列表，确认该主机已出现

**目标图片**：
![控制中心终端列表](images/terminal_list_new_client.png)

**操作类型**：验证
**等待时间**：30秒
**预期结果**：安装成功后，该主机出现在AR控制中心终端列表中

---

### 步骤4：检查安装目录
**操作描述**：验证Windows系统中是否存在C:\Program Files (x86)\mredrclient文件夹

**目标图片**：
![客户端安装目录](images/client_install_directory.png)

**操作类型**：验证
**等待时间**：5秒
**预期结果**：Windows存在C:\Program Files (x86)\mredrclient文件夹

---

### 步骤5：检查系统服务
**操作描述**：打开服务管理器，检查mr_mainsvc服务状态

**目标图片**：
![系统服务状态](images/mr_mainsvc_service.png)

**操作类型**：验证
**等待时间**：5秒
**预期结果**：Windows有mr_mainsvc服务并且处于运行状态

---

### 步骤6：检查AR相关进程
**操作描述**：打开任务管理器，检查是否存在AR相关的进程

**目标图片**：
![AR相关进程](images/ar_processes.png)

**操作类型**：验证
**等待时间**：5秒
**预期结果**：存在AR相关的进程

---

### 步骤7：截图保存测试结果
**操作描述**：对安装完成后的状态进行截图保存

**操作类型**：截图
**保存文件名**：ar_windows_client_install_test_{timestamp}.png
**预期结果**：测试结果截图保存成功

## 测试验证点
```yaml
verification_points:
  - name: "安装命令执行成功"
    description: "msiexec安装命令正常执行完成"
    critical: true
  
  - name: "控制中心显示新终端"
    description: "新安装的客户端在控制中心终端列表中显示"
    image: "images/terminal_list_new_client.png"
    critical: true
  
  - name: "安装目录创建成功"
    description: "客户端安装目录正确创建"
    image: "images/client_install_directory.png"
    critical: true
    
  - name: "系统服务正常运行"
    description: "mr_mainsvc服务处于运行状态"
    image: "images/mr_mainsvc_service.png"
    critical: true
    
  - name: "AR进程正常启动"
    description: "AR相关进程正常运行"
    image: "images/ar_processes.png"
    critical: true
```

## 测试数据
```yaml
test_data:
  client_package:
    filename: "mredrclient-********.x86.msi"
    install_path: "C:\\Program Files (x86)\\mredrclient"
    
  ar_center:
    ip: "***********"  # 示例IP，实际使用时需要替换
    
  system_service:
    service_name: "mr_mainsvc"
    expected_status: "运行中"
    
  install_command: "msiexec.exe /i E:\\mredrclient-********.x86.msi ServerIp=***********"
```
