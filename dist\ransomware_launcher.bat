@echo off
chcp 65001 > nul
title <PERSON><PERSON><PERSON><PERSON><PERSON> SIMULATION - AR Security Test

color 0C
echo.
echo ████████████████████████████████████████████████████████████████████
echo ██                                                                ██
echo ██           <PERSON><PERSON><PERSON><PERSON>RE BEHAVIOR SIMULATION PROGRAM               ██
echo ██                                                                ██
echo ████████████████████████████████████████████████████████████████████
echo.
color 0F
echo ⚠️  WARNING: This is a test program for AR security software
echo 📋 FUNCTION: Simulate ransomware encryption and ransom note generation
echo 🎯 PURPOSE: Verify AR security software's ransomware detection capabilities
echo.
echo This program will:
echo • Search for existing files in user directories
echo • Create additional test files in multiple locations
echo • Simulate file encryption (rename with .encrypted extension)
echo • Generate realistic ransom notes in English
echo • Place ransom notes in every affected directory
echo • Create a main ransom note on desktop
echo.
echo ============================================================
echo                    SIMULATION OPTIONS
echo ============================================================
echo.
echo 1. Light Test    (2 files per directory, encrypt existing files)
echo 2. Standard Test (3 files per directory, encrypt existing files)
echo 3. Heavy Test    (5 files per directory, encrypt existing files)
echo 4. Safe Test     (3 files per directory, NO existing files)
echo 5. Custom Test   (specify parameters)
echo 6. Preserve Mode (don't auto-cleanup files)
echo 7. Exit
echo.

set /p choice=Select test mode (1-7): 

if "%choice%"=="1" (
    echo.
    echo 🚀 Starting Light Test Mode...
    echo • Files per directory: 2
    echo • Encrypt existing files: YES
    echo • Auto cleanup: YES
    echo.
    ransomware_simulator.exe --files-per-dir 2 --encrypt-existing
) else if "%choice%"=="2" (
    echo.
    echo 🚀 Starting Standard Test Mode...
    echo • Files per directory: 3
    echo • Encrypt existing files: YES
    echo • Auto cleanup: YES
    echo.
    ransomware_simulator.exe --files-per-dir 3 --encrypt-existing
) else if "%choice%"=="3" (
    echo.
    echo 🚀 Starting Heavy Test Mode...
    echo • Files per directory: 5
    echo • Encrypt existing files: YES
    echo • Auto cleanup: YES
    echo.
    ransomware_simulator.exe --files-per-dir 5 --encrypt-existing
) else if "%choice%"=="4" (
    echo.
    echo 🚀 Starting Safe Test Mode...
    echo • Files per directory: 3
    echo • Encrypt existing files: NO
    echo • Auto cleanup: YES
    echo.
    ransomware_simulator.exe --files-per-dir 3 --no-encrypt-existing
) else if "%choice%"=="5" (
    echo.
    set /p filecount=Enter files per directory (1-10): 
    echo.
    echo Choose existing file handling:
    echo 1. Encrypt existing files (more realistic)
    echo 2. Only create new files (safer)
    set /p existchoice=Enter choice (1-2): 
    
    if "%existchoice%"=="1" (
        echo.
        echo 🚀 Starting Custom Test Mode...
        echo • Files per directory: %filecount%
        echo • Encrypt existing files: YES
        echo • Auto cleanup: YES
        echo.
        ransomware_simulator.exe --files-per-dir %filecount% --encrypt-existing
    ) else (
        echo.
        echo 🚀 Starting Custom Test Mode...
        echo • Files per directory: %filecount%
        echo • Encrypt existing files: NO
        echo • Auto cleanup: YES
        echo.
        ransomware_simulator.exe --files-per-dir %filecount% --no-encrypt-existing
    )
) else if "%choice%"=="6" (
    echo.
    echo 🚀 Starting Preserve Mode...
    echo • Files per directory: 3
    echo • Encrypt existing files: YES
    echo • Auto cleanup: NO (files will be preserved)
    echo.
    ransomware_simulator.exe --files-per-dir 3 --encrypt-existing --no-cleanup
) else if "%choice%"=="7" (
    exit
) else (
    echo.
    echo ❌ Invalid selection, please restart the program
)

echo.
echo ============================================================
echo                    POST-TEST CHECKLIST
echo ============================================================
echo.
echo Please verify the following AR security features:
echo.
echo ✓ 1. Ransomware Detection
echo    - Check if AR control center shows ransomware alerts
echo    - Verify detection time and accuracy
echo.
echo ✓ 2. File Protection
echo    - Check if file real-time protection triggered
echo    - Verify if encryption was blocked or allowed
echo.
echo ✓ 3. Ransom Trap (Honeypot)
echo    - Check if ransom trap files detected the attack
echo    - Verify if malicious process was terminated
echo.
echo ✓ 4. Behavioral Analysis
echo    - Check if suspicious file operations were logged
echo    - Verify if behavioral patterns were detected
echo.
echo ✓ 5. Response Actions
echo    - Check if configured response policies executed
echo    - Verify if alerts were sent to administrators
echo.
echo ============================================================
echo.
echo 📝 Check these locations for ransom notes:
echo    • Desktop: !!! READ_ME_DECRYPT !!!.txt
echo    • Each affected directory: README_DECRYPT.txt
echo.
echo 🔍 Check these locations for encrypted files:
echo    • User directories (Documents, Desktop, Downloads, etc.)
echo    • Current working directory
echo    • Temporary directories
echo.
echo ============================================================
pause
