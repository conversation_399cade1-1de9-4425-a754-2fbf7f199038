@echo off
chcp 65001 > nul
title AR安全软件病毒模拟程序

echo ============================================================
echo                AR安全软件病毒模拟程序
echo ============================================================
echo.
echo 请选择模拟类型:
echo 1. 文件感染模拟 (file_infection)
echo 2. 勒索软件模拟 (ransomware)  
echo 3. 注册表修改模拟 (registry)
echo 4. 网络活动模拟 (network)
echo 5. 进程注入模拟 (injection)
echo 6. 完整测试 (all)
echo 7. 退出
echo.

set /p choice=请输入选择 (1-7): 

if "%choice%"=="1" (
    virus_simulator.exe --type file_infection
) else if "%choice%"=="2" (
    virus_simulator.exe --type ransomware
) else if "%choice%"=="3" (
    virus_simulator.exe --type registry
) else if "%choice%"=="4" (
    virus_simulator.exe --type network
) else if "%choice%"=="5" (
    virus_simulator.exe --type injection
) else if "%choice%"=="6" (
    virus_simulator.exe --type all
) else if "%choice%"=="7" (
    exit
) else (
    echo 无效选择，请重新运行程序
)

echo.
pause
