# AR勒索诱捕日志测试

## 测试信息
```yaml
test_name: "AR控制中心勒索诱捕日志检查测试"
test_description: "检查控制中心勒索诱捕日志的显示和功能"
test_timeout: 180
expected_result: "勒索诱捕日志正确显示诱捕事件信息"
test_environment: "Windows/Linux控制中心"
test_priority: "P1"
test_category: "事件日志/勒索诱捕"
```

## 前置条件
- 触发过勒索诱捕告警

## 测试步骤

### 步骤1：登录控制中心
**操作描述**：使用管理员账号登录AR控制中心

**目标图片**：
![登录控制中心](images/login_center_honeypot_log.png)

**操作类型**：登录
**等待时间**：5秒
**预期结果**：成功登录控制中心

---

### 步骤2：进入事件日志
**操作描述**：进入控制中心-事件日志菜单

**目标图片**：
![进入事件日志](images/enter_event_logs_honeypot.png)

**操作类型**：导航
**等待时间**：3秒
**预期结果**：成功进入事件日志页面

---

### 步骤3：点击勒索诱捕日志
**操作描述**：点击事件日志-勒索诱捕

**目标图片**：
![点击勒索诱捕日志](images/click_ransomware_honeypot_logs.png)

**操作类型**：点击
**等待时间**：3秒
**预期结果**：进入勒索诱捕日志页面

---

### 步骤4：查看勒索诱捕日志列表
**操作描述**：查看勒索诱捕日志的列表显示

**目标图片**：
![勒索诱捕日志列表](images/ransomware_honeypot_log_list.png)

**操作类型**：查看
**等待时间**：5秒
**预期结果**：有对应的勒索诱捕的日志

---

### 步骤5：查看日志详细信息
**操作描述**：点击某条日志，查看勒索诱捕日志的具体信息

**目标图片**：
![勒索诱捕日志详情](images/ransomware_honeypot_log_details.png)

**操作类型**：查看详情
**等待时间**：5秒
**预期结果**：勒索诱捕日志有正确的检测时间、检测机器的别名与ip、有对应的行为类型、病毒路径、操作进程、检出动作、处理结果、进程哈希

---

### 步骤6：验证检测时间显示
**操作描述**：验证日志中检测时间的显示是否正确

**目标图片**：
![验证检测时间](images/verify_detection_time_honeypot.png)

**操作类型**：验证
**等待时间**：3秒
**预期结果**：检测时间显示正确

---

### 步骤7：验证终端信息显示
**操作描述**：验证日志中终端别名和IP地址显示是否正确

**目标图片**：
![验证终端信息](images/verify_terminal_info_honeypot.png)

**操作类型**：验证
**等待时间**：3秒
**预期结果**：检测机器的别名与IP显示正确

---

### 步骤8：验证诱捕行为类型
**操作描述**：验证日志中诱捕行为类型显示是否正确

**目标图片**：
![验证诱捕行为类型](images/verify_honeypot_behavior_type.png)

**操作类型**：验证
**等待时间**：3秒
**预期结果**：有对应的诱捕行为类型显示正确

---

### 步骤9：验证诱捕文件路径
**操作描述**：验证日志中被加密的诱捕文件路径显示是否正确

**目标图片**：
![验证诱捕文件路径](images/verify_honeypot_file_path.png)

**操作类型**：验证
**等待时间**：3秒
**预期结果**：诱捕文件路径显示正确

---

### 步骤10：验证勒索进程信息
**操作描述**：验证日志中勒索进程信息显示是否正确

**目标图片**：
![验证勒索进程](images/verify_ransomware_process.png)

**操作类型**：验证
**等待时间**：3秒
**预期结果**：勒索进程信息显示正确

---

### 步骤11：验证检出动作和处理结果
**操作描述**：验证日志中检出动作和处理结果显示是否正确

**目标图片**：
![验证检出动作](images/verify_detection_action_honeypot.png)

**操作类型**：验证
**等待时间**：3秒
**预期结果**：检出动作、处理结果显示正确

---

### 步骤12：验证进程哈希信息
**操作描述**：验证日志中勒索进程哈希值显示是否正确

**目标图片**：
![验证进程哈希](images/verify_process_hash_honeypot.png)

**操作类型**：验证
**等待时间**：3秒
**预期结果**：进程哈希值显示正确

---

### 步骤13：测试日志筛选功能
**操作描述**：使用时间、终端、处理结果等条件筛选勒索诱捕日志

**目标图片**：
![勒索诱捕日志筛选](images/honeypot_log_filter.png)

**操作类型**：筛选
**等待时间**：5秒
**预期结果**：筛选功能正常工作

---

### 步骤14：截图保存测试结果
**操作描述**：对勒索诱捕日志测试进行截图保存

**操作类型**：截图
**保存文件名**：ar_ransomware_honeypot_log_test_{timestamp}.png
**预期结果**：测试结果截图保存成功

## 测试验证点
```yaml
verification_points:
  - name: "勒索诱捕日志页面正常"
    description: "勒索诱捕日志页面能够正常加载"
    image: "images/click_ransomware_honeypot_logs.png"
    critical: true
  
  - name: "诱捕记录正确显示"
    description: "有对应的勒索诱捕的日志"
    image: "images/ransomware_honeypot_log_list.png"
    critical: true
  
  - name: "日志详情信息完整"
    description: "日志详情显示完整的诱捕信息"
    image: "images/ransomware_honeypot_log_details.png"
    critical: true
    
  - name: "检测时间显示正确"
    description: "检测时间信息显示正确"
    image: "images/verify_detection_time_honeypot.png"
    critical: true
    
  - name: "终端信息显示正确"
    description: "终端别名和IP信息显示正确"
    image: "images/verify_terminal_info_honeypot.png"
    critical: true
    
  - name: "诱捕行为类型正确"
    description: "诱捕行为类型信息显示正确"
    image: "images/verify_honeypot_behavior_type.png"
    critical: true
    
  - name: "诱捕文件路径正确"
    description: "诱捕文件路径信息显示正确"
    image: "images/verify_honeypot_file_path.png"
    critical: true
    
  - name: "勒索进程信息正确"
    description: "勒索进程信息显示正确"
    image: "images/verify_ransomware_process.png"
    critical: true
    
  - name: "检出动作和结果正确"
    description: "检出动作和处理结果显示正确"
    image: "images/verify_detection_action_honeypot.png"
    critical: true
    
  - name: "进程哈希信息正确"
    description: "进程哈希值信息显示正确"
    image: "images/verify_process_hash_honeypot.png"
    critical: true
```

## 测试数据
```yaml
test_data:
  log_fields:
    required_fields:
      - "检测时间"
      - "终端别名"
      - "终端IP"
      - "行为类型"
      - "诱捕文件路径"
      - "勒索进程"
      - "检出动作"
      - "处理结果"
      - "进程哈希"
    
    optional_fields:
      - "进程PID"
      - "用户名"
      - "加密算法"
      - "勒索信息"
      
  honeypot_behavior_types:
    - "文件加密"
    - "文件删除"
    - "文件重命名"
    - "勒索信创建"
    
  detection_actions:
    - "诱捕文件监控"
    - "文件加密检测"
    - "勒索行为识别"
    - "诱捕触发"
    
  processing_results:
    - "已阻止"
    - "已终止进程"
    - "已删除文件"
    - "已隔离"
    - "仅记录"
    
  honeypot_file_types:
    - "诱捕文档"
    - "诱捕图片"
    - "诱捕数据库"
    - "诱捕脚本"
    
  filter_options:
    time_range:
      - "今天"
      - "最近7天"
      - "最近30天"
      - "自定义时间"
    
    behavior_type:
      - "文件加密"
      - "文件删除"
      - "文件重命名"
    
    result_filter:
      - "已阻止"
      - "已终止"
      - "仅记录"
      
  sample_log_entry:
    detection_time: "2024-01-15 14:30:25"
    terminal_alias: "PC-001"
    terminal_ip: "*************"
    behavior_type: "文件加密"
    honeypot_file_path: "C:\\Users\\<USER>\\document.docx"
    ransomware_process: "malware.exe"
    detection_action: "诱捕文件监控"
    processing_result: "已终止进程"
    process_hash: "d41d8cd98f00b204e9800998ecf8427e"
```
