# AR删除离线客户端测试

## 测试信息
```yaml
test_name: "AR控制中心删除离线客户端测试"
test_description: "测试控制中心删除离线客户端的功能"
test_timeout: 180
expected_result: "离线客户端能够被正确删除，在线客户端不受影响"
test_environment: "Windows/Linux控制中心"
test_priority: "P1"
test_category: "控制中心/终端列表"
```

## 前置条件
- 存在离线的客户端

## 测试步骤

### 步骤1：登录控制中心
**操作描述**：使用管理员账号登录AR控制中心

**目标图片**：
![登录控制中心](images/login_center_delete_terminals.png)

**操作类型**：登录
**等待时间**：5秒
**预期结果**：成功登录控制中心

---

### 步骤2：进入终端列表
**操作描述**：进入控制中心-终端管理-终端列表

**目标图片**：
![进入终端列表](images/enter_terminal_list_delete.png)

**操作类型**：导航
**等待时间**：3秒
**预期结果**：成功进入终端列表页面

---

### 步骤3：识别离线客户端
**操作描述**：在终端列表中识别离线状态的客户端（灰色别名）

**目标图片**：
![识别离线客户端](images/identify_offline_terminals.png)

**操作类型**：查看
**等待时间**：5秒
**预期结果**：能够识别出离线状态的客户端

---

### 步骤4：选择离线客户端
**操作描述**：选择若干离线状态的客户端

**目标图片**：
![选择离线客户端](images/select_offline_terminals.png)

**操作类型**：选择
**等待时间**：3秒
**预期结果**：成功选择离线客户端

---

### 步骤5：选择在线客户端（对照组）
**操作描述**：同时选择一些在线状态的客户端作为对照

**目标图片**：
![选择在线客户端](images/select_online_terminals.png)

**操作类型**：选择
**等待时间**：3秒
**预期结果**：成功选择在线客户端

---

### 步骤6：点击删除按钮
**操作描述**：点击"删除"按钮

**目标图片**：
![点击删除按钮](images/click_delete_button.png)

**操作类型**：点击
**等待时间**：3秒
**预期结果**：弹出删除确认对话框

---

### 步骤7：确认删除操作
**操作描述**：在确认对话框中点击确认

**目标图片**：
![确认删除操作](images/confirm_delete_operation.png)

**操作类型**：确认
**等待时间**：5秒
**预期结果**：删除操作开始执行

---

### 步骤8：验证离线终端被删除
**操作描述**：检查选中的离线终端是否被删除

**目标图片**：
![验证离线终端删除](images/verify_offline_terminals_deleted.png)

**操作类型**：验证
**等待时间**：10秒
**预期结果**：确认后，选中的离线终端被删除

---

### 步骤9：验证在线终端未被删除
**操作描述**：检查选中的在线终端是否仍然存在

**目标图片**：
![验证在线终端保留](images/verify_online_terminals_preserved.png)

**操作类型**：验证
**等待时间**：5秒
**预期结果**：选中的在线终端会忽略（不会被删除）

---

### 步骤10：刷新终端列表
**操作描述**：刷新终端列表确认删除结果

**目标图片**：
![刷新终端列表](images/refresh_terminal_list_delete.png)

**操作类型**：刷新
**等待时间**：5秒
**预期结果**：离线终端不再显示，在线终端正常显示

---

### 步骤11：检查操作日志
**操作描述**：查看中心操作日志中的删除操作记录

**目标图片**：
![检查删除操作日志](images/check_delete_operation_log.png)

**操作类型**：查看
**等待时间**：10秒
**预期结果**：操作日志正确记录删除操作

---

### 步骤12：截图保存测试结果
**操作描述**：对删除离线客户端测试进行截图保存

**操作类型**：截图
**保存文件名**：ar_delete_offline_terminals_test_{timestamp}.png
**预期结果**：测试结果截图保存成功

## 测试验证点
```yaml
verification_points:
  - name: "正确识别离线终端"
    description: "能够正确识别离线状态的终端"
    image: "images/identify_offline_terminals.png"
    critical: true
  
  - name: "删除功能正常触发"
    description: "删除功能能够正常触发"
    image: "images/click_delete_button.png"
    critical: true
  
  - name: "离线终端正确删除"
    description: "离线终端被正确删除"
    image: "images/verify_offline_terminals_deleted.png"
    critical: true
    
  - name: "在线终端不受影响"
    description: "在线终端不受删除操作影响"
    image: "images/verify_online_terminals_preserved.png"
    critical: true
    
  - name: "操作日志正确记录"
    description: "删除操作被正确记录到操作日志"
    image: "images/check_delete_operation_log.png"
    critical: true
```

## 测试数据
```yaml
test_data:
  terminal_status_colors:
    online: "绿色"
    offline: "灰色"
    risk: "红色"
    scanning: "黄色"
    
  delete_operation:
    button_name: "删除"
    confirmation_required: true
    
  expected_behavior:
    offline_terminals: "被删除"
    online_terminals: "被忽略，不删除"
    
  log_verification:
    log_location: "中心操作日志"
    expected_fields:
      - "操作时间"
      - "操作用户"
      - "操作类型"
      - "删除的终端数量"
      - "操作结果"
      
  test_scenarios:
    - scenario: "仅选择离线终端"
      expected: "离线终端被删除"
    - scenario: "仅选择在线终端"
      expected: "在线终端不被删除"
    - scenario: "混合选择离线和在线终端"
      expected: "仅离线终端被删除"
      
  deletion_criteria:
    - "终端必须处于离线状态"
    - "终端最后心跳时间超过阈值"
    - "终端未在执行任务"
```
