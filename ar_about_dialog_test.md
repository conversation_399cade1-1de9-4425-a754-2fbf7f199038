# AR关于对话框检查测试

## 测试信息
```yaml
test_name: "AR控制中心关于对话框检查测试"
test_description: "检查控制中心关于对话框的显示内容"
test_timeout: 120
expected_result: "关于对话框正确显示系统版本和授权信息"
test_environment: "Windows/Linux控制中心"
test_priority: "P2"
test_category: "控制中心功能测试"
```

## 前置条件
- 已登录控制中心

## 测试步骤

### 步骤1：登录控制中心
**操作描述**：使用管理员账号登录AR控制中心

**目标图片**：
![登录控制中心](images/login_center_about.png)

**操作类型**：登录
**等待时间**：5秒
**预期结果**：成功登录控制中心

---

### 步骤2：点击用户下拉菜单
**操作描述**：右上角点击下拉框，查看菜单选项

**目标图片**：
![用户下拉菜单选项](images/user_dropdown_menu_options.png)

**操作类型**：点击
**等待时间**：3秒
**预期结果**：有三个选项：修改密码、关于、退出登录

---

### 步骤3：点击关于选项
**操作描述**：在下拉菜单中点击"关于"选项

**目标图片**：
![点击关于选项](images/click_about_option.png)

**操作类型**：点击
**等待时间**：3秒
**预期结果**：打开关于对话框

---

### 步骤4：查看系统版本信息
**操作描述**：查看关于对话框中的系统版本信息

**目标图片**：
![系统版本信息](images/system_version_info.png)

**操作类型**：查看
**等待时间**：5秒
**预期结果**：能看到系统版本

---

### 步骤5：查看有效授权信息
**操作描述**：查看关于对话框中的有效授权信息

**目标图片**：
![有效授权信息](images/valid_license_info.png)

**操作类型**：查看
**等待时间**：3秒
**预期结果**：显示有效授权状态

---

### 步骤6：查看终端授权点数
**操作描述**：查看Windows/Linux/信创终端的授权点数

**目标图片**：
![终端授权点数](images/terminal_license_points.png)

**操作类型**：查看
**等待时间**：3秒
**预期结果**：Windows/Linux/信创终端的授权点数

---

### 步骤7：查看有效期信息
**操作描述**：查看授权有效期信息

**目标图片**：
![有效期信息](images/license_validity_period.png)

**操作类型**：查看
**等待时间**：3秒
**预期结果**：有效期为对应购买的期限

---

### 步骤8：查看序列号信息
**操作描述**：查看产品序列号信息

**目标图片**：
![序列号信息](images/serial_number_info.png)

**操作类型**：查看
**等待时间**：3秒
**预期结果**：对应购买的序列号

---

### 步骤9：验证信息完整性
**操作描述**：验证关于对话框中所有信息的完整性和准确性

**目标图片**：
![信息完整性验证](images/info_completeness_verification.png)

**操作类型**：验证
**等待时间**：5秒
**预期结果**：所有信息显示完整准确

---

### 步骤10：关闭关于对话框
**操作描述**：关闭关于对话框

**目标图片**：
![关闭关于对话框](images/close_about_dialog.png)

**操作类型**：关闭
**等待时间**：3秒
**预期结果**：关于对话框正常关闭

---

### 步骤11：截图保存测试结果
**操作描述**：对关于对话框测试进行截图保存

**操作类型**：截图
**保存文件名**：ar_about_dialog_test_{timestamp}.png
**预期结果**：测试结果截图保存成功

## 测试验证点
```yaml
verification_points:
  - name: "关于对话框正常打开"
    description: "关于对话框能够正常打开"
    image: "images/click_about_option.png"
    critical: true
  
  - name: "系统版本信息正确"
    description: "系统版本信息正确显示"
    image: "images/system_version_info.png"
    critical: true
  
  - name: "授权信息正确显示"
    description: "有效授权信息正确显示"
    image: "images/valid_license_info.png"
    critical: true
    
  - name: "终端授权点数正确"
    description: "各类终端授权点数正确显示"
    image: "images/terminal_license_points.png"
    critical: true
    
  - name: "有效期信息正确"
    description: "授权有效期信息正确显示"
    image: "images/license_validity_period.png"
    critical: true
    
  - name: "序列号信息正确"
    description: "产品序列号信息正确显示"
    image: "images/serial_number_info.png"
    critical: true
```

## 测试数据
```yaml
test_data:
  about_dialog_content:
    system_info:
      - "系统版本"
      - "构建时间"
      - "版本号"
    
    license_info:
      - "有效授权状态"
      - "授权类型"
      - "授权有效期"
      - "产品序列号"
    
    terminal_licenses:
      - "Windows终端授权点数"
      - "Linux终端授权点数"
      - "信创终端授权点数"
      
  expected_format:
    version: "v2.x.x.xxx"
    license_status: "有效"
    validity_period: "YYYY-MM-DD 至 YYYY-MM-DD"
    serial_number: "XXXX-XXXX-XXXX-XXXX"
    
  verification_items:
    - "版本号格式正确"
    - "授权状态显示"
    - "点数统计准确"
    - "有效期格式正确"
    - "序列号格式正确"
```
