# AR简单白名单创建测试

## 测试信息
```yaml
test_name: "AR简单白名单创建测试"
test_description: "简单测试通过路径创建白名单的基本功能"
test_timeout: 120
expected_result: "成功通过路径创建白名单规则"
test_environment: "Windows/Linux控制中心"
test_priority: "P1"
test_category: "白名单管理"
```

## 前置条件
- AR控制中心已安装并运行
- 浏览器可访问控制中心

## 测试步骤

### 步骤1：登录控制中心
**操作描述**：使用浏览器登录AR控制中心，账号admin，密码Admin.2022

**目标图片**：
![登录控制中心](images/login_center_simple_whitelist.png)

**操作类型**：登录
**等待时间**：20秒
**预期结果**：成功登录控制中心

---

### 步骤2：进入白名单管理
**操作描述**：进入控制中心-防护策略-白名单

**目标图片**：
![进入白名单管理](images/enter_whitelist_simple.png)

**操作类型**：导航
**等待时间**：10秒
**预期结果**：成功进入白名单管理页面

---

### 步骤3：点击添加白名单
**操作描述**：点击"添加"或"新建"按钮开始创建白名单

**目标图片**：
![点击添加白名单](images/click_add_whitelist.png)

**操作类型**：点击
**等待时间**：10秒
**预期结果**：打开白名单创建界面

---

### 步骤4：选择路径类型
**操作描述**：选择通过文件路径方式创建白名单

**目标图片**：
![选择路径类型](images/select_path_type.png)

**操作类型**：选择
**等待时间**：10秒
**预期结果**：选中路径白名单类型

---

### 步骤5：输入文件路径
**操作描述**：在路径输入框中输入要加白的文件路径，例如：D:\泯然\auto_test

**目标图片**：
![输入文件路径](images/input_file_path.png)

**操作类型**：输入
**等待时间**：10秒
**预期结果**：成功输入文件路径

---

### 步骤6：保存白名单规则
**操作描述**：点击"保存"或"确定"按钮保存白名单规则

**目标图片**：
![保存白名单规则](images/save_whitelist_rule.png)

**操作类型**：保存
**等待时间**：10秒
**预期结果**：白名单规则保存成功，显示在白名单列表中

---

### 步骤7：验证白名单创建成功
**操作描述**：在白名单列表中查看刚创建的规则

**目标图片**：
![验证白名单创建](images/verify_whitelist_created.png)

**操作类型**：验证
**等待时间**：10秒
**预期结果**：白名单列表中显示新创建的路径规则

---

### 步骤8：修改白名单规则
**操作描述**：在白名单列表中找到刚创建的规则，点击编辑按钮进行修改

**目标图片**：
![修改白名单规则](images/edit_whitelist_rule.png)

**操作类型**：编辑
**等待时间**：10秒
**预期结果**：打开编辑对话框，可以修改白名单路径

---

### 步骤9：更新白名单路径
**操作描述**：在编辑对话框中修改文件路径，例如改为：D:\泯然\auto_test\modified

**目标图片**：
![更新白名单路径](images/update_whitelist_path.png)

**操作类型**：输入
**等待时间**：10秒
**预期结果**：成功修改路径并保存

---

### 步骤10：删除白名单规则
**操作描述**：在白名单列表中找到修改后的规则，点击删除按钮进行删除

**目标图片**：
![删除白名单规则](images/delete_whitelist_rule.png)

**操作类型**：删除
**等待时间**：10秒
**预期结果**：成功删除白名单规则，规则从列表中消失

---

### 步骤11：截图保存测试结果
**操作描述**：对完整的白名单管理测试进行截图保存

**操作类型**：截图
**保存文件名**：ar_complete_whitelist_management_test_{timestamp}.png
**预期结果**：测试结果截图保存成功

## 测试验证点
```yaml
verification_points:
  - name: "登录成功"
    description: "能够成功登录控制中心"
    image: "images/login_center_simple_whitelist.png"
    critical: true

  - name: "白名单页面正常"
    description: "能够正常进入白名单管理页面"
    image: "images/enter_whitelist_simple.png"
    critical: true

  - name: "添加功能正常"
    description: "白名单添加功能正常"
    image: "images/click_add_whitelist.png"
    critical: true

  - name: "路径输入正常"
    description: "能够正常输入文件路径"
    image: "images/input_file_path.png"
    critical: true

  - name: "保存功能正常"
    description: "白名单规则能够正常保存"
    image: "images/save_whitelist_rule.png"
    critical: true

  - name: "创建结果正确"
    description: "白名单创建成功并显示在列表中"
    image: "images/verify_whitelist_created.png"
    critical: true

  - name: "编辑功能正常"
    description: "能够正常编辑已有的白名单规则"
    image: "images/edit_whitelist_rule.png"
    critical: true

  - name: "修改功能正常"
    description: "能够正常修改白名单路径"
    image: "images/update_whitelist_path.png"
    critical: true

  - name: "删除功能正常"
    description: "能够正常删除白名单规则"
    image: "images/delete_whitelist_rule.png"
    critical: true
```

## 测试数据
```yaml
test_data:
  login_info:
    username: "admin"
    password: "Admin.2022"
    url: "http://**************:8088"
    
  test_paths:
    original: "D:\\泯然\\auto_test"
    modified: "D:\\泯然\\auto_test\\modified"

  whitelist_type: "路径白名单"

  operations:
    - "创建白名单"
    - "修改白名单"
    - "删除白名单"

  expected_results:
    create: "白名单规则显示在列表中"
    edit: "能够打开编辑对话框"
    update: "路径修改成功并保存"
    delete: "白名单规则从列表中删除"
```
