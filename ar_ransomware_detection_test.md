# AR勒索检测测试（模拟程序）

## 测试信息
```yaml
test_name: "AR勒索检测功能测试（模拟程序）"
test_description: "使用勒索模拟程序测试AR客户端恶意行为监控功能"
test_timeout: 300
expected_result: "恶意行为监控成功检测并按策略处置勒索行为"
test_environment: "Windows/Linux客户端"
test_priority: "P0"
test_category: "客户端功能测试"
```

## 前置条件
- 已安装AR客户端
- 准备勒索软件模拟程序

## 测试步骤

### 步骤1：开启恶意行为监控功能
**操作描述**：在控制中心或客户端开启恶意行为监控功能

**目标图片**：
![开启恶意行为监控](images/enable_behavior_monitoring.png)

**操作类型**：配置修改
**等待时间**：5秒
**预期结果**：恶意行为监控功能成功开启

---

### 步骤2：执行勒索模拟程序
**操作描述**：运行勒索软件模拟程序，开始模拟勒索行为

**目标图片**：
![执行勒索模拟程序](images/run_ransomware_simulator.png)

**操作类型**：程序执行
**等待时间**：30秒
**预期结果**：触发告警日志

---

### 步骤3：验证告警触发
**操作描述**：检查是否触发了恶意行为监控告警

**目标图片**：
![恶意行为告警](images/malicious_behavior_alert.png)

**操作类型**：验证
**等待时间**：10秒
**预期结果**：成功触发恶意行为监控告警

---

### 步骤4：更改处置方式为"仅记录"
**操作描述**：在策略中将处置方式更改为"仅记录"

**目标图片**：
![设置仅记录](images/set_log_only.png)

**操作类型**：配置修改
**等待时间**：5秒
**预期结果**：策略修改成功

---

### 步骤5：再次执行勒索模拟程序
**操作描述**：重新运行勒索模拟程序测试"仅记录"模式

**目标图片**：
![再次执行模拟程序](images/rerun_simulator_log_only.png)

**操作类型**：程序执行
**等待时间**：30秒
**预期结果**：按策略中的处置方式处置（仅记录）

---

### 步骤6：更改处置方式为"杀进程"
**操作描述**：在策略中将处置方式更改为"杀进程"

**目标图片**：
![设置杀进程](images/set_kill_process.png)

**操作类型**：配置修改
**等待时间**：5秒
**预期结果**：策略修改成功

---

### 步骤7：测试杀进程功能
**操作描述**：再次运行勒索模拟程序测试"杀进程"模式

**目标图片**：
![测试杀进程](images/test_kill_process.png)

**操作类型**：程序执行
**等待时间**：30秒
**预期结果**：按策略中的处置方式处置（杀进程）

---

### 步骤8：更改处置方式为"杀进程+删文件"
**操作描述**：在策略中将处置方式更改为"杀进程+删文件"

**目标图片**：
![设置杀进程删文件](images/set_kill_and_delete.png)

**操作类型**：配置修改
**等待时间**：5秒
**预期结果**：策略修改成功

---

### 步骤9：测试杀进程+删文件功能
**操作描述**：最后一次运行勒索模拟程序测试"杀进程+删文件"模式

**目标图片**：
![测试杀进程删文件](images/test_kill_and_delete.png)

**操作类型**：程序执行
**等待时间**：30秒
**预期结果**：按策略中的处置方式处置（杀进程+删文件）

---

### 步骤10：检查安全日志
**操作描述**：查看客户端和控制中心的安全日志记录

**目标图片**：
![检查安全日志](images/check_security_logs.png)

**操作类型**：验证
**等待时间**：5秒
**预期结果**：日志正确记录所有测试过程和结果

---

### 步骤11：截图保存测试结果
**操作描述**：对测试过程和结果进行截图保存

**操作类型**：截图
**保存文件名**：ar_ransomware_detection_test_{timestamp}.png
**预期结果**：测试结果截图保存成功

## 测试验证点
```yaml
verification_points:
  - name: "恶意行为监控触发"
    description: "勒索模拟程序触发恶意行为监控告警"
    image: "images/malicious_behavior_alert.png"
    critical: true
  
  - name: "仅记录模式正常工作"
    description: "仅记录模式下只产生日志不阻止行为"
    critical: true
  
  - name: "杀进程模式正常工作"
    description: "杀进程模式下成功终止恶意进程"
    critical: true
    
  - name: "杀进程+删文件模式正常工作"
    description: "杀进程+删文件模式下成功终止进程并删除文件"
    critical: true
    
  - name: "安全日志记录完整"
    description: "所有测试过程都有完整的日志记录"
    image: "images/complete_security_logs.png"
    critical: true
```

## 测试数据
```yaml
test_data:
  simulator_program:
    name: "勒索软件模拟程序"
    path: "ransomware_simulator.exe"
    
  policy_actions:
    - "仅记录"
    - "杀进程"
    - "杀进程+删文件"
    
  expected_log_fields:
    - "检测时间"
    - "行为类型"
    - "病毒路径"
    - "操作进程"
    - "检出动作"
    - "处理结果"
    - "进程哈希"
    
  behavior_monitoring:
    feature_name: "恶意行为监控"
    behavior_type: "勒索"
```
