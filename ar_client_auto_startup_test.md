# AR客户端系统开机自动启动测试

## 测试信息
```yaml
test_name: "AR客户端系统开机自动启动测试"
test_description: "测试AR客户端在系统重启后自动启动功能"
test_timeout: 600
expected_result: "系统重启后AR客户端自动启动并保持在线状态"
test_environment: "Windows/Linux客户端"
test_priority: "P1"
test_category: "客户端功能测试"
```

## 前置条件
- mr_mainsvc服务已启动

## 测试步骤

### 步骤1：检查重启前服务状态
**操作描述**：检查重启前mr_mainsvc服务的运行状态

**目标图片**：
![重启前服务状态](images/service_status_before_reboot.png)

**操作类型**：状态检查
**等待时间**：5秒
**预期结果**：mr_mainsvc服务正在运行

---

### 步骤2：检查重启前在线状态
**操作描述**：在控制中心确认客户端处于在线状态

**目标图片**：
![重启前在线状态](images/online_status_before_reboot.png)

**操作类型**：状态检查
**等待时间**：5秒
**预期结果**：客户端在控制中心显示为在线状态

---

### 步骤3：执行系统重启
**操作描述**：重启操作系统

**目标图片**：
![执行系统重启](images/execute_system_reboot.png)

**操作类型**：系统重启
**等待时间**：10秒
**预期结果**：系统开始重启过程

---

### 步骤4：等待系统重启完成
**操作描述**：等待操作系统完全重启完成

**目标图片**：
![系统重启完成](images/system_reboot_completed.png)

**操作类型**：等待
**等待时间**：300秒
**预期结果**：系统重启完成，能够正常登录

---

### 步骤5：检查服务自动启动
**操作描述**：检查mr_mainsvc服务是否自动启动

**目标图片**：
![服务自动启动](images/service_auto_startup.png)

**操作类型**：服务检查
**等待时间**：30秒
**预期结果**：mr_mainsvc服务自动启动

---

### 步骤6：检查AR进程启动
**操作描述**：检查AR相关进程是否正常启动

**目标图片**：
![AR进程启动](images/ar_processes_startup.png)

**操作类型**：进程检查
**等待时间**：30秒
**预期结果**：AR相关进程正常启动

---

### 步骤7：验证控制中心连接
**操作描述**：检查客户端是否重新连接到控制中心

**目标图片**：
![控制中心连接](images/center_connection_restored.png)

**操作类型**：连接验证
**等待时间**：60秒
**预期结果**：客户端重新连接到控制中心

---

### 步骤8：验证在线状态恢复
**操作描述**：在控制中心确认客户端恢复在线状态

**目标图片**：
![在线状态恢复](images/online_status_restored.png)

**操作类型**：状态验证
**等待时间**：60秒
**预期结果**：操作系统重启后，EDR在控制中心仍处于在线状态

---

### 步骤9：测试客户端功能
**操作描述**：测试重启后客户端各项功能是否正常

**目标图片**：
![客户端功能测试](images/client_functionality_test.png)

**操作类型**：功能测试
**等待时间**：30秒
**预期结果**：客户端各项功能正常工作

---

### 步骤10：验证防护功能
**操作描述**：验证重启后防护功能是否正常工作

**目标图片**：
![防护功能验证](images/protection_function_verification.png)

**操作类型**：功能验证
**等待时间**：30秒
**预期结果**：防护功能正常工作

---

### 步骤11：检查客户端界面
**操作描述**：打开客户端界面，检查是否正常显示

**目标图片**：
![客户端界面检查](images/client_interface_check.png)

**操作类型**：界面检查
**等待时间**：10秒
**预期结果**：客户端界面正常显示

---

### 步骤12：验证日志记录
**操作描述**：检查重启相关的日志记录

**目标图片**：
![日志记录验证](images/log_record_verification.png)

**操作类型**：日志检查
**等待时间**：10秒
**预期结果**：重启和启动过程被正确记录

---

### 步骤13：截图保存测试结果
**操作描述**：对客户端自动启动测试进行截图保存

**操作类型**：截图
**保存文件名**：ar_client_auto_startup_test_{timestamp}.png
**预期结果**：测试结果截图保存成功

## 测试验证点
```yaml
verification_points:
  - name: "系统成功重启"
    description: "操作系统能够成功重启"
    image: "images/system_reboot_completed.png"
    critical: true
  
  - name: "服务自动启动"
    description: "mr_mainsvc服务在重启后自动启动"
    image: "images/service_auto_startup.png"
    critical: true
  
  - name: "AR进程正常启动"
    description: "AR相关进程在重启后正常启动"
    image: "images/ar_processes_startup.png"
    critical: true
    
  - name: "控制中心连接恢复"
    description: "客户端重新连接到控制中心"
    image: "images/center_connection_restored.png"
    critical: true
    
  - name: "在线状态恢复"
    description: "客户端在控制中心恢复在线状态"
    image: "images/online_status_restored.png"
    critical: true
    
  - name: "客户端功能正常"
    description: "重启后客户端各项功能正常"
    image: "images/client_functionality_test.png"
    critical: true
    
  - name: "防护功能正常"
    description: "重启后防护功能正常工作"
    image: "images/protection_function_verification.png"
    critical: true
```

## 测试数据
```yaml
test_data:
  ar_services:
    windows:
      - "mr_mainsvc"
    linux:
      - "mr_mainsvc"
      - "mr_enginesvc"
      
  ar_processes:
    windows:
      - "mr_mainsvc.exe"
      - "mredrclient.exe"
    linux:
      - "mr_mainsvc"
      - "mr_enginesvc"
      - "mredrclient"
      
  startup_verification:
    service_startup_time: "30秒内"
    center_connection_time: "60秒内"
    online_status_time: "60秒内"
    
  functionality_checks:
    - "客户端界面显示"
    - "实时防护功能"
    - "扫描功能"
    - "日志记录功能"
    - "策略同步功能"
    
  expected_logs:
    - "服务启动日志"
    - "连接控制中心日志"
    - "策略同步日志"
    - "防护功能启动日志"
    
  verification_commands:
    windows:
      check_service: "sc query mr_mainsvc"
      check_process: "tasklist | findstr mredr"
    linux:
      check_service: "systemctl status mr_mainsvc"
      check_process: "ps aux | grep mredr"
```
