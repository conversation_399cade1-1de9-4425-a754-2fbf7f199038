# AR白名单管理测试

## 测试信息
```yaml
test_name: "AR控制中心白名单管理测试"
test_description: "测试控制中心白名单的增删改功能"
test_timeout: 300
expected_result: "白名单管理功能正常工作，白名单规则正确生效"
test_environment: "Windows/Linux控制中心"
test_priority: "P0"
test_category: "防护策略/白名单"
```

## 前置条件
- 已登录控制中心
- 准备测试文件和路径

## 测试步骤

### 步骤1：登录控制中心
**操作描述**：使用浏览器登录AR控制中心，账号admin，密码Admin.2022

**目标图片**：
![登录控制中心](images/login_center_whitelist.png)

**操作类型**：登录
**等待时间**：5秒
**预期结果**：成功登录控制中心

---

### 步骤2：进入白名单管理
**操作描述**：进入控制中心-防护策略-白名单

**目标图片**：
![进入白名单管理](images/enter_whitelist_management.png)

**操作类型**：导航
**等待时间**：3秒
**预期结果**：成功进入白名单管理页面

---

### 步骤3：查看现有白名单
**操作描述**：查看当前系统中已有的白名单规则

**目标图片**：
![查看现有白名单](images/view_existing_whitelist.png)

**操作类型**：查看
**等待时间**：5秒
**预期结果**：显示所有现有的白名单规则

---

### 步骤4：通过文件路径添加白名单
**操作描述**：通过文件路径去添加，输入相对应的文件路径即可对该路径下的文件进行加白

**目标图片**：
![路径添加白名单](images/add_whitelist_by_path.png)

**操作类型**：添加
**等待时间**：5秒
**预期结果**：成功通过路径添加白名单规则

---

### 步骤5：配置路径白名单规则
**操作描述**：设置路径白名单的具体规则和范围

**目标图片**：
![配置路径白名单](images/configure_path_whitelist.png)

**操作类型**：配置
**等待时间**：5秒
**预期结果**：成功配置路径白名单规则

---

### 步骤6：保存路径白名单
**操作描述**：保存路径白名单配置

**目标图片**：
![保存路径白名单](images/save_path_whitelist.png)

**操作类型**：保存
**等待时间**：3秒
**预期结果**：路径白名单保存成功

---

### 步骤7：通过文件哈希添加白名单
**操作描述**：通过文件哈希进行加白，可以选取对应文件进行计算哈希加白

**目标图片**：
![哈希添加白名单](images/add_whitelist_by_hash.png)

**操作类型**：添加
**等待时间**：5秒
**预期结果**：成功通过哈希添加白名单规则

---

### 步骤8：选择文件计算哈希
**操作描述**：选择目标文件，系统自动计算文件哈希值

**目标图片**：
![计算文件哈希](images/calculate_file_hash.png)

**操作类型**：计算
**等待时间**：10秒
**预期结果**：成功计算文件哈希值

---

### 步骤9：保存哈希白名单
**操作描述**：保存基于哈希的白名单规则

**目标图片**：
![保存哈希白名单](images/save_hash_whitelist.png)

**操作类型**：保存
**等待时间**：3秒
**预期结果**：哈希白名单保存成功

---

### 步骤10：测试路径白名单效果
**操作描述**：对该路径下的文件进行扫描，或者文件实时监控都不会进行报毒

**目标图片**：
![测试路径白名单](images/test_path_whitelist_effect.png)

**操作类型**：测试
**等待时间**：30秒
**预期结果**：对该路径下的文件进行扫描，或者文件实时监控都不会进行报毒

---

### 步骤11：测试哈希白名单效果
**操作描述**：对加白的文件进行扫描或者复制粘贴都不会报毒

**目标图片**：
![测试哈希白名单](images/test_hash_whitelist_effect.png)

**操作类型**：测试
**等待时间**：30秒
**预期结果**：对加白的文件进行扫描或者复制粘贴都不会报毒

---

### 步骤12：编辑白名单规则
**操作描述**：修改已有的白名单规则

**目标图片**：
![编辑白名单规则](images/edit_whitelist_rule.png)

**操作类型**：编辑
**等待时间**：5秒
**预期结果**：能够正常编辑白名单规则

---

### 步骤13：删除白名单规则
**操作描述**：删除不需要的白名单规则

**目标图片**：
![删除白名单规则](images/delete_whitelist_rule.png)

**操作类型**：删除
**等待时间**：5秒
**预期结果**：白名单规则删除成功

---

### 步骤14：验证白名单同步
**操作描述**：验证白名单规则是否同步到客户端

**目标图片**：
![验证白名单同步](images/verify_whitelist_sync.png)

**操作类型**：验证
**等待时间**：60秒
**预期结果**：白名单规则正确同步到客户端

---

### 步骤15：截图保存测试结果
**操作描述**：对白名单管理测试进行截图保存

**操作类型**：截图
**保存文件名**：ar_whitelist_management_test_{timestamp}.png
**预期结果**：测试结果截图保存成功

## 测试验证点
```yaml
verification_points:
  - name: "白名单管理界面正常"
    description: "白名单管理界面能够正常打开"
    image: "images/enter_whitelist_management.png"
    critical: true
  
  - name: "路径白名单添加正常"
    description: "能够正常通过路径添加白名单"
    image: "images/add_whitelist_by_path.png"
    critical: true
  
  - name: "哈希白名单添加正常"
    description: "能够正常通过哈希添加白名单"
    image: "images/add_whitelist_by_hash.png"
    critical: true
    
  - name: "文件哈希计算正确"
    description: "能够正确计算文件哈希值"
    image: "images/calculate_file_hash.png"
    critical: true
    
  - name: "路径白名单生效"
    description: "路径白名单规则正确生效"
    image: "images/test_path_whitelist_effect.png"
    critical: true
    
  - name: "哈希白名单生效"
    description: "哈希白名单规则正确生效"
    image: "images/test_hash_whitelist_effect.png"
    critical: true
    
  - name: "白名单编辑删除正常"
    description: "白名单规则能够正常编辑和删除"
    image: "images/edit_whitelist_rule.png"
    critical: true
    
  - name: "白名单同步正常"
    description: "白名单规则能够正确同步到客户端"
    image: "images/verify_whitelist_sync.png"
    critical: true
```

## 测试数据
```yaml
test_data:
  whitelist_types:
    - "路径白名单"
    - "文件哈希白名单"
    - "进程白名单"
    - "扩展名白名单"
    
  test_paths:
    windows:
      - "C:\\Users\\<USER>\\Documents\\safe_folder\\"
      - "C:\\Program Files\\TrustedApp\\"
      - "D:\\SafeFiles\\*"
    linux:
      - "/home/<USER>/safe_folder/"
      - "/opt/trusted_app/"
      - "/usr/local/safe/*"
      
  test_files:
    - name: "safe_tool.exe"
      path: "C:\\Users\\<USER>\\safe_tool.exe"
      type: "可执行文件"
    - name: "trusted_script.bat"
      path: "C:\\Scripts\\trusted_script.bat"
      type: "脚本文件"
      
  hash_algorithms:
    - "MD5"
    - "SHA1"
    - "SHA256"
    
  whitelist_operations:
    - "添加路径白名单"
    - "添加哈希白名单"
    - "编辑白名单规则"
    - "删除白名单规则"
    - "批量导入白名单"
    - "导出白名单规则"
    
  effectiveness_tests:
    - "病毒扫描忽略"
    - "文件实时防护忽略"
    - "恶意行为监控忽略"
    - "勒索检测忽略"
    
  sync_verification:
    - "客户端白名单列表"
    - "白名单规则生效时间"
    - "白名单规则版本"
```
