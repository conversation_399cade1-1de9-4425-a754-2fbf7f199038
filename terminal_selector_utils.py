#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AR控制中心终端选择工具类
标准化的终端选择方法，可在所有自动化测试中复用

使用方法:
    from terminal_selector_utils import TerminalSelector
    
    selector = TerminalSelector(driver)
    success = selector.select_terminals(count=3)
"""

import time
import re
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC


class TerminalSelector:
    """AR控制中心终端选择器"""
    
    def __init__(self, driver, timeout=10):
        """
        初始化终端选择器
        
        Args:
            driver: Selenium WebDriver实例
            timeout: 等待超时时间，默认10秒
        """
        self.driver = driver
        self.wait = WebDriverWait(driver, timeout)
        
        # 选择器配置
        self.selectors = {
            'checkbox': "//span[contains(@class, 'el-checkbox__inner')]",
            'table_row': "./ancestor::tr[1]",
            'table_cells': "td"
        }
        
        # 识别规则
        self.rules = {
            'ip_pattern': r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}',
            'status_keywords': ['在线', '离线'],
            'os_keywords': ['Windows', 'Linux', 'openEuler'],
            'exclude_keywords': ['分组', '未分组'],
            'min_columns': 5
        }
    
    def find_all_checkboxes(self):
        """查找页面上所有的复选框"""
        try:
            checkboxes = self.driver.find_elements(By.XPATH, self.selectors['checkbox'])
            visible_checkboxes = [cb for cb in checkboxes if cb.is_displayed()]
            print(f"🔍 找到 {len(visible_checkboxes)} 个可见复选框")
            return visible_checkboxes
        except Exception as e:
            print(f"❌ 查找复选框失败: {e}")
            return []
    
    def is_terminal_checkbox(self, checkbox):
        """
        判断复选框是否为终端复选框
        
        Args:
            checkbox: 复选框元素
            
        Returns:
            dict: 包含判断结果和相关信息的字典
        """
        try:
            # 查找所在的表格行
            tr_ancestor = checkbox.find_element(By.XPATH, self.selectors['table_row'])
            tr_text = tr_ancestor.text
            
            # 获取表格列数
            td_cells = tr_ancestor.find_elements(By.TAG_NAME, self.selectors['table_cells'])
            column_count = len(td_cells)
            
            # 应用识别规则
            has_ip = bool(re.search(self.rules['ip_pattern'], tr_text))
            has_status = any(keyword in tr_text for keyword in self.rules['status_keywords'])
            has_os = any(keyword in tr_text for keyword in self.rules['os_keywords'])
            has_exclude = any(keyword in tr_text for keyword in self.rules['exclude_keywords'])
            has_enough_columns = column_count >= self.rules['min_columns']
            
            # 判断是否为终端复选框
            is_terminal = (has_ip or has_status or has_os) and not has_exclude and has_enough_columns
            
            return {
                'is_terminal': is_terminal,
                'element': checkbox,
                'row_text': tr_text[:100],
                'column_count': column_count,
                'has_ip': has_ip,
                'has_status': has_status,
                'has_os': has_os,
                'has_exclude': has_exclude
            }
            
        except Exception as e:
            print(f"⚠️ 分析复选框失败: {e}")
            return {
                'is_terminal': False,
                'element': checkbox,
                'error': str(e)
            }
    
    def find_terminal_checkboxes(self):
        """查找所有终端复选框"""
        all_checkboxes = self.find_all_checkboxes()
        terminal_checkboxes = []
        
        print("\n🎯 分析复选框类型:")
        
        for i, checkbox in enumerate(all_checkboxes):
            result = self.is_terminal_checkbox(checkbox)
            
            if result['is_terminal']:
                terminal_checkboxes.append(result)
                print(f"   ✅ 终端 {len(terminal_checkboxes)}: {result['row_text'][:50]}...")
            else:
                reason = result.get('error', '不符合终端识别规则')
                print(f"   ⚠️ 非终端 {i+1}: {reason}")
        
        print(f"\n📊 找到 {len(terminal_checkboxes)} 个终端复选框")
        return terminal_checkboxes
    
    def select_terminals(self, count=3, scroll_pause=1, click_pause=1):
        """
        选择指定数量的终端
        
        Args:
            count: 要选择的终端数量，默认3个
            scroll_pause: 滚动后等待时间，默认1秒
            click_pause: 点击后等待时间，默认1秒
            
        Returns:
            dict: 选择结果信息
        """
        try:
            print(f"\n🎯 开始选择 {count} 个终端...")
            
            # 查找终端复选框
            terminal_checkboxes = self.find_terminal_checkboxes()
            
            if not terminal_checkboxes:
                return {
                    'success': False,
                    'message': '未找到终端复选框',
                    'selected_count': 0
                }
            
            # 限制选择数量
            available_count = len(terminal_checkboxes)
            actual_count = min(count, available_count)
            
            if actual_count < count:
                print(f"⚠️ 只找到 {available_count} 个终端，将选择全部")
            
            # 执行选择
            selected_terminals = []
            success_count = 0
            
            for i in range(actual_count):
                try:
                    checkbox_info = terminal_checkboxes[i]
                    checkbox = checkbox_info['element']
                    
                    print(f"\n选择终端 {i+1}/{actual_count}:")
                    print(f"   内容: {checkbox_info['row_text']}")
                    
                    # 滚动到可见位置
                    self.driver.execute_script("arguments[0].scrollIntoView(true);", checkbox)
                    time.sleep(scroll_pause)
                    
                    # 点击选择
                    self.driver.execute_script("arguments[0].click();", checkbox)
                    time.sleep(click_pause)
                    
                    selected_terminals.append(checkbox_info)
                    success_count += 1
                    
                    print(f"   ✅ 选择成功")
                    
                except Exception as e:
                    print(f"   ❌ 选择失败: {e}")
            
            result = {
                'success': success_count > 0,
                'message': f'成功选择 {success_count}/{actual_count} 个终端',
                'selected_count': success_count,
                'total_available': available_count,
                'selected_terminals': selected_terminals
            }
            
            print(f"\n📊 选择结果: {result['message']}")
            return result
            
        except Exception as e:
            error_msg = f"终端选择过程失败: {e}"
            print(f"❌ {error_msg}")
            return {
                'success': False,
                'message': error_msg,
                'selected_count': 0
            }
    
    def get_selected_terminals_info(self):
        """获取当前已选择的终端信息"""
        try:
            # 查找已选中的复选框
            selected_checkboxes = self.driver.find_elements(
                By.XPATH, 
                "//span[contains(@class, 'el-checkbox__inner') and contains(@class, 'is-checked')]"
            )
            
            selected_info = []
            for checkbox in selected_checkboxes:
                result = self.is_terminal_checkbox(checkbox)
                if result['is_terminal']:
                    selected_info.append(result)
            
            return {
                'count': len(selected_info),
                'terminals': selected_info
            }
            
        except Exception as e:
            print(f"❌ 获取选中终端信息失败: {e}")
            return {'count': 0, 'terminals': []}
    
    def clear_selection(self):
        """清除所有终端选择"""
        try:
            print("\n🔄 清除终端选择...")
            
            # 查找已选中的终端复选框
            selected_info = self.get_selected_terminals_info()
            
            if selected_info['count'] == 0:
                print("✅ 没有已选择的终端")
                return True
            
            # 取消选择
            success_count = 0
            for terminal_info in selected_info['terminals']:
                try:
                    checkbox = terminal_info['element']
                    self.driver.execute_script("arguments[0].click();", checkbox)
                    time.sleep(0.5)
                    success_count += 1
                except Exception as e:
                    print(f"❌ 取消选择失败: {e}")
            
            print(f"✅ 成功取消选择 {success_count} 个终端")
            return success_count == selected_info['count']
            
        except Exception as e:
            print(f"❌ 清除选择失败: {e}")
            return False


# 便捷函数
def select_terminals_quick(driver, count=3, timeout=10):
    """
    快速选择终端的便捷函数
    
    Args:
        driver: Selenium WebDriver实例
        count: 要选择的终端数量
        timeout: 等待超时时间
        
    Returns:
        bool: 是否成功选择终端
    """
    selector = TerminalSelector(driver, timeout)
    result = selector.select_terminals(count)
    return result['success']


if __name__ == "__main__":
    print("AR控制中心终端选择工具类")
    print("请在其他脚本中导入使用:")
    print("from terminal_selector_utils import TerminalSelector")
