# AR客户端安全日志检查测试

## 测试信息
```yaml
test_name: "AR客户端安全日志检查测试"
test_description: "检查AR客户端安全日志的显示和功能"
test_timeout: 180
expected_result: "客户端安全日志正确显示各类安全事件"
test_environment: "Windows/Linux客户端"
test_priority: "P1"
test_category: "客户端功能测试"
```

## 前置条件
- 执行过扫描、升级等动作
- 触发过文件实时防护、恶意行为监控等告警

## 测试步骤

### 步骤1：打开客户端
**操作描述**：打开AR客户端应用程序

**目标图片**：
![打开客户端](images/open_ar_client.png)

**操作类型**：启动应用
**等待时间**：5秒
**预期结果**：成功打开AR客户端

---

### 步骤2：进入安全日志标签页
**操作描述**：进入"安全日志"标签页，检查列表

**目标图片**：
![进入安全日志](images/enter_security_log_tab.png)

**操作类型**：点击标签
**等待时间**：3秒
**预期结果**：成功进入安全日志页面

---

### 步骤3：查看安全日志列表
**操作描述**：查看安全日志列表的显示内容

**目标图片**：
![安全日志列表](images/security_log_list.png)

**操作类型**：查看
**等待时间**：5秒
**预期结果**：列表准确显示最近的若干条安全日志

---

### 步骤4：点击更多日志
**操作描述**：点击"更多日志…"，进入安全日志对话框，检查列表

**目标图片**：
![更多日志对话框](images/more_logs_dialog.png)

**操作类型**：点击
**等待时间**：3秒
**预期结果**：列表准确显示全部日志

---

### 步骤5：按类型筛选日志
**操作描述**：使用类型筛选功能筛选不同类型的日志

**目标图片**：
![按类型筛选](images/filter_by_type.png)

**操作类型**：筛选
**等待时间**：5秒
**预期结果**：筛选结果正确

---

### 步骤6：按日期筛选日志
**操作描述**：使用日期筛选功能筛选特定时间段的日志

**目标图片**：
![按日期筛选](images/filter_by_date.png)

**操作类型**：筛选
**等待时间**：5秒
**预期结果**：筛选结果正确

---

### 步骤7：查看日志详情
**操作描述**：点击某条日志，查看底部的日志详情

**目标图片**：
![查看日志详情](images/view_log_details.png)

**操作类型**：点击查看
**等待时间**：3秒
**预期结果**：日志详情显示正确

---

### 步骤8：验证扫描日志
**操作描述**：查看扫描相关的安全日志

**目标图片**：
![验证扫描日志](images/verify_scan_logs.png)

**操作类型**：验证
**等待时间**：5秒
**预期结果**：扫描日志信息完整准确

---

### 步骤9：验证升级日志
**操作描述**：查看升级相关的安全日志

**目标图片**：
![验证升级日志](images/verify_upgrade_logs.png)

**操作类型**：验证
**等待时间**：5秒
**预期结果**：升级日志信息完整准确

---

### 步骤10：验证实时防护日志
**操作描述**：查看文件实时防护相关的安全日志

**目标图片**：
![验证实时防护日志](images/verify_realtime_logs.png)

**操作类型**：验证
**等待时间**：5秒
**预期结果**：实时防护日志信息完整准确

---

### 步骤11：验证恶意行为监控日志
**操作描述**：查看恶意行为监控相关的安全日志

**目标图片**：
![验证行为监控日志](images/verify_behavior_logs.png)

**操作类型**：验证
**等待时间**：5秒
**预期结果**：恶意行为监控日志信息完整准确

---

### 步骤12：测试日志导出功能
**操作描述**：测试安全日志的导出功能（如果有）

**目标图片**：
![日志导出功能](images/log_export_function.png)

**操作类型**：导出
**等待时间**：10秒
**预期结果**：日志导出功能正常工作

---

### 步骤13：截图保存测试结果
**操作描述**：对客户端安全日志测试进行截图保存

**操作类型**：截图
**保存文件名**：ar_client_security_log_test_{timestamp}.png
**预期结果**：测试结果截图保存成功

## 测试验证点
```yaml
verification_points:
  - name: "安全日志页面正常"
    description: "客户端安全日志页面能够正常打开"
    image: "images/enter_security_log_tab.png"
    critical: true
  
  - name: "日志列表显示正确"
    description: "安全日志列表准确显示最近的日志"
    image: "images/security_log_list.png"
    critical: true
  
  - name: "更多日志功能正常"
    description: "更多日志对话框能够显示全部日志"
    image: "images/more_logs_dialog.png"
    critical: true
    
  - name: "类型筛选功能正常"
    description: "按类型筛选日志功能正常"
    image: "images/filter_by_type.png"
    critical: true
    
  - name: "日期筛选功能正常"
    description: "按日期筛选日志功能正常"
    image: "images/filter_by_date.png"
    critical: true
    
  - name: "日志详情显示正确"
    description: "日志详情信息显示正确"
    image: "images/view_log_details.png"
    critical: true
    
  - name: "各类日志信息完整"
    description: "扫描、升级、防护等各类日志信息完整"
    critical: true
```

## 测试数据
```yaml
test_data:
  log_types:
    - "病毒扫描"
    - "实时防护"
    - "恶意行为监控"
    - "软件升级"
    - "病毒库更新"
    - "策略同步"
    - "系统事件"
    
  log_fields:
    - "事件时间"
    - "事件类型"
    - "事件描述"
    - "处理结果"
    - "详细信息"
    
  filter_options:
    by_type:
      - "全部"
      - "扫描事件"
      - "防护事件"
      - "升级事件"
      - "系统事件"
    
    by_date:
      - "今天"
      - "最近7天"
      - "最近30天"
      - "自定义时间"
      
  sample_log_entries:
    scan_log:
      time: "2024-01-15 14:30:25"
      type: "病毒扫描"
      description: "全盘扫描完成"
      result: "发现2个威胁，已处理"
      
    protection_log:
      time: "2024-01-15 15:20:10"
      type: "实时防护"
      description: "检测到恶意文件"
      result: "文件已隔离"
      
    upgrade_log:
      time: "2024-01-15 16:00:00"
      type: "软件升级"
      description: "客户端升级"
      result: "升级成功"
```
