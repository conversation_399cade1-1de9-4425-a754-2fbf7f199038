#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AR扫描任务自动化测试运行脚本
提供命令行接口和配置管理
"""

import argparse
import sys
import os
import yaml
import logging
from datetime import datetime
from ar_scan_task_automation import ARScanTaskTest

def setup_logging(config):
    """设置日志"""
    log_config = config.get('logging', {})
    level = getattr(logging, log_config.get('level', 'INFO'))
    format_str = log_config.get('format', '%(asctime)s - %(levelname)s - %(message)s')
    
    # 创建日志目录
    log_file = log_config.get('file', 'ar_scan_test.log')
    log_dir = os.path.dirname(log_file)
    if log_dir and not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    logging.basicConfig(
        level=level,
        format=format_str,
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )

def load_config(config_file):
    """加载配置文件"""
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        print(f"✅ 配置文件加载成功: {config_file}")
        return config
    except FileNotFoundError:
        print(f"❌ 配置文件不存在: {config_file}")
        return None
    except yaml.YAMLError as e:
        print(f"❌ 配置文件格式错误: {e}")
        return None

def create_test_instance(config):
    """创建测试实例并应用配置"""
    test = ARScanTaskTest()
    
    # 应用系统配置
    if 'system' in config:
        system_config = config['system']
        test.config.update({
            'url': system_config.get('url', test.config['url']),
            'username': system_config.get('username', test.config['username']),
            'password': system_config.get('password', test.config['password']),
            'timeout': system_config.get('timeout', test.config['timeout'])
        })
    
    # 应用浏览器配置
    if 'browser' in config:
        browser_config = config['browser']
        test.config['chrome_driver_path'] = browser_config.get('chrome_driver_path', '')
    
    # 应用测试配置
    if 'test' in config:
        test_config = config['test']
        test.screenshot_dir = test_config.get('screenshot_dir', test.screenshot_dir)
        
        # 创建截图目录
        if not os.path.exists(test.screenshot_dir):
            os.makedirs(test.screenshot_dir)
    
    return test

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='AR扫描任务自动化测试程序',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python run_test.py                    # 使用默认配置运行测试
  python run_test.py -c custom.yaml     # 使用自定义配置文件
  python run_test.py --dry-run          # 干运行模式（仅验证配置）
  python run_test.py --verbose          # 详细输出模式
        """
    )
    
    parser.add_argument(
        '-c', '--config',
        default='config.yaml',
        help='配置文件路径 (默认: config.yaml)'
    )
    
    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='干运行模式，仅验证配置不执行测试'
    )
    
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='详细输出模式'
    )
    
    parser.add_argument(
        '--steps',
        nargs='+',
        type=int,
        help='指定要执行的步骤编号 (例如: --steps 1 2 3)'
    )
    
    args = parser.parse_args()
    
    # 打印程序信息
    print("🎯 AR扫描任务下发自动化测试程序")
    print("=" * 60)
    print(f"配置文件: {args.config}")
    print(f"运行模式: {'干运行' if args.dry_run else '正常运行'}")
    print(f"详细输出: {'是' if args.verbose else '否'}")
    if args.steps:
        print(f"指定步骤: {args.steps}")
    print("-" * 60)
    
    # 加载配置
    config = load_config(args.config)
    if not config:
        print("❌ 无法加载配置文件，程序退出")
        return 1
    
    # 设置日志
    setup_logging(config)
    
    # 干运行模式
    if args.dry_run:
        print("🔍 干运行模式 - 验证配置")
        print("✅ 配置验证通过")
        print("📋 测试步骤:")
        steps = [
            "1. 登录控制中心",
            "2. 进入终端列表", 
            "3. 选择终端并执行病毒扫描",
            "4. 配置扫描任务",
            "5. 查看扫描任务",
            "6. 查看任务详情"
        ]
        for step in steps:
            print(f"   {step}")
        return 0
    
    # 创建测试实例
    test = create_test_instance(config)
    
    # 运行测试
    try:
        success = test.run_complete_test()
        
        if success:
            print("\n🎉 所有测试步骤执行成功！")
            return 0
        else:
            print("\n💥 测试执行失败，请查看详细日志和截图")
            return 1
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断测试")
        return 2
    except Exception as e:
        print(f"\n❌ 程序执行异常: {e}")
        logging.exception("程序执行异常")
        return 3

if __name__ == "__main__":
    exit(main())
