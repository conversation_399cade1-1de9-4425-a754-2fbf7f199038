# AR修改当前用户密码测试

## 测试信息
```yaml
test_name: "AR控制中心修改当前用户密码测试"
test_description: "测试控制中心修改当前登录用户密码功能"
test_timeout: 180
expected_result: "密码修改功能正常工作，新密码生效"
test_environment: "Windows/Linux控制中心"
test_priority: "P1"
test_category: "控制中心功能测试"
```

## 前置条件
- 已登录控制中心

## 测试步骤

### 步骤1：登录控制中心
**操作描述**：使用当前用户账号登录AR控制中心

**目标图片**：
![登录控制中心](images/login_center_password_change.png)

**操作类型**：登录
**等待时间**：5秒
**预期结果**：成功登录控制中心

---

### 步骤2：点击用户下拉菜单
**操作描述**：右上角点击当前用户的下拉框

**目标图片**：
![用户下拉菜单](images/user_dropdown_menu.png)

**操作类型**：点击
**等待时间**：3秒
**预期结果**：显示下拉菜单，有三个选项：修改密码、关于、退出登录

---

### 步骤3：点击修改密码
**操作描述**：在下拉菜单中点击"修改密码"选项

**目标图片**：
![点击修改密码](images/click_change_password.png)

**操作类型**：点击
**等待时间**：3秒
**预期结果**：打开修改密码对话框

---

### 步骤4：输入原始密码
**操作描述**：在修改密码对话框中输入原始密码

**目标图片**：
![输入原始密码](images/input_original_password.png)

**操作类型**：输入
**等待时间**：5秒
**预期结果**：成功输入原始密码

---

### 步骤5：输入新密码
**操作描述**：输入修改的新密码

**目标图片**：
![输入新密码](images/input_new_password.png)

**操作类型**：输入
**等待时间**：5秒
**预期结果**：成功输入新密码

---

### 步骤6：确认新密码
**操作描述**：再次输入新密码进行确认

**目标图片**：
![确认新密码](images/confirm_new_password.png)

**操作类型**：输入
**等待时间**：5秒
**预期结果**：成功确认新密码

---

### 步骤7：保存密码修改
**操作描述**：点击保存按钮保存密码修改

**目标图片**：
![保存密码修改](images/save_password_change.png)

**操作类型**：保存
**等待时间**：5秒
**预期结果**：密码修改保存成功

---

### 步骤8：退出当前登录
**操作描述**：退出当前登录会话

**目标图片**：
![退出登录](images/logout_current_session.png)

**操作类型**：退出
**等待时间**：3秒
**预期结果**：成功退出登录

---

### 步骤9：使用新密码登录
**操作描述**：使用修改后的新密码重新登录

**目标图片**：
![新密码登录](images/login_with_new_password.png)

**操作类型**：登录
**等待时间**：5秒
**预期结果**：重新登录时，需要使用新密码才可登录

---

### 步骤10：验证旧密码失效
**操作描述**：尝试使用旧密码登录，验证是否失效

**目标图片**：
![验证旧密码失效](images/verify_old_password_invalid.png)

**操作类型**：登录验证
**等待时间**：5秒
**预期结果**：旧密码无法登录

---

### 步骤11：检查操作日志
**操作描述**：查看中心操作日志中的修改密码记录

**目标图片**：
![检查操作日志](images/check_password_change_log.png)

**操作类型**：日志查看
**等待时间**：10秒
**预期结果**：中心操作日志-有对应的修改密码的日志

---

### 步骤12：截图保存测试结果
**操作描述**：对密码修改测试进行截图保存

**操作类型**：截图
**保存文件名**：ar_password_change_test_{timestamp}.png
**预期结果**：测试结果截图保存成功

## 测试验证点
```yaml
verification_points:
  - name: "用户下拉菜单正常"
    description: "用户下拉菜单能够正常显示"
    image: "images/user_dropdown_menu.png"
    critical: true
  
  - name: "修改密码对话框正常"
    description: "修改密码对话框能够正常打开"
    image: "images/click_change_password.png"
    critical: true
  
  - name: "密码修改功能正常"
    description: "密码修改功能正常工作"
    image: "images/save_password_change.png"
    critical: true
    
  - name: "新密码生效"
    description: "新密码能够正常登录"
    image: "images/login_with_new_password.png"
    critical: true
    
  - name: "旧密码失效"
    description: "旧密码无法登录"
    image: "images/verify_old_password_invalid.png"
    critical: true
    
  - name: "操作日志正确记录"
    description: "密码修改操作被正确记录"
    image: "images/check_password_change_log.png"
    critical: true
```

## 测试数据
```yaml
test_data:
  dropdown_menu_options:
    - "修改密码"
    - "关于"
    - "退出登录"
    
  password_requirements:
    min_length: 8
    max_length: 20
    required_chars: "大小写字母、数字、特殊字符"
    
  test_passwords:
    original: "Admin.2022"
    new: "NewAdmin@2024"
    
  validation_rules:
    - "原密码必须正确"
    - "新密码必须符合复杂度要求"
    - "确认密码必须与新密码一致"
    
  expected_log_fields:
    - "操作时间"
    - "操作用户"
    - "操作类型: 修改密码"
    - "操作结果: 成功"
```
