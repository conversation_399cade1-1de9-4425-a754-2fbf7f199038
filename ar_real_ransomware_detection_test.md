# AR勒索检测测试（真实病毒）

## 测试信息
```yaml
test_name: "AR勒索检测功能测试（真实病毒）"
test_description: "使用真实勒索病毒测试AR客户端恶意行为监控功能"
test_timeout: 300
expected_result: "恶意行为监控和文件实时防护成功检测并处置真实勒索病毒"
test_environment: "Windows/Linux客户端"
test_priority: "P0"
test_category: "客户端功能测试"
```

## 前置条件
- 准备一份真实勒索病毒样本（近期的样本，且最好是行为和勒索信都报的）

## 测试步骤

### 步骤1：开启恶意行为监控功能
**操作描述**：在控制中心开启恶意行为监控功能

**目标图片**：
![开启恶意行为监控](images/enable_behavior_monitoring_real.png)

**操作类型**：配置修改
**等待时间**：5秒
**预期结果**：恶意行为监控功能成功开启

---

### 步骤2：开启文件实时防护（仅记录）
**操作描述**：开启文件实时防护功能，并设置为仅记录模式

**目标图片**：
![开启文件实时防护仅记录](images/enable_realtime_protection_log_only.png)

**操作类型**：配置修改
**等待时间**：5秒
**预期结果**：文件实时防护功能开启，设置为仅记录

---

### 步骤3：执行真实勒索病毒
**操作描述**：运行准备好的真实勒索病毒样本

**目标图片**：
![执行真实勒索病毒](images/execute_real_ransomware_sample.png)

**操作类型**：程序执行
**等待时间**：60秒
**预期结果**：真实勒索病毒开始执行

---

### 步骤4：验证恶意行为监控告警
**操作描述**：检查是否触发了恶意行为监控告警

**目标图片**：
![恶意行为监控告警](images/behavior_monitoring_alert_real.png)

**操作类型**：验证
**等待时间**：30秒
**预期结果**：触发恶意行为监控告警日志

---

### 步骤5：验证文件实时防护告警
**操作描述**：检查是否触发了文件实时防护告警

**目标图片**：
![文件实时防护告警](images/realtime_protection_alert_real.png)

**操作类型**：验证
**等待时间**：30秒
**预期结果**：触发文件实时防护告警日志

---

### 步骤6：更改处置方式为"仅记录"
**操作描述**：在策略中将恶意行为监控的处置方式更改为"仅记录"

**目标图片**：
![设置仅记录模式](images/set_behavior_log_only.png)

**操作类型**：配置修改
**等待时间**：5秒
**预期结果**：策略修改成功

---

### 步骤7：再次执行真实病毒（仅记录）
**操作描述**：重新运行真实勒索病毒测试"仅记录"模式

**目标图片**：
![再次执行仅记录模式](images/rerun_real_virus_log_only.png)

**操作类型**：程序执行
**等待时间**：60秒
**预期结果**：按策略中的处置方式处置（仅记录）

---

### 步骤8：更改处置方式为"杀进程"
**操作描述**：在策略中将处置方式更改为"杀进程"

**目标图片**：
![设置杀进程模式](images/set_behavior_kill_process.png)

**操作类型**：配置修改
**等待时间**：5秒
**预期结果**：策略修改成功

---

### 步骤9：再次执行真实病毒（杀进程）
**操作描述**：重新运行真实勒索病毒测试"杀进程"模式

**目标图片**：
![再次执行杀进程模式](images/rerun_real_virus_kill_process.png)

**操作类型**：程序执行
**等待时间**：60秒
**预期结果**：按策略中的处置方式处置（杀进程）

---

### 步骤10：更改处置方式为"杀进程+删文件"
**操作描述**：在策略中将处置方式更改为"杀进程+删文件"

**目标图片**：
![设置杀进程删文件模式](images/set_behavior_kill_and_delete.png)

**操作类型**：配置修改
**等待时间**：5秒
**预期结果**：策略修改成功

---

### 步骤11：再次执行真实病毒（杀进程+删文件）
**操作描述**：最后一次运行真实勒索病毒测试"杀进程+删文件"模式

**目标图片**：
![再次执行杀进程删文件模式](images/rerun_real_virus_kill_and_delete.png)

**操作类型**：程序执行
**等待时间**：60秒
**预期结果**：按策略中的处置方式处置（杀进程+删文件）

---

### 步骤12：检查综合安全日志
**操作描述**：查看客户端和控制中心的安全日志，确认恶意行为监控和文件实时防护都有记录

**目标图片**：
![综合安全日志](images/comprehensive_security_logs.png)

**操作类型**：验证
**等待时间**：5秒
**预期结果**：日志正确记录所有检测事件

---

### 步骤13：截图保存测试结果
**操作描述**：对测试过程和结果进行截图保存

**操作类型**：截图
**保存文件名**：ar_real_ransomware_detection_test_{timestamp}.png
**预期结果**：测试结果截图保存成功

## 测试验证点
```yaml
verification_points:
  - name: "恶意行为监控触发"
    description: "真实勒索病毒触发恶意行为监控告警"
    image: "images/behavior_monitoring_alert_real.png"
    critical: true
  
  - name: "文件实时防护触发"
    description: "真实勒索病毒触发文件实时防护告警"
    image: "images/realtime_protection_alert_real.png"
    critical: true
  
  - name: "仅记录模式正常工作"
    description: "仅记录模式下只产生日志不阻止行为"
    critical: true
    
  - name: "杀进程模式正常工作"
    description: "杀进程模式下成功终止恶意进程"
    critical: true
    
  - name: "杀进程+删文件模式正常工作"
    description: "杀进程+删文件模式下成功终止进程并删除文件"
    critical: true
    
  - name: "双重检测机制工作"
    description: "恶意行为监控和文件实时防护都能检测到威胁"
    image: "images/comprehensive_security_logs.png"
    critical: true
```

## 测试数据
```yaml
test_data:
  real_ransomware_sample:
    type: "真实勒索病毒样本"
    requirement: "近期的样本，且最好是行为和勒索信都报的"
    
  detection_mechanisms:
    - "恶意行为监控"
    - "文件实时防护"
    
  policy_actions:
    - "仅记录"
    - "杀进程"
    - "杀进程+删文件"
    
  expected_alerts:
    - type: "恶意行为监控告警"
      fields: ["检测时间", "行为类型", "进程信息", "处置动作"]
    - type: "文件实时防护告警"
      fields: ["检测时间", "病毒名称", "文件路径", "处理结果"]
      
  protection_settings:
    realtime_protection: "开启（仅记录）"
    behavior_monitoring: "开启"
```
