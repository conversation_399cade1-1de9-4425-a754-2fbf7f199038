# AR客户端网络连通性测试

## 测试信息
```yaml
test_name: "AR客户端网络连通性测试"
test_description: "测试AR客户端与控制中心的网络连通性"
test_timeout: 300
expected_result: "客户端能够正常连接控制中心，网络异常时有相应提示"
test_environment: "Windows/Linux客户端"
test_priority: "P0"
test_category: "客户端功能测试"
```

## 前置条件
- 已安装AR客户端
- 控制中心正常运行

## 测试步骤

### 步骤1：验证正常连接状态
**操作描述**：在网络正常情况下，验证客户端与控制中心的连接状态

**目标图片**：
![正常连接状态](images/normal_connection_status.png)

**操作类型**：状态验证
**等待时间**：10秒
**预期结果**：客户端显示与控制中心连接正常

---

### 步骤2：检查心跳通信
**操作描述**：观察客户端与控制中心的心跳通信

**目标图片**：
![心跳通信检查](images/heartbeat_communication_check.png)

**操作类型**：通信验证
**等待时间**：60秒
**预期结果**：心跳通信正常，控制中心显示客户端在线

---

### 步骤3：断开网络连接
**操作描述**：断开客户端的网络连接（禁用网卡或断开网线）

**目标图片**：
![断开网络连接](images/disconnect_network.png)

**操作类型**：网络操作
**等待时间**：10秒
**预期结果**：网络连接断开

---

### 步骤4：观察连接状态变化
**操作描述**：观察客户端连接状态的变化

**目标图片**：
![连接状态变化](images/connection_status_change.png)

**操作类型**：状态观察
**等待时间**：120秒
**预期结果**：客户端显示连接断开或网络异常

---

### 步骤5：验证离线提示
**操作描述**：验证客户端是否显示离线或网络异常提示

**目标图片**：
![离线提示](images/offline_notification.png)

**操作类型**：提示验证
**等待时间**：30秒
**预期结果**：客户端显示相应的离线或网络异常提示

---

### 步骤6：检查控制中心状态
**操作描述**：在控制中心查看客户端状态变化

**目标图片**：
![控制中心状态变化](images/center_status_change.png)

**操作类型**：状态验证
**等待时间**：180秒
**预期结果**：控制中心显示客户端离线状态

---

### 步骤7：恢复网络连接
**操作描述**：恢复客户端的网络连接

**目标图片**：
![恢复网络连接](images/restore_network_connection.png)

**操作类型**：网络操作
**等待时间**：10秒
**预期结果**：网络连接恢复

---

### 步骤8：观察重连过程
**操作描述**：观察客户端重新连接控制中心的过程

**目标图片**：
![重连过程](images/reconnection_process.png)

**操作类型**：过程观察
**等待时间**：120秒
**预期结果**：客户端自动重新连接控制中心

---

### 步骤9：验证连接恢复
**操作描述**：验证客户端与控制中心连接是否完全恢复

**目标图片**：
![连接恢复验证](images/connection_recovery_verification.png)

**操作类型**：连接验证
**等待时间**：60秒
**预期结果**：客户端与控制中心连接完全恢复正常

---

### 步骤10：测试部分网络限制
**操作描述**：测试在部分网络限制下的连接情况（如防火墙阻止特定端口）

**目标图片**：
![部分网络限制](images/partial_network_restriction.png)

**操作类型**：网络限制测试
**等待时间**：60秒
**预期结果**：客户端显示相应的连接问题提示

---

### 步骤11：验证功能影响
**操作描述**：验证网络异常对客户端防护功能的影响

**目标图片**：
![功能影响验证](images/function_impact_verification.png)

**操作类型**：功能验证
**等待时间**：30秒
**预期结果**：本地防护功能继续工作，远程管理功能受限

---

### 步骤12：检查网络日志
**操作描述**：查看客户端网络连接相关的日志记录

**目标图片**：
![网络连接日志](images/network_connection_logs.png)

**操作类型**：日志查看
**等待时间**：10秒
**预期结果**：网络连接状态变化被正确记录

---

### 步骤13：截图保存测试结果
**操作描述**：对网络连通性测试进行截图保存

**操作类型**：截图
**保存文件名**：ar_client_network_connectivity_test_{timestamp}.png
**预期结果**：测试结果截图保存成功

## 测试验证点
```yaml
verification_points:
  - name: "正常连接状态正确"
    description: "网络正常时连接状态显示正确"
    image: "images/normal_connection_status.png"
    critical: true
  
  - name: "心跳通信正常"
    description: "客户端与控制中心心跳通信正常"
    image: "images/heartbeat_communication_check.png"
    critical: true
  
  - name: "网络断开检测正确"
    description: "网络断开时客户端能够正确检测"
    image: "images/connection_status_change.png"
    critical: true
    
  - name: "离线提示显示正确"
    description: "网络异常时显示相应提示"
    image: "images/offline_notification.png"
    critical: true
    
  - name: "控制中心状态同步"
    description: "控制中心能够正确显示客户端离线状态"
    image: "images/center_status_change.png"
    critical: true
    
  - name: "自动重连功能正常"
    description: "网络恢复后能够自动重连"
    image: "images/reconnection_process.png"
    critical: true
    
  - name: "连接完全恢复"
    description: "重连后功能完全恢复正常"
    image: "images/connection_recovery_verification.png"
    critical: true
    
  - name: "本地功能不受影响"
    description: "网络异常时本地防护功能继续工作"
    image: "images/function_impact_verification.png"
    critical: true
    
  - name: "网络日志记录完整"
    description: "网络状态变化被正确记录"
    image: "images/network_connection_logs.png"
    critical: false
```

## 测试数据
```yaml
test_data:
  connection_states:
    - "已连接"
    - "连接中"
    - "连接失败"
    - "网络异常"
    - "离线"
    
  heartbeat_interval: "30秒"
  
  network_test_scenarios:
    - "完全断网"
    - "部分网络限制"
    - "DNS解析失败"
    - "端口被阻止"
    - "网络延迟过高"
    
  connection_parameters:
    server_address: "控制中心IP地址"
    server_port: "8088"
    heartbeat_port: "通信端口"
    
  offline_detection_time: "2-3分钟"
  reconnection_time: "1-2分钟"
  
  status_indicators:
    connected: "绿色/已连接图标"
    connecting: "黄色/连接中图标"
    disconnected: "红色/断开图标"
    
  affected_functions:
    during_offline:
      continue_working:
        - "文件实时防护"
        - "恶意行为监控"
        - "本地扫描"
      limited_functions:
        - "策略同步"
        - "远程扫描"
        - "远程升级"
        - "日志上报"
        
  log_events:
    - "连接建立"
    - "连接断开"
    - "重连尝试"
    - "连接恢复"
    - "心跳超时"
    
  network_requirements:
    outbound_ports:
      - "8088 (HTTP)"
      - "443 (HTTPS)"
    protocols:
      - "TCP"
      - "HTTP/HTTPS"
```
