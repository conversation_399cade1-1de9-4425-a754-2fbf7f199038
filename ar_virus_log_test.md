# AR病毒日志检查测试

## 测试信息
```yaml
test_name: "AR控制中心病毒日志检查测试"
test_description: "检查控制中心病毒日志（扫描日志）的显示和功能"
test_timeout: 180
expected_result: "病毒日志正确显示扫描发现的病毒信息"
test_environment: "Windows/Linux控制中心"
test_priority: "P1"
test_category: "事件日志/病毒日志"
```

## 前置条件
- 成功执行过病毒扫描
- 且发现病毒

## 测试步骤

### 步骤1：登录控制中心
**操作描述**：使用浏览器登录AR控制中心

**目标图片**：
![登录控制中心](images/login_center_virus_log.png)

**操作类型**：登录
**等待时间**：5秒
**预期结果**：成功登录控制中心

---

### 步骤2：进入事件日志
**操作描述**：进入控制中心-事件日志菜单

**目标图片**：
![进入事件日志](images/enter_event_logs.png)

**操作类型**：导航
**等待时间**：3秒
**预期结果**：成功进入事件日志页面

---

### 步骤3：点击病毒日志
**操作描述**：点击事件日志-病毒日志

**目标图片**：
![点击病毒日志](images/click_virus_logs.png)

**操作类型**：点击
**等待时间**：3秒
**预期结果**：进入病毒日志页面

---

### 步骤4：查看病毒日志列表
**操作描述**：查看病毒日志的列表显示

**目标图片**：
![病毒日志列表](images/virus_log_list.png)

**操作类型**：查看
**等待时间**：5秒
**预期结果**：有对应的扫描发现病毒的记录

---

### 步骤5：查看扫描日志详细信息
**操作描述**：点击某条日志，查看扫描日志的具体信息

**目标图片**：
![扫描日志详情](images/scan_log_details.png)

**操作类型**：查看详情
**等待时间**：5秒
**预期结果**：展示正确的扫描时间、扫描机器的别名与ip、有扫描出来相对的病毒名称以及病毒路径

---

### 步骤6：验证扫描时间显示
**操作描述**：验证日志中扫描时间的显示是否正确

**目标图片**：
![验证扫描时间](images/verify_scan_time.png)

**操作类型**：验证
**等待时间**：3秒
**预期结果**：扫描时间显示正确

---

### 步骤7：验证终端信息显示
**操作描述**：验证日志中终端别名和IP地址显示是否正确

**目标图片**：
![验证终端信息](images/verify_terminal_info.png)

**操作类型**：验证
**等待时间**：3秒
**预期结果**：扫描机器的别名与IP显示正确

---

### 步骤8：验证病毒信息显示
**操作描述**：验证日志中病毒名称和路径显示是否正确

**目标图片**：
![验证病毒信息](images/verify_virus_info.png)

**操作类型**：验证
**等待时间**：3秒
**预期结果**：有扫描出来相对的病毒名称以及病毒路径

---

### 步骤9：测试日志筛选功能
**操作描述**：使用时间、终端等条件筛选病毒日志

**目标图片**：
![日志筛选功能](images/log_filter_function.png)

**操作类型**：筛选
**等待时间**：5秒
**预期结果**：筛选功能正常工作

---

### 步骤10：测试日志搜索功能
**操作描述**：使用关键词搜索特定的病毒日志

**目标图片**：
![日志搜索功能](images/log_search_function.png)

**操作类型**：搜索
**等待时间**：5秒
**预期结果**：搜索功能正常工作

---

### 步骤11：验证日志分页功能
**操作描述**：测试日志列表的分页显示功能

**目标图片**：
![日志分页功能](images/log_pagination.png)

**操作类型**：分页操作
**等待时间**：3秒
**预期结果**：分页功能正常工作

---

### 步骤12：导出病毒日志
**操作描述**：测试病毒日志的导出功能

**目标图片**：
![导出病毒日志](images/export_virus_logs.png)

**操作类型**：导出
**等待时间**：10秒
**预期结果**：能够成功导出病毒日志

---

### 步骤13：验证导出文件内容
**操作描述**：检查导出的日志文件内容是否完整

**目标图片**：
![验证导出内容](images/verify_export_content.png)

**操作类型**：验证
**等待时间**：5秒
**预期结果**：导出文件内容完整准确

---

### 步骤14：截图保存测试结果
**操作描述**：对病毒日志检查测试进行截图保存

**操作类型**：截图
**保存文件名**：ar_virus_log_test_{timestamp}.png
**预期结果**：测试结果截图保存成功

## 测试验证点
```yaml
verification_points:
  - name: "病毒日志页面正常加载"
    description: "病毒日志页面能够正常加载显示"
    image: "images/click_virus_logs.png"
    critical: true
  
  - name: "病毒记录正确显示"
    description: "有对应的扫描发现病毒的记录"
    image: "images/virus_log_list.png"
    critical: true
  
  - name: "日志详情信息完整"
    description: "日志详情显示完整的扫描信息"
    image: "images/scan_log_details.png"
    critical: true
    
  - name: "扫描时间显示正确"
    description: "扫描时间信息显示正确"
    image: "images/verify_scan_time.png"
    critical: true
    
  - name: "终端信息显示正确"
    description: "终端别名和IP信息显示正确"
    image: "images/verify_terminal_info.png"
    critical: true
    
  - name: "病毒信息显示正确"
    description: "病毒名称和路径信息显示正确"
    image: "images/verify_virus_info.png"
    critical: true
    
  - name: "日志筛选功能正常"
    description: "日志筛选功能正常工作"
    image: "images/log_filter_function.png"
    critical: false
    
  - name: "日志导出功能正常"
    description: "日志导出功能正常工作"
    image: "images/export_virus_logs.png"
    critical: false
```

## 测试数据
```yaml
test_data:
  log_fields:
    required_fields:
      - "扫描时间"
      - "终端别名"
      - "终端IP"
      - "病毒名称"
      - "病毒路径"
      - "扫描类型"
      - "处理结果"
    
    optional_fields:
      - "文件大小"
      - "文件哈希"
      - "检测引擎"
      - "病毒类型"
      
  filter_options:
    time_range:
      - "今天"
      - "最近7天"
      - "最近30天"
      - "自定义时间"
    
    terminal_filter:
      - "按终端别名"
      - "按IP地址"
      - "按分组"
    
    virus_type:
      - "木马"
      - "病毒"
      - "蠕虫"
      - "后门"
      
  search_keywords:
    - "病毒名称"
    - "文件路径"
    - "终端名称"
    
  export_formats:
    - "Excel (.xlsx)"
    - "CSV (.csv)"
    - "PDF (.pdf)"
    
  sample_log_entry:
    scan_time: "2024-01-15 14:30:25"
    terminal_alias: "PC-001"
    terminal_ip: "*************"
    virus_name: "Trojan.Win32.Agent"
    virus_path: "C:\\Users\\<USER>\\malware.exe"
    scan_type: "全盘扫描"
    result: "已隔离"
```
