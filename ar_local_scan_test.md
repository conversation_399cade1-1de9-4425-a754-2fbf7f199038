# AR本地扫描测试

## 测试信息
```yaml
test_name: "AR客户端本地扫描功能测试"
test_description: "测试AR客户端本地病毒扫描功能"
test_timeout: 600
expected_result: "本地扫描功能正常工作，能够检测病毒并记录日志"
test_environment: "Windows/Linux客户端"
test_priority: "P0"
test_category: "客户端功能测试"
```

## 前置条件
- AR客户端已安装
- 准备一些测试病毒样本

## 测试步骤

### 步骤1：打开Windows客户端扫描界面
**操作描述**：Windows客户端：页面直接选择扫描选项

**目标图片**：
![Windows客户端扫描界面](images/windows_client_scan_interface.png)

**操作类型**：界面操作
**等待时间**：3秒
**预期结果**：成功打开扫描选项界面

---

### 步骤2：选择扫描类型和路径
**操作描述**：选择扫描类型（快速扫描/全盘扫描/自定义扫描）并设置扫描路径

**目标图片**：
![选择扫描选项](images/select_scan_options.png)

**操作类型**：选择操作
**等待时间**：3秒
**预期结果**：成功选择扫描类型和路径

---

### 步骤3：开始Windows客户端扫描
**操作描述**：点击扫描按钮开始扫描

**目标图片**：
![开始Windows扫描](images/start_windows_scan.png)

**操作类型**：点击
**等待时间**：5秒
**预期结果**：扫描开始执行

---

### 步骤4：监控Windows扫描进度
**操作描述**：观察扫描进度条和扫描状态

**目标图片**：
![Windows扫描进度](images/windows_scan_progress.png)

**操作类型**：监控
**等待时间**：300秒
**预期结果**：客户端进行扫描后有进度条，且能够显示扫描了多少病毒以及相关路径

---

### 步骤5：查看Windows扫描结果
**操作描述**：扫描完成后查看扫描结果

**目标图片**：
![Windows扫描结果](images/windows_scan_results.png)

**操作类型**：查看
**等待时间**：10秒
**预期结果**：显示扫描结果，包括发现的病毒数量和详细信息

---

### 步骤6：Linux客户端命令行扫描
**操作描述**：Linux客户端：需要输入指令./khscan 扫描路径

**目标图片**：
![Linux命令行扫描](images/linux_command_scan.png)

**操作类型**：命令执行
**等待时间**：5秒
**预期结果**：扫描命令开始执行

---

### 步骤7：监控Linux扫描进度
**操作描述**：观察Linux扫描进度（桌面版有进度条，服务器版则没有）

**目标图片**：
![Linux扫描进度](images/linux_scan_progress.png)

**操作类型**：监控
**等待时间**：300秒
**预期结果**：客户端进行扫描指定路径，桌面版有进度条\服务器版则没有

---

### 步骤8：查看Linux扫描结果
**操作描述**：Linux扫描完成后查看扫描结果

**目标图片**：
![Linux扫描结果](images/linux_scan_results.png)

**操作类型**：查看
**等待时间**：10秒
**预期结果**：显示扫描完成信息和检测结果

---

### 步骤9：检查控制中心扫描日志
**操作描述**：登录控制中心，查看事件日志-扫描日志中的扫描详情

**目标图片**：
![控制中心扫描日志](images/center_scan_logs.png)

**操作类型**：验证
**等待时间**：10秒
**预期结果**：在控制中心-事件日志-扫描日志中能看到扫描的详情

---

### 步骤10：检查终端操作日志
**操作描述**：在控制中心查看终端操作日志中的扫描操作记录

**目标图片**：
![终端操作日志](images/terminal_operation_logs.png)

**操作类型**：验证
**等待时间**：5秒
**预期结果**：在终端操作日志中，可以看到终端进行了扫描的操作

---

### 步骤11：截图保存测试结果
**操作描述**：对扫描过程和结果进行截图保存

**操作类型**：截图
**保存文件名**：ar_local_scan_test_{timestamp}.png
**预期结果**：测试结果截图保存成功

## 测试验证点
```yaml
verification_points:
  - name: "Windows客户端扫描界面正常"
    description: "Windows客户端扫描界面能够正常打开和操作"
    image: "images/windows_client_scan_interface.png"
    critical: true
  
  - name: "Windows扫描进度显示正常"
    description: "Windows客户端扫描时有进度条显示"
    image: "images/windows_scan_progress.png"
    critical: true
  
  - name: "Linux命令行扫描正常"
    description: "Linux客户端命令行扫描功能正常"
    image: "images/linux_command_scan.png"
    critical: true
    
  - name: "扫描结果显示准确"
    description: "扫描完成后结果显示准确"
    critical: true
    
  - name: "控制中心日志记录完整"
    description: "控制中心扫描日志正确记录扫描详情"
    image: "images/center_scan_logs.png"
    critical: true
    
  - name: "终端操作日志记录正确"
    description: "终端操作日志正确记录扫描操作"
    image: "images/terminal_operation_logs.png"
    critical: true
```

## 测试数据
```yaml
test_data:
  scan_types:
    - "快速扫描"
    - "全盘扫描"
    - "自定义扫描"
    
  scan_paths:
    windows:
      - "C:\\Users\\<USER>\\Documents\\"
      - "C:\\Users\\<USER>\\Desktop\\"
      - "C:\\Temp\\"
    linux:
      - "/home/<USER>/documents/"
      - "/tmp/"
      - "/home/<USER>/downloads/"
      
  linux_scan_command: "./khscan /path/to/scan"
  
  expected_progress_info:
    - "扫描进度百分比"
    - "已扫描文件数量"
    - "发现病毒数量"
    - "当前扫描路径"
    
  expected_log_fields:
    - "扫描时间"
    - "扫描类型"
    - "扫描路径"
    - "扫描结果"
    - "发现病毒数量"
    - "处理结果"
```
