# AR扫描任务下发测试

## 测试信息
```yaml
test_name: "AR控制中心下发扫描任务测试"
test_description: "测试从控制中心向客户端下发病毒扫描任务"
test_timeout: 600
expected_result: "扫描任务成功下发并执行，结果正确记录"
test_environment: "Windows/Linux控制中心和客户端"
test_priority: "P0"
test_category: "控制中心/终端列表"
```

## 前置条件
- 已安装客户端
- 客户端处于在线状态

## 测试步骤

### 步骤1：登录控制中心
**操作描述**：使用浏览器登录AR控制中心

**目标图片**：
![登录控制中心](images/login_center_scan_task.png)

**操作类型**：登录
**等待时间**：5秒
**预期结果**：成功登录控制中心

---

### 步骤2：进入终端列表
**操作描述**：进入控制中心-终端管理-终端列表

**目标图片**：
![进入终端列表](images/enter_terminal_list_scan.png)

**操作类型**：导航
**等待时间**：3秒
**预期结果**：成功进入终端列表页面

---

### 步骤3：选择目标客户端
**操作描述**：在终端列表中选择要执行扫描的客户端

**目标图片**：
![选择目标客户端](images/select_target_clients.png)

**操作类型**：选择
**等待时间**：3秒
**预期结果**：成功选择目标客户端

---

### 步骤4：点击病毒扫描按钮
**操作描述**：点击"病毒扫描"按钮开始创建扫描任务

**目标图片**：
![点击病毒扫描按钮](images/click_virus_scan_button.png)

**操作类型**：点击
**等待时间**：3秒
**预期结果**：弹出扫描任务配置对话框

---

### 步骤5：配置扫描任务参数
**操作描述**：设置扫描类型、扫描路径等参数

**目标图片**：
![配置扫描任务](images/configure_scan_task.png)

**操作类型**：配置
**等待时间**：5秒
**预期结果**：成功配置扫描任务参数

---

### 步骤6：下发扫描任务
**操作描述**：点击确认按钮下发扫描任务

**目标图片**：
![下发扫描任务](images/distribute_scan_task.png)

**操作类型**：点击确认
**等待时间**：5秒
**预期结果**：扫描任务成功下发

---

### 步骤7：查看扫描任务状态
**操作描述**：进入终端管理-扫描任务，查看任务状态

**目标图片**：
![查看扫描任务状态](images/view_scan_task_status.png)

**操作类型**：查看
**等待时间**：10秒
**预期结果**：进行扫描任务，可在扫描任务中查看任务状态

---

### 步骤8：监控任务执行进度
**操作描述**：监控扫描任务的执行进度

**目标图片**：
![监控任务进度](images/monitor_task_progress.png)

**操作类型**：监控
**等待时间**：300秒
**预期结果**：能够看到任务执行进度和状态变化

---

### 步骤9：等待任务完成
**操作描述**：等待扫描任务执行完成

**目标图片**：
![任务完成状态](images/task_completion_status.png)

**操作类型**：等待
**等待时间**：300秒
**预期结果**：任务完成之后，状态显示为已完成

---

### 步骤10：查看扫描结果
**操作描述**：任务完成后，在事件日志-病毒日志中查看扫描结果

**目标图片**：
![查看扫描结果](images/view_scan_results_log.png)

**操作类型**：查看
**等待时间**：10秒
**预期结果**：在事件日志-病毒日志中能看到扫描结果详情

---

### 步骤11：验证客户端扫描执行
**操作描述**：在客户端上验证扫描任务是否正确执行

**目标图片**：
![客户端扫描验证](images/client_scan_verification.png)

**操作类型**：验证
**等待时间**：5秒
**预期结果**：客户端显示扫描已执行，安全日志有相关记录

---

### 步骤12：截图保存测试结果
**操作描述**：对扫描任务下发和执行过程进行截图保存

**操作类型**：截图
**保存文件名**：ar_scan_task_distribution_test_{timestamp}.png
**预期结果**：测试结果截图保存成功

## 测试验证点
```yaml
verification_points:
  - name: "扫描任务创建成功"
    description: "能够成功创建和配置扫描任务"
    image: "images/configure_scan_task.png"
    critical: true
  
  - name: "任务下发成功"
    description: "扫描任务能够成功下发到目标客户端"
    image: "images/distribute_scan_task.png"
    critical: true
  
  - name: "任务状态正确显示"
    description: "扫描任务状态能够正确显示和更新"
    image: "images/view_scan_task_status.png"
    critical: true
    
  - name: "任务执行进度可监控"
    description: "能够监控扫描任务的执行进度"
    image: "images/monitor_task_progress.png"
    critical: true
    
  - name: "扫描结果正确记录"
    description: "扫描完成后结果正确记录到日志"
    image: "images/view_scan_results_log.png"
    critical: true
    
  - name: "客户端正确执行任务"
    description: "客户端能够正确接收和执行扫描任务"
    image: "images/client_scan_verification.png"
    critical: true
```

## 测试数据
```yaml
test_data:
  scan_task_types:
    - "快速扫描"
    - "全盘扫描"
    - "自定义扫描"
    
  scan_paths:
    windows:
      - "C:\\Users\\<USER>\\Program Files\\"
      - "C:\\Windows\\Temp\\"
    linux:
      - "/home/"
      - "/tmp/"
      - "/var/log/"
      
  task_status_values:
    - "待执行"
    - "执行中"
    - "已完成"
    - "执行失败"
    
  expected_log_fields:
    - "任务ID"
    - "目标终端"
    - "扫描类型"
    - "扫描路径"
    - "开始时间"
    - "完成时间"
    - "扫描结果"
    - "发现病毒数量"
    
  task_parameters:
    scan_type: "全盘扫描"
    priority: "普通"
    timeout: "3600秒"
```
