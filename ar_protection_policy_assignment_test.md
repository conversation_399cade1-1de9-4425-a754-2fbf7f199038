# AR防护策略分配测试

## 测试信息
```yaml
test_name: "AR控制中心防护策略分配测试"
test_description: "测试控制中心为终端分组分配防护策略的功能"
test_timeout: 300
expected_result: "防护策略能够正确分配给终端分组，终端应用正确的策略"
test_environment: "Windows/Linux控制中心"
test_priority: "P0"
test_category: "防护策略/分组策略"
```

## 前置条件
- 存在终端分组
- 存在防护策略

## 测试步骤

### 步骤1：登录控制中心
**操作描述**：使用浏览器登录AR控制中心

**目标图片**：
![登录控制中心](images/login_center_policy_assignment.png)

**操作类型**：登录
**等待时间**：5秒
**预期结果**：成功登录控制中心

---

### 步骤2：进入防护策略管理
**操作描述**：进入控制中心-防护策略-分组策略

**目标图片**：
![进入策略管理](images/enter_policy_management.png)

**操作类型**：导航
**等待时间**：3秒
**预期结果**：成功进入分组策略管理页面

---

### 步骤3：查看当前策略分配
**操作描述**：查看当前各个分组的策略分配情况

**目标图片**：
![查看当前策略分配](images/view_current_policy_assignment.png)

**操作类型**：查看
**等待时间**：5秒
**预期结果**：显示各分组当前的策略分配状态

---

### 步骤4：点击重新分配
**操作描述**：点击重新分配按钮，为某个分组配置防护策略

**目标图片**：
![点击重新分配](images/click_reassign_policy.png)

**操作类型**：点击
**等待时间**：3秒
**预期结果**：打开策略分配配置界面

---

### 步骤5：选择目标分组
**操作描述**：选择要配置策略的终端分组

**目标图片**：
![选择目标分组](images/select_target_group.png)

**操作类型**：选择
**等待时间**：3秒
**预期结果**：成功选择目标分组

---

### 步骤6：选择防护策略
**操作描述**：为选中的分组选择合适的防护策略

**目标图片**：
![选择防护策略](images/select_protection_policy.png)

**操作类型**：选择
**等待时间**：5秒
**预期结果**：成功选择防护策略

---

### 步骤7：确认策略分配
**操作描述**：确认策略分配配置并保存

**目标图片**：
![确认策略分配](images/confirm_policy_assignment.png)

**操作类型**：确认保存
**等待时间**：5秒
**预期结果**：策略分配配置保存成功

---

### 步骤8：验证分配结果
**操作描述**：验证策略分配是否成功应用

**目标图片**：
![验证分配结果](images/verify_assignment_result.png)

**操作类型**：验证
**等待时间**：10秒
**预期结果**：分组策略分配状态正确更新

---

### 步骤9：检查终端策略应用
**操作描述**：到终端列表，抽查某终端，其显示的策略，应与所分配的策略一致

**目标图片**：
![检查终端策略](images/check_terminal_policy.png)

**操作类型**：验证
**等待时间**：10秒
**预期结果**：配置后，到终端列表，抽查某终端，其显示的策略，应与所分配的策略一致

---

### 步骤10：验证策略生效
**操作描述**：验证新策略是否在终端上生效

**目标图片**：
![验证策略生效](images/verify_policy_effective.png)

**操作类型**：验证
**等待时间**：30秒
**预期结果**：终端应用新的防护策略设置

---

### 步骤11：检查中心操作日志
**操作描述**：查看中心操作日志，确认策略分配操作被记录

**目标图片**：
![检查操作日志](images/check_center_operation_log.png)

**操作类型**：查看
**等待时间**：10秒
**预期结果**：中心操作日志，记录本次操作

---

### 步骤12：测试多个分组分配
**操作描述**：为多个不同分组分配不同的防护策略

**目标图片**：
![多分组策略分配](images/multiple_group_policy_assignment.png)

**操作类型**：配置
**等待时间**：15秒
**预期结果**：能够为多个分组分配不同策略

---

### 步骤13：验证策略继承
**操作描述**：验证新加入分组的终端是否自动继承分组策略

**目标图片**：
![验证策略继承](images/verify_policy_inheritance.png)

**操作类型**：验证
**等待时间**：30秒
**预期结果**：新加入分组的终端自动应用分组策略

---

### 步骤14：截图保存测试结果
**操作描述**：对策略分配测试进行截图保存

**操作类型**：截图
**保存文件名**：ar_protection_policy_assignment_test_{timestamp}.png
**预期结果**：测试结果截图保存成功

## 测试验证点
```yaml
verification_points:
  - name: "策略分配界面正常"
    description: "策略分配管理界面能够正常打开"
    image: "images/enter_policy_management.png"
    critical: true
  
  - name: "当前分配状态正确显示"
    description: "能够正确显示当前策略分配状态"
    image: "images/view_current_policy_assignment.png"
    critical: true
  
  - name: "策略分配功能正常"
    description: "能够正常为分组分配防护策略"
    image: "images/confirm_policy_assignment.png"
    critical: true
    
  - name: "终端策略显示一致"
    description: "终端显示的策略与分配的策略一致"
    image: "images/check_terminal_policy.png"
    critical: true
    
  - name: "策略在终端生效"
    description: "分配的策略在终端上正确生效"
    image: "images/verify_policy_effective.png"
    critical: true
    
  - name: "操作日志正确记录"
    description: "策略分配操作被正确记录"
    image: "images/check_center_operation_log.png"
    critical: true
    
  - name: "策略继承功能正常"
    description: "新终端能够正确继承分组策略"
    image: "images/verify_policy_inheritance.png"
    critical: true
```

## 测试数据
```yaml
test_data:
  terminal_groups:
    - "Windows终端组"
    - "Linux终端组"
    - "服务器组"
    - "测试组"
    
  protection_policies:
    - "默认策略"
    - "严格策略"
    - "宽松策略"
    - "服务器策略"
    - "测试策略"
    
  policy_components:
    - "文件实时防护"
    - "恶意行为监控"
    - "勒索诱捕"
    - "扫描策略"
    - "升级策略"
    
  assignment_operations:
    - "查看当前分配"
    - "重新分配策略"
    - "批量分配"
    - "策略继承验证"
    
  verification_points:
    - "分组策略状态更新"
    - "终端策略显示一致"
    - "策略设置生效"
    - "操作日志记录"
    
  expected_log_fields:
    - "操作时间"
    - "操作用户"
    - "功能模块"
    - "操作类型"
    - "操作对象"
    - "操作结果"
    
  policy_sync_time: "30秒内"
```
