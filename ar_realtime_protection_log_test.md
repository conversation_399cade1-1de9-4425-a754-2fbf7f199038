# AR文件实时防护日志测试

## 测试信息
```yaml
test_name: "AR控制中心文件实时防护日志检查测试"
test_description: "检查控制中心文件实时防护日志的显示和功能"
test_timeout: 180
expected_result: "文件实时防护日志正确显示实时防护事件信息"
test_environment: "Windows/Linux控制中心"
test_priority: "P1"
test_category: "事件日志/文件实时防护"
```

## 前置条件
- 触发过文件实时防护告警

## 测试步骤

### 步骤1：登录控制中心
**操作描述**：使用浏览器登录AR控制中心

**目标图片**：
![登录控制中心](images/login_center_realtime_log.png)

**操作类型**：登录
**等待时间**：5秒
**预期结果**：成功登录控制中心

---

### 步骤2：进入事件日志
**操作描述**：进入控制中心-事件日志菜单

**目标图片**：
![进入事件日志](images/enter_event_logs_realtime.png)

**操作类型**：导航
**等待时间**：3秒
**预期结果**：成功进入事件日志页面

---

### 步骤3：点击文件实时防护日志
**操作描述**：点击事件日志-文件实时防护

**目标图片**：
![点击实时防护日志](images/click_realtime_protection_logs.png)

**操作类型**：点击
**等待时间**：3秒
**预期结果**：进入文件实时防护日志页面

---

### 步骤4：查看实时防护日志列表
**操作描述**：查看文件实时防护日志的列表显示

**目标图片**：
![实时防护日志列表](images/realtime_protection_log_list.png)

**操作类型**：查看
**等待时间**：5秒
**预期结果**：有对应的文件实时监控的日志

---

### 步骤5：查看日志详细信息
**操作描述**：点击某条日志，查看文件实时监控日志的具体信息

**目标图片**：
![实时防护日志详情](images/realtime_protection_log_details.png)

**操作类型**：查看详情
**等待时间**：5秒
**预期结果**：文件实时监控日志有正确的扫描时间、扫描机器的别名与ip、有对应的病毒名称、病毒路径、操作进程、检出动作、处理结果、病毒的哈希

---

### 步骤6：验证扫描时间显示
**操作描述**：验证日志中扫描时间的显示是否正确

**目标图片**：
![验证扫描时间实时](images/verify_scan_time_realtime.png)

**操作类型**：验证
**等待时间**：3秒
**预期结果**：扫描时间显示正确

---

### 步骤7：验证终端信息显示
**操作描述**：验证日志中终端别名和IP地址显示是否正确

**目标图片**：
![验证终端信息实时](images/verify_terminal_info_realtime.png)

**操作类型**：验证
**等待时间**：3秒
**预期结果**：扫描机器的别名与IP显示正确

---

### 步骤8：验证病毒信息显示
**操作描述**：验证日志中病毒名称和路径显示是否正确

**目标图片**：
![验证病毒信息实时](images/verify_virus_info_realtime.png)

**操作类型**：验证
**等待时间**：3秒
**预期结果**：有对应的病毒名称、病毒路径显示正确

---

### 步骤9：验证操作进程信息
**操作描述**：验证日志中操作进程信息显示是否正确

**目标图片**：
![验证操作进程](images/verify_operation_process.png)

**操作类型**：验证
**等待时间**：3秒
**预期结果**：操作进程信息显示正确

---

### 步骤10：验证检出动作和处理结果
**操作描述**：验证日志中检出动作和处理结果显示是否正确

**目标图片**：
![验证检出动作](images/verify_detection_action.png)

**操作类型**：验证
**等待时间**：3秒
**预期结果**：检出动作、处理结果显示正确

---

### 步骤11：验证病毒哈希信息
**操作描述**：验证日志中病毒哈希值显示是否正确

**目标图片**：
![验证病毒哈希](images/verify_virus_hash.png)

**操作类型**：验证
**等待时间**：3秒
**预期结果**：病毒的哈希值显示正确

---

### 步骤12：测试日志筛选功能
**操作描述**：使用时间、终端、处理结果等条件筛选实时防护日志

**目标图片**：
![实时防护日志筛选](images/realtime_log_filter.png)

**操作类型**：筛选
**等待时间**：5秒
**预期结果**：筛选功能正常工作

---

### 步骤13：测试日志搜索功能
**操作描述**：使用关键词搜索特定的实时防护日志

**目标图片**：
![实时防护日志搜索](images/realtime_log_search.png)

**操作类型**：搜索
**等待时间**：5秒
**预期结果**：搜索功能正常工作

---

### 步骤14：截图保存测试结果
**操作描述**：对文件实时防护日志测试进行截图保存

**操作类型**：截图
**保存文件名**：ar_realtime_protection_log_test_{timestamp}.png
**预期结果**：测试结果截图保存成功

## 测试验证点
```yaml
verification_points:
  - name: "实时防护日志页面正常"
    description: "文件实时防护日志页面能够正常加载"
    image: "images/click_realtime_protection_logs.png"
    critical: true
  
  - name: "实时防护记录正确显示"
    description: "有对应的文件实时监控的日志"
    image: "images/realtime_protection_log_list.png"
    critical: true
  
  - name: "日志详情信息完整"
    description: "日志详情显示完整的实时防护信息"
    image: "images/realtime_protection_log_details.png"
    critical: true
    
  - name: "扫描时间显示正确"
    description: "扫描时间信息显示正确"
    image: "images/verify_scan_time_realtime.png"
    critical: true
    
  - name: "终端信息显示正确"
    description: "终端别名和IP信息显示正确"
    image: "images/verify_terminal_info_realtime.png"
    critical: true
    
  - name: "病毒信息显示正确"
    description: "病毒名称和路径信息显示正确"
    image: "images/verify_virus_info_realtime.png"
    critical: true
    
  - name: "操作进程信息正确"
    description: "操作进程信息显示正确"
    image: "images/verify_operation_process.png"
    critical: true
    
  - name: "检出动作和结果正确"
    description: "检出动作和处理结果显示正确"
    image: "images/verify_detection_action.png"
    critical: true
    
  - name: "病毒哈希信息正确"
    description: "病毒哈希值信息显示正确"
    image: "images/verify_virus_hash.png"
    critical: true
```

## 测试数据
```yaml
test_data:
  log_fields:
    required_fields:
      - "扫描时间"
      - "终端别名"
      - "终端IP"
      - "病毒名称"
      - "病毒路径"
      - "操作进程"
      - "检出动作"
      - "处理结果"
      - "病毒哈希"
    
    optional_fields:
      - "文件大小"
      - "进程PID"
      - "用户名"
      - "检测引擎"
      
  detection_actions:
    - "实时监控检出"
    - "文件访问拦截"
    - "文件写入拦截"
    - "文件执行拦截"
    
  processing_results:
    - "已隔离"
    - "已删除"
    - "已阻止"
    - "仅记录"
    - "用户忽略"
    
  filter_options:
    time_range:
      - "今天"
      - "最近7天"
      - "最近30天"
      - "自定义时间"
    
    result_filter:
      - "已隔离"
      - "已删除"
      - "已阻止"
      - "仅记录"
    
    terminal_filter:
      - "按终端别名"
      - "按IP地址"
      - "按分组"
      
  sample_log_entry:
    scan_time: "2024-01-15 14:30:25"
    terminal_alias: "PC-001"
    terminal_ip: "*************"
    virus_name: "Trojan.Win32.Agent"
    virus_path: "C:\\Users\\<USER>\\malware.exe"
    operation_process: "explorer.exe"
    detection_action: "文件访问拦截"
    processing_result: "已隔离"
    virus_hash: "d41d8cd98f00b204e9800998ecf8427e"
```
