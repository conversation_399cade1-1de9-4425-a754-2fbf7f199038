# AR安全软件测试用例汇总

## 📋 已创建的测试用例列表

### 🏢 控制中心相关测试用例

1. **ar_control_center_install_test.md** - AR控制中心安装测试
2. **ar_center_auto_startup_test.md** - AR控制中心系统开机自动启动测试
3. **ar_homepage_display_test.md** - AR控制中心首页显示测试
4. **ar_terminal_list_test.md** - AR终端列表显示测试
5. **ar_terminal_details_test.md** - AR终端详情显示测试
6. **ar_scan_task_distribution_test.md** - AR扫描任务下发测试
7. **ar_client_upgrade_task_test.md** - AR客户端升级任务测试
8. **ar_terminal_grouping_test.md** - AR终端分组测试
9. **ar_risk_status_reset_test.md** - AR风险状态复位测试

### 💻 客户端安装卸载测试用例

10. **ar_windows_client_install_test.md** - AR Windows客户端安装测试
11. **ar_linux_client_install_test.md** - AR Linux客户端安装测试
12. **ar_windows_client_uninstall_test.md** - AR Windows客户端卸载测试
13. **ar_linux_client_uninstall_test.md** - AR Linux客户端卸载测试

### 🛡️ 客户端功能测试用例

14. **ar_file_realtime_protection_test.md** - AR文件实时防护测试
15. **ar_ransomware_detection_test.md** - AR勒索检测测试（模拟程序）
16. **ar_real_ransomware_detection_test.md** - AR勒索检测测试（真实病毒）
17. **ar_ransomware_honeypot_test.md** - AR勒索诱捕测试
18. **ar_local_scan_test.md** - AR本地扫描测试
19. **ar_client_security_log_test.md** - AR客户端安全日志检查测试
20. **ar_client_auto_startup_test.md** - AR客户端系统开机自动启动测试

### 📋 策略管理测试用例

21. **ar_protection_policy_assignment_test.md** - AR防护策略分配测试
22. **ar_policy_management_test.md** - AR防护策略编辑管理测试
23. **ar_whitelist_management_test.md** - AR白名单管理测试

### 📊 日志管理测试用例

24. **ar_virus_log_test.md** - AR病毒日志检查测试
25. **ar_realtime_protection_log_test.md** - AR文件实时防护日志测试

### 👥 用户管理测试用例

26. **ar_user_management_test.md** - AR用户管理测试
27. **ar_system_configuration_test.md** - AR系统配置检查测试
28. **ar_password_change_test.md** - AR修改当前用户密码测试
29. **ar_about_dialog_test.md** - AR关于对话框检查测试

### 🔧 客户端高级功能测试用例

30. **ar_quarantine_operations_test.md** - AR隔离区操作测试
31. **ar_client_protection_settings_test.md** - AR客户端防护设置检查测试
32. **ar_client_hardware_info_test.md** - AR客户端硬件信息显示测试
33. **ar_client_self_protection_test.md** - AR客户端自我保护测试

### ⏰ 计划任务测试用例

34. **ar_scheduled_scan_test.md** - AR计划任务定期扫描测试

### 🗑️ 终端管理高级功能测试用例

35. **ar_delete_offline_terminals_test.md** - AR删除离线客户端测试
36. **ar_scan_task_status_test.md** - AR扫描任务状态检查测试
37. **ar_upgrade_task_status_test.md** - AR升级任务状态检查测试

### 📋 高级日志管理测试用例

38. **ar_behavior_monitoring_log_test.md** - AR恶意行为监控日志测试
39. **ar_ransomware_honeypot_log_test.md** - AR勒索诱捕日志测试
40. **ar_terminal_operation_log_test.md** - AR终端操作日志测试
41. **ar_center_operation_log_test.md** - AR中心操作日志测试

### 🖥️ 客户端界面和交互测试用例

42. **ar_client_status_display_test.md** - AR客户端状态显示测试
43. **ar_client_tray_icon_test.md** - AR客户端托盘图标测试

### 🌐 网络和性能测试用例

44. **ar_client_network_connectivity_test.md** - AR客户端网络连通性测试
45. **ar_performance_impact_test.md** - AR客户端性能影响测试

### 🔄 系统维护和更新测试用例

46. **ar_virus_database_update_test.md** - AR病毒库更新测试
47. **ar_client_configuration_test.md** - AR客户端配置管理测试

### 🛠️ 系统稳定性和兼容性测试用例

48. **ar_error_handling_test.md** - AR错误处理和异常情况测试
49. **ar_multi_platform_compatibility_test.md** - AR多平台兼容性测试

### 🔧 系统管理和维护测试用例

50. **ar_backup_restore_test.md** - AR备份恢复测试
51. **ar_license_management_test.md** - AR授权许可管理测试
52. **ar_system_monitoring_test.md** - AR系统监控测试
53. **ar_report_generation_test.md** - AR报告生成测试
54. **ar_alert_notification_test.md** - AR告警通知测试
55. **ar_data_migration_test.md** - AR数据迁移测试

### 🔌 集成和安全测试用例

56. **ar_integration_api_test.md** - AR集成API测试
57. **ar_security_hardening_test.md** - AR安全加固测试
58. **ar_load_stress_test.md** - AR负载压力测试

### 🚨 灾难恢复和综合测试用例

59. **ar_disaster_recovery_test.md** - AR灾难恢复测试
60. **ar_comprehensive_integration_test.md** - AR综合集成测试

## 🖼️ 需要准备的截图文件清单

### 📁 建议的目录结构
```
images/
├── control_center/          # 控制中心相关截图
├── client_install/          # 客户端安装相关截图
├── client_functions/        # 客户端功能相关截图
├── scan_operations/         # 扫描操作相关截图
├── protection_features/     # 防护功能相关截图
└── system_operations/       # 系统操作相关截图
```

### 🎯 按测试用例分类的截图清单

#### 控制中心安装测试
- `images/ssh_connection.png` - SSH连接界面
- `images/extract_package.png` - 解压安装包
- `images/run_setup.png` - 运行安装程序
- `images/ar_center_login.png` - AR控制中心登录界面
- `images/client_package_directory.png` - 客户端安装包目录
- `images/install_success.png` - 安装成功提示
- `images/ar_center_dashboard.png` - AR控制中心主界面

#### Windows客户端安装测试
- `images/cmd_window.png` - CMD命令窗口
- `images/msiexec_install_command.png` - 执行安装命令
- `images/terminal_list_new_client.png` - 控制中心终端列表
- `images/client_install_directory.png` - 客户端安装目录
- `images/mr_mainsvc_service.png` - 系统服务状态
- `images/ar_processes.png` - AR相关进程

#### Linux客户端安装测试
- `images/ssh_connect_production.png` - SSH连接生产机
- `images/cd_package_directory.png` - 进入安装包目录
- `images/redhat_install_command.png` - RedHat系统安装命令
- `images/debian_install_command.png` - Debian系统安装命令
- `images/verify_linux_install_directory.png` - 验证安装目录
- `images/check_linux_services.png` - 检查Linux服务状态
- `images/check_ar_processes_linux.png` - 检查AR进程
- `images/center_terminal_list_linux.png` - 控制中心终端列表Linux

#### 首页显示测试
- `images/login_ar_center.png` - 登录控制中心
- `images/ar_center_homepage.png` - 控制中心首页
- `images/homepage_cards_display.png` - 首页卡片显示
- `images/terminal_card_tooltip.png` - 终端卡片气泡提示
- `images/attention_card_tooltip.png` - 注意卡片气泡提示
- `images/risk_card_tooltip.png` - 风险卡片气泡提示
- `images/security_card_tooltip.png` - 安全卡片气泡提示
- `images/data_accuracy_verification.png` - 数据准确性验证

#### 文件实时防护测试
- `images/copy_malware_samples.png` - 拷贝样本文件
- `images/run_malware_samples.png` - 运行恶意样本
- `images/client_security_log.png` - 客户端安全日志
- `images/center_realtime_protection_log.png` - 控制中心实时防护日志
- `images/disable_realtime_protection.png` - 关闭实时防护策略
- `images/retest_malware_samples.png` - 重新测试样本
- `images/realtime_protection_alert.png` - 实时防护告警

#### 勒索检测测试（模拟程序）
- `images/enable_behavior_monitoring.png` - 开启恶意行为监控
- `images/run_ransomware_simulator.png` - 执行勒索模拟程序
- `images/malicious_behavior_alert.png` - 恶意行为告警
- `images/set_log_only.png` - 设置仅记录
- `images/set_kill_process.png` - 设置杀进程
- `images/set_kill_and_delete.png` - 设置杀进程删文件
- `images/check_security_logs.png` - 检查安全日志

#### 勒索检测测试（真实病毒）
- `images/enable_behavior_monitoring_real.png` - 开启恶意行为监控
- `images/enable_realtime_protection_log_only.png` - 开启文件实时防护仅记录
- `images/execute_real_ransomware_sample.png` - 执行真实勒索病毒
- `images/behavior_monitoring_alert_real.png` - 恶意行为监控告警
- `images/realtime_protection_alert_real.png` - 文件实时防护告警
- `images/comprehensive_security_logs.png` - 综合安全日志

#### 勒索诱捕测试
- `images/enable_ransomware_honeypot.png` - 开启勒索诱捕功能
- `images/check_honeypot_files.png` - 检查诱捕文件
- `images/view_tagfile_config.png` - 查看诱捕配置文件
- `images/execute_real_ransomware.png` - 执行勒索病毒
- `images/honeypot_files_encrypted.png` - 诱捕文件被加密
- `images/honeypot_alert_logs.png` - 勒索诱捕告警日志
- `images/disable_ransomware_honeypot.png` - 关闭勒索诱捕功能
- `images/honeypot_files_cleanup.png` - 诱捕文件清理

#### 本地扫描测试
- `images/windows_client_scan_interface.png` - Windows客户端扫描界面
- `images/select_scan_options.png` - 选择扫描选项
- `images/start_windows_scan.png` - 开始Windows扫描
- `images/windows_scan_progress.png` - Windows扫描进度
- `images/windows_scan_results.png` - Windows扫描结果
- `images/linux_command_scan.png` - Linux命令行扫描
- `images/linux_scan_progress.png` - Linux扫描进度
- `images/linux_scan_results.png` - Linux扫描结果
- `images/center_scan_logs.png` - 控制中心扫描日志
- `images/terminal_operation_logs.png` - 终端操作日志

#### 终端列表测试
- `images/login_center_terminal_list.png` - 登录控制中心
- `images/terminal_list_interface.png` - 终端列表界面
- `images/terminal_status_display.png` - 终端状态显示
- `images/software_version_display.png` - 软件版本显示
- `images/filter_by_alias.png` - 别名筛选功能
- `images/filter_by_ip.png` - IP筛选功能
- `images/filter_by_os.png` - 操作系统筛选
- `images/custom_display_fields.png` - 自定义显示字段
- `images/adjust_display_fields.png` - 调整显示字段
- `images/field_adjustment_result.png` - 字段调整效果

#### 扫描任务下发测试
- `images/login_center_scan_task.png` - 登录控制中心
- `images/enter_terminal_list_scan.png` - 进入终端列表
- `images/select_target_clients.png` - 选择目标客户端
- `images/click_virus_scan_button.png` - 点击病毒扫描按钮
- `images/configure_scan_task.png` - 配置扫描任务
- `images/distribute_scan_task.png` - 下发扫描任务
- `images/view_scan_task_status.png` - 查看扫描任务状态
- `images/monitor_task_progress.png` - 监控任务进度
- `images/task_completion_status.png` - 任务完成状态
- `images/view_scan_results_log.png` - 查看扫描结果
- `images/client_scan_verification.png` - 客户端扫描验证

#### Windows客户端卸载测试
- `images/open_control_panel.png` - 打开控制面板
- `images/programs_and_features.png` - 程序和功能
- `images/find_ar_client_program.png` - 找到AR客户端程序
- `images/execute_uninstall_operation.png` - 执行卸载操作
- `images/confirm_uninstall.png` - 确认卸载
- `images/command_line_uninstall.png` - 命令行卸载
- `images/center_terminal_disappeared.png` - 控制中心终端消失
- `images/windows_folders_deleted.png` - Windows文件夹删除
- `images/terminal_operation_log_uninstall.png` - 终端运行日志
- `images/system_services_cleanup.png` - 系统服务清理
- `images/registry_cleanup.png` - 注册表清理

## 📊 统计信息

- **已创建测试用例数量**: 60个 ✅ **完成！**
- **需要准备的截图数量**: 约750张
- **涵盖的测试类别**:
  - 控制中心功能 (9个)
  - 客户端安装卸载 (4个)
  - 客户端防护功能 (7个)
  - 策略管理 (3个)
  - 日志管理 (2个)
  - 用户管理 (4个)
  - 客户端高级功能 (4个)
  - 计划任务 (1个)
  - 终端管理高级功能 (3个)
  - 高级日志管理 (4个)
  - 客户端界面和交互 (2个)
  - 网络和性能 (2个)
  - 系统维护和更新 (2个)
  - 系统稳定性和兼容性 (2个)
  - 系统管理和维护 (6个)
  - 集成和安全 (3个)
  - 灾难恢复和综合 (2个)

## 🎉 项目完成状态

✅ **所有60个测试用例已创建完成！**

## 🚀 下一步工作

1. **创建images文件夹**并按建议的目录结构组织
2. **按照测试用例逐一准备截图**（约750张截图）
3. **开始编写自动化执行程序**：基于现有的测试用例开始开发

## 💡 截图准备建议

1. **保持截图清晰度**：建议使用1920x1080或更高分辨率
2. **统一截图风格**：保持界面主题和字体大小一致
3. **突出关键信息**：确保关键按钮、文字、状态信息清晰可见
4. **文件命名规范**：严格按照测试用例中指定的文件名保存
5. **分类存储**：按功能模块分类存储，便于管理和查找

你觉得这个汇总怎么样？需要我继续创建剩余的测试用例吗？
