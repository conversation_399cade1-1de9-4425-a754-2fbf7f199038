# AR隔离区操作测试

## 测试信息
```yaml
test_name: "AR客户端隔离区操作测试"
test_description: "测试AR客户端隔离区的提取、恢复、删除功能"
test_timeout: 300
expected_result: "隔离区操作功能正常工作，能够正确管理隔离文件"
test_environment: "Windows/Linux客户端"
test_priority: "P1"
test_category: "客户端功能测试"
```

## 前置条件
- 已有"病毒文件"被隔离

## 测试步骤

### 步骤1：打开客户端隔离区
**操作描述**：打开AR客户端，进入隔离区管理界面

**目标图片**：
![打开隔离区](images/open_quarantine_zone.png)

**操作类型**：界面操作
**等待时间**：5秒
**预期结果**：成功打开隔离区管理界面

---

### 步骤2：查看隔离文件列表
**操作描述**：查看当前隔离区中的文件列表

**目标图片**：
![隔离文件列表](images/quarantine_file_list.png)

**操作类型**：查看
**等待时间**：3秒
**预期结果**：显示已隔离的病毒文件列表

---

### 步骤3：选择要操作的隔离文件
**操作描述**：选择隔离区中的某个/某些文件

**目标图片**：
![选择隔离文件](images/select_quarantine_files.png)

**操作类型**：选择
**等待时间**：3秒
**预期结果**：成功选择隔离文件

---

### 步骤4：提取隔离文件（图形界面）
**操作描述**：图形界面中，提取隔离区某个/某些文件

**目标图片**：
![提取隔离文件](images/extract_quarantine_files.png)

**操作类型**：提取操作
**等待时间**：10秒
**预期结果**：文件被成功提取到目标位置

---

### 步骤5：恢复隔离文件（图形界面）
**操作描述**：恢复隔离区某个/某些文件到原路径

**目标图片**：
![恢复隔离文件](images/restore_quarantine_files.png)

**操作类型**：恢复操作
**等待时间**：10秒
**预期结果**：文件被成功恢复到原路径

---

### 步骤6：删除隔离文件（图形界面）
**操作描述**：删除隔离区某个/某些文件

**目标图片**：
![删除隔离文件](images/delete_quarantine_files.png)

**操作类型**：删除操作
**等待时间**：5秒
**预期结果**：成功删除

---

### 步骤7：使用命令行列出隔离文件
**操作描述**：使用安装目录中的mr_utils程序（以管理员运行CMD），列出隔离区文件（mr_utils -l)

**目标图片**：
![命令行列出文件](images/command_list_quarantine.png)

**操作类型**：命令执行
**等待时间**：5秒
**预期结果**：正确显示被隔离文件

---

### 步骤8：命令行提取隔离文件
**操作描述**：提取隔离区文件（mr_utils -g id号 -f 目标路径及最后文件名)

**目标图片**：
![命令行提取文件](images/command_extract_quarantine.png)

**操作类型**：命令执行
**等待时间**：10秒
**预期结果**：文件被成功提取到目标位置

---

### 步骤9：命令行恢复隔离文件
**操作描述**：恢复隔离区文件（mr_utils -r id号)

**目标图片**：
![命令行恢复文件](images/command_restore_quarantine.png)

**操作类型**：命令执行
**等待时间**：10秒
**预期结果**：文件被成功恢复到原路径

---

### 步骤10：验证操作结果
**操作描述**：验证提取、恢复、删除操作的结果

**目标图片**：
![验证操作结果](images/verify_operation_results.png)

**操作类型**：验证
**等待时间**：10秒
**预期结果**：所有操作结果正确

---

### 步骤11：检查隔离区日志
**操作描述**：查看隔离区操作相关的日志记录

**目标图片**：
![隔离区日志](images/quarantine_operation_logs.png)

**操作类型**：日志查看
**等待时间**：5秒
**预期结果**：操作日志正确记录

---

### 步骤12：截图保存测试结果
**操作描述**：对隔离区操作测试进行截图保存

**操作类型**：截图
**保存文件名**：ar_quarantine_operations_test_{timestamp}.png
**预期结果**：测试结果截图保存成功

## 测试验证点
```yaml
verification_points:
  - name: "隔离区界面正常"
    description: "隔离区管理界面能够正常打开"
    image: "images/open_quarantine_zone.png"
    critical: true
  
  - name: "隔离文件列表正确"
    description: "能够正确显示隔离文件列表"
    image: "images/quarantine_file_list.png"
    critical: true
  
  - name: "图形界面提取功能正常"
    description: "图形界面提取隔离文件功能正常"
    image: "images/extract_quarantine_files.png"
    critical: true
    
  - name: "图形界面恢复功能正常"
    description: "图形界面恢复隔离文件功能正常"
    image: "images/restore_quarantine_files.png"
    critical: true
    
  - name: "图形界面删除功能正常"
    description: "图形界面删除隔离文件功能正常"
    image: "images/delete_quarantine_files.png"
    critical: true
    
  - name: "命令行列出功能正常"
    description: "命令行能够正确列出隔离文件"
    image: "images/command_list_quarantine.png"
    critical: true
    
  - name: "命令行提取功能正常"
    description: "命令行提取隔离文件功能正常"
    image: "images/command_extract_quarantine.png"
    critical: true
    
  - name: "命令行恢复功能正常"
    description: "命令行恢复隔离文件功能正常"
    image: "images/command_restore_quarantine.png"
    critical: true
```

## 测试数据
```yaml
test_data:
  quarantine_operations:
    gui_operations:
      - "提取文件"
      - "恢复文件"
      - "删除文件"
      - "查看文件信息"
    
    command_operations:
      - "列出隔离文件: mr_utils -l"
      - "提取文件: mr_utils -g id号 -f 目标路径"
      - "恢复文件: mr_utils -r id号"
      
  mr_utils_path:
    windows: "C:\\Program Files (x86)\\mredrclient\\mr_utils.exe"
    linux: "/opt/apps/mredrclient/mr_utils"
    
  sample_commands:
    list_files: "mr_utils -l"
    extract_file: "mr_utils -g 001 -f C:\\temp\\extracted_file.exe"
    restore_file: "mr_utils -r 001"
    
  quarantine_file_info:
    - "文件ID"
    - "原始路径"
    - "文件名"
    - "病毒名称"
    - "隔离时间"
    - "文件大小"
    - "文件哈希"
    
  operation_results:
    extract: "文件成功提取到指定位置"
    restore: "文件成功恢复到原路径"
    delete: "文件从隔离区删除"
    
  test_scenarios:
    - "单个文件操作"
    - "多个文件批量操作"
    - "图形界面操作"
    - "命令行操作"
```
