# AR Linux客户端卸载测试

## 测试信息
```yaml
test_name: "AR Linux客户端手动卸载测试"
test_description: "测试Linux系统上AR客户端的卸载功能"
test_timeout: 300
expected_result: "AR客户端成功卸载，相关文件和服务被清理"
test_environment: "Linux终端"
test_priority: "P0"
test_category: "客户端/安装卸载"
```

## 前置条件
- 终端安装成功Linux客户端

## 测试步骤

### 步骤1：SSH连接或打开终端
**操作描述**：通过SSH连接Linux终端或直接在终端上打开命令行

**目标图片**：
![SSH连接终端](images/ssh_connect_for_uninstall.png)

**操作类型**：连接
**等待时间**：5秒
**预期结果**：成功连接到Linux终端

---

### 步骤2：CentOS/RedHat系统卸载
**操作描述**：对于CentOS、Redhat、Oracle linux、RockyLinuxrelease8.8、SUSE、AnolisOS、麒麟、统信UOS服务器版、OpenEuler客户端执行: sudo rpm -e mredrclient

**目标图片**：
![CentOS卸载命令](images/centos_uninstall_command.png)

**操作类型**：命令执行
**等待时间**：60秒
**预期结果**：rpm卸载命令执行成功

---

### 步骤3：Ubuntu/Debian系统卸载
**操作描述**：对于Ubuntu、统信桌面版执行: sudo dpkg -P mredrclient

**目标图片**：
![Ubuntu卸载命令](images/ubuntu_uninstall_command.png)

**操作类型**：命令执行
**等待时间**：60秒
**预期结果**：dpkg卸载命令执行成功

---

### 步骤4：验证卸载过程
**操作描述**：观察卸载过程的输出信息

**目标图片**：
![卸载过程输出](images/uninstall_process_output.png)

**操作类型**：观察
**等待时间**：30秒
**预期结果**：显示卸载进度和完成信息

---

### 步骤5：验证控制中心终端消失
**操作描述**：检查AR控制中心终端列表中，终端是否消失

**目标图片**：
![控制中心终端消失Linux](images/center_terminal_disappeared_linux.png)

**操作类型**：验证
**等待时间**：30秒
**预期结果**：客户端卸载成功，AR控制中心终端列表中，终端消失

---

### 步骤6：验证Linux文件夹删除
**操作描述**：检查Linux相关文件夹是否被删除

**目标图片**：
![Linux文件夹删除](images/linux_folders_deleted.png)

**操作类型**：验证
**等待时间**：5秒
**预期结果**：Linux相关文件夹被删除

---

### 步骤7：检查安装目录清理
**操作描述**：验证/opt/apps/mredrclient目录是否被删除

**目标图片**：
![安装目录清理](images/install_directory_cleanup_linux.png)

**操作类型**：验证
**等待时间**：5秒
**预期结果**：安装目录被完全清理

---

### 步骤8：检查系统服务清理
**操作描述**：验证mr_mainsvc、mr_enginesvc服务是否被清理

**目标图片**：
![系统服务清理Linux](images/system_services_cleanup_linux.png)

**操作类型**：验证
**等待时间**：5秒
**预期结果**：AR相关的系统服务被清理

---

### 步骤9：检查进程清理
**操作描述**：使用ps命令检查AR相关进程是否被清理

**目标图片**：
![进程清理验证](images/process_cleanup_verification.png)

**操作类型**：验证
**等待时间**：5秒
**预期结果**：AR相关进程被完全清理

---

### 步骤10：检查终端运行日志
**操作描述**：在AR控制中心的终端运行日志中查看相关日志记录

**目标图片**：
![终端运行日志Linux](images/terminal_operation_log_uninstall_linux.png)

**操作类型**：验证
**等待时间**：10秒
**预期结果**：AR控制中心的终端运行日志，有相关日志记录

---

### 步骤11：验证配置文件清理
**操作描述**：检查系统配置文件和日志文件是否被清理

**目标图片**：
![配置文件清理](images/config_files_cleanup.png)

**操作类型**：验证
**等待时间**：5秒
**预期结果**：相关配置文件和日志文件被清理

---

### 步骤12：截图保存测试结果
**操作描述**：对卸载过程和结果进行截图保存

**操作类型**：截图
**保存文件名**：ar_linux_client_uninstall_test_{timestamp}.png
**预期结果**：测试结果截图保存成功

## 测试验证点
```yaml
verification_points:
  - name: "卸载命令正常执行"
    description: "rpm或dpkg卸载命令能够正常执行"
    critical: true
  
  - name: "卸载过程正常完成"
    description: "卸载过程能够正常完成"
    image: "images/uninstall_process_output.png"
    critical: true
  
  - name: "控制中心终端消失"
    description: "卸载后终端从控制中心列表中消失"
    image: "images/center_terminal_disappeared_linux.png"
    critical: true
    
  - name: "安装目录被删除"
    description: "AR客户端安装目录被正确删除"
    image: "images/install_directory_cleanup_linux.png"
    critical: true
    
  - name: "系统服务被清理"
    description: "AR相关的系统服务被正确清理"
    image: "images/system_services_cleanup_linux.png"
    critical: true
    
  - name: "进程被完全清理"
    description: "AR相关进程被完全清理"
    image: "images/process_cleanup_verification.png"
    critical: true
    
  - name: "卸载日志正确记录"
    description: "控制中心正确记录终端卸载日志"
    image: "images/terminal_operation_log_uninstall_linux.png"
    critical: true
```

## 测试数据
```yaml
test_data:
  uninstall_commands:
    centos_redhat: "sudo rpm -e mredrclient"
    ubuntu_debian: "sudo dpkg -P mredrclient"
    
  supported_systems:
    rpm_based:
      - "CentOS"
      - "RedHat"
      - "Oracle Linux"
      - "RockyLinux release 8.8"
      - "SUSE"
      - "AnolisOS"
      - "麒麟"
      - "统信UOS服务器版"
      - "OpenEuler"
    deb_based:
      - "Ubuntu"
      - "统信桌面版"
      
  cleanup_items:
    directories:
      - "/opt/apps/mredrclient"
      - "/var/log/mredrclient"
      - "/etc/mredrclient"
    services:
      - "mr_mainsvc"
      - "mr_enginesvc"
    processes:
      - "mr_mainsvc"
      - "mr_enginesvc"
      - "mredrclient"
      
  verification_commands:
    check_directory: "ls -la /opt/apps/mredrclient"
    check_services: "systemctl status mr_mainsvc mr_enginesvc"
    check_processes: "ps aux | grep mredr"
```
