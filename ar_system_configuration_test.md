# AR系统配置检查测试

## 测试信息
```yaml
test_name: "AR控制中心系统配置检查测试"
test_description: "检查控制中心系统配置的各项设置"
test_timeout: 180
expected_result: "系统配置各项设置显示正确，功能正常"
test_environment: "Windows/Linux控制中心"
test_priority: "P1"
test_category: "中心管理/系统配置"
```

## 前置条件
- 已登录控制中心

## 测试步骤

### 步骤1：登录控制中心
**操作描述**：使用管理员账号登录AR控制中心

**目标图片**：
![登录控制中心](images/login_center_system_config.png)

**操作类型**：登录
**等待时间**：5秒
**预期结果**：成功登录控制中心

---

### 步骤2：进入系统配置
**操作描述**：进入控制中心-中心管理-系统配置

**目标图片**：
![进入系统配置](images/enter_system_configuration.png)

**操作类型**：导航
**等待时间**：3秒
**预期结果**：成功进入系统配置页面

---

### 步骤3：查看中心基本配置
**操作描述**：查看中心基本配置、终端升级配置、开放接口配置、时区设置

**目标图片**：
![中心基本配置](images/center_basic_configuration.png)

**操作类型**：查看
**等待时间**：5秒
**预期结果**：中心基本配置：开启智能通讯优化、通讯间隔默认30秒

---

### 步骤4：查看终端升级配置
**操作描述**：查看终端升级相关配置

**目标图片**：
![终端升级配置](images/terminal_upgrade_configuration.png)

**操作类型**：查看
**等待时间**：5秒
**预期结果**：终端升级配置：查看对应的软件\病毒升级包是否为为最新版本、中心升级包为最新版本

---

### 步骤5：查看开放接口配置
**操作描述**：查看开放中心接口配置

**目标图片**：
![开放接口配置](images/open_interface_configuration.png)

**操作类型**：查看
**等待时间**：5秒
**预期结果**：开放中心接口配置：官方授权为Rose、Appld、AP密钥都是正确的

---

### 步骤6：查看时区设置
**操作描述**：查看系统时区设置

**目标图片**：
![时区设置](images/timezone_settings.png)

**操作类型**：查看
**等待时间**：3秒
**预期结果**：时区设置：为客户对应的地区如：Asia/shanghai

---

### 步骤7：测试配置修改
**操作描述**：尝试修改某些可配置的参数

**目标图片**：
![测试配置修改](images/test_configuration_modification.png)

**操作类型**：配置修改
**等待时间**：10秒
**预期结果**：配置修改功能正常工作

---

### 步骤8：验证配置保存
**操作描述**：验证配置修改是否正确保存

**目标图片**：
![验证配置保存](images/verify_configuration_save.png)

**操作类型**：验证
**等待时间**：5秒
**预期结果**：配置修改正确保存

---

### 步骤9：检查配置生效
**操作描述**：检查修改的配置是否生效

**目标图片**：
![检查配置生效](images/check_configuration_effect.png)

**操作类型**：验证
**等待时间**：30秒
**预期结果**：配置修改正确生效

---

### 步骤10：截图保存测试结果
**操作描述**：对系统配置测试进行截图保存

**操作类型**：截图
**保存文件名**：ar_system_configuration_test_{timestamp}.png
**预期结果**：测试结果截图保存成功

## 测试验证点
```yaml
verification_points:
  - name: "系统配置界面正常"
    description: "系统配置界面能够正常打开"
    image: "images/enter_system_configuration.png"
    critical: true
  
  - name: "中心基本配置正确"
    description: "中心基本配置显示正确"
    image: "images/center_basic_configuration.png"
    critical: true
  
  - name: "终端升级配置正确"
    description: "终端升级配置显示正确"
    image: "images/terminal_upgrade_configuration.png"
    critical: true
    
  - name: "开放接口配置正确"
    description: "开放接口配置显示正确"
    image: "images/open_interface_configuration.png"
    critical: true
    
  - name: "时区设置正确"
    description: "时区设置显示正确"
    image: "images/timezone_settings.png"
    critical: true
    
  - name: "配置修改功能正常"
    description: "配置修改功能正常工作"
    image: "images/test_configuration_modification.png"
    critical: false
```

## 测试数据
```yaml
test_data:
  basic_configuration:
    intelligent_communication: "开启"
    communication_interval: "30秒"
    
  upgrade_configuration:
    software_package: "最新版本"
    virus_database: "最新版本"
    center_package: "最新版本"
    
  interface_configuration:
    official_authorization: "Rose"
    app_id: "正确配置"
    api_key: "正确配置"
    
  timezone_setting:
    default_timezone: "Asia/Shanghai"
    
  configurable_items:
    - "通讯间隔"
    - "智能通讯优化"
    - "时区设置"
    - "接口配置"
    - "升级策略"
    
  expected_values:
    communication_interval: "30"
    timezone: "Asia/Shanghai"
    intelligent_optimization: "启用"
```
