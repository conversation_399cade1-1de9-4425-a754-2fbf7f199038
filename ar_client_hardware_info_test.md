# AR客户端硬件信息显示测试

## 测试信息
```yaml
test_name: "AR客户端硬件信息显示测试"
test_description: "检查AR客户端硬件信息的显示内容"
test_timeout: 120
expected_result: "客户端硬件信息正确完整显示"
test_environment: "Windows/Linux客户端"
test_priority: "P2"
test_category: "客户端功能测试"
```

## 前置条件
- 已安装AR客户端

## 测试步骤

### 步骤1：打开AR客户端
**操作描述**：启动AR客户端应用程序

**目标图片**：
![打开AR客户端](images/open_ar_client_hardware.png)

**操作类型**：启动应用
**等待时间**：5秒
**预期结果**：成功打开AR客户端

---

### 步骤2：进入硬件信息标签页
**操作描述**：查看客户端界面"硬件信息"标签页

**目标图片**：
![硬件信息标签页](images/hardware_info_tab.png)

**操作类型**：点击标签
**等待时间**：3秒
**预期结果**：成功进入硬件信息页面

---

### 步骤3：查看操作系统信息
**操作描述**：查看操作系统相关信息的显示

**目标图片**：
![操作系统信息](images/operating_system_info.png)

**操作类型**：查看
**等待时间**：5秒
**预期结果**：操作系统信息显示正确

---

### 步骤4：查看BIOS信息
**操作描述**：查看BIOS相关信息的显示

**目标图片**：
![BIOS信息](images/bios_info.png)

**操作类型**：查看
**等待时间**：3秒
**预期结果**：BIOS信息显示正确

---

### 步骤5：查看CPU信息
**操作描述**：查看CPU相关信息的显示

**目标图片**：
![CPU信息](images/cpu_info.png)

**操作类型**：查看
**等待时间**：3秒
**预期结果**：CPU信息显示正确

---

### 步骤6：查看内存信息
**操作描述**：查看内存相关信息的显示

**目标图片**：
![内存信息](images/memory_info.png)

**操作类型**：查看
**等待时间**：3秒
**预期结果**：内存信息显示正确

---

### 步骤7：查看硬盘信息
**操作描述**：查看硬盘相关信息的显示

**目标图片**：
![硬盘信息](images/disk_info.png)

**操作类型**：查看
**等待时间**：3秒
**预期结果**：硬盘信息显示正确

---

### 步骤8：查看网卡信息
**操作描述**：查看网卡相关信息的显示

**目标图片**：
![网卡信息](images/network_card_info.png)

**操作类型**：查看
**等待时间**：3秒
**预期结果**：网卡信息显示正确

---

### 步骤9：验证信息完整性
**操作描述**：验证所有硬件信息的完整性和准确性

**目标图片**：
![信息完整性验证](images/hardware_info_completeness.png)

**操作类型**：验证
**等待时间**：10秒
**预期结果**：操作系统、BIOS、CPU、内存、硬盘、网卡等信息显示正确

---

### 步骤10：对比系统实际信息
**操作描述**：对比客户端显示的信息与系统实际信息是否一致

**目标图片**：
![系统信息对比](images/system_info_comparison.png)

**操作类型**：对比验证
**等待时间**：30秒
**预期结果**：客户端显示的硬件信息与系统实际信息一致

---

### 步骤11：截图保存测试结果
**操作描述**：对客户端硬件信息测试进行截图保存

**操作类型**：截图
**保存文件名**：ar_client_hardware_info_test_{timestamp}.png
**预期结果**：测试结果截图保存成功

## 测试验证点
```yaml
verification_points:
  - name: "硬件信息页面正常"
    description: "硬件信息页面能够正常显示"
    image: "images/hardware_info_tab.png"
    critical: true
  
  - name: "操作系统信息正确"
    description: "操作系统信息正确显示"
    image: "images/operating_system_info.png"
    critical: true
  
  - name: "BIOS信息正确"
    description: "BIOS信息正确显示"
    image: "images/bios_info.png"
    critical: true
    
  - name: "CPU信息正确"
    description: "CPU信息正确显示"
    image: "images/cpu_info.png"
    critical: true
    
  - name: "内存信息正确"
    description: "内存信息正确显示"
    image: "images/memory_info.png"
    critical: true
    
  - name: "硬盘信息正确"
    description: "硬盘信息正确显示"
    image: "images/disk_info.png"
    critical: true
    
  - name: "网卡信息正确"
    description: "网卡信息正确显示"
    image: "images/network_card_info.png"
    critical: true
    
  - name: "信息与系统一致"
    description: "显示信息与系统实际信息一致"
    image: "images/system_info_comparison.png"
    critical: true
```

## 测试数据
```yaml
test_data:
  hardware_categories:
    - "操作系统"
    - "BIOS"
    - "CPU"
    - "内存"
    - "硬盘"
    - "网卡"
    
  os_info_fields:
    - "操作系统名称"
    - "操作系统版本"
    - "系统架构"
    - "内核版本"
    
  bios_info_fields:
    - "BIOS厂商"
    - "BIOS版本"
    - "BIOS日期"
    
  cpu_info_fields:
    - "CPU型号"
    - "CPU架构"
    - "核心数量"
    - "线程数量"
    - "主频"
    
  memory_info_fields:
    - "总内存大小"
    - "可用内存"
    - "内存类型"
    
  disk_info_fields:
    - "硬盘型号"
    - "硬盘容量"
    - "硬盘类型"
    - "分区信息"
    
  network_info_fields:
    - "网卡型号"
    - "MAC地址"
    - "IP地址"
    - "网络状态"
    
  verification_commands:
    windows:
      os: "systeminfo"
      cpu: "wmic cpu get name"
      memory: "wmic memorychip get capacity"
      disk: "wmic diskdrive get model,size"
    linux:
      os: "uname -a"
      cpu: "cat /proc/cpuinfo"
      memory: "cat /proc/meminfo"
      disk: "lsblk"
```
