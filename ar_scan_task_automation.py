#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AR扫描任务下发自动化测试程序
基于用户提供的截图和需求

更新日志：
- 2025-08-05: 使用标准终端选择方法，确保准确选择终端复选框
"""

import time
import os
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
import yaml

# 导入标准终端选择工具
from terminal_selector_utils import TerminalSelector

class ARScanTaskTest:
    def __init__(self):
        """初始化测试类"""
        self.driver = None
        self.wait = None
        self.test_results = []
        
        # 测试配置
        self.config = {
            'url': 'http://172.16.103.202:8088',
            'username': 'admin',
            'password': 'Admin.2022',
            'timeout': 10,
            'chrome_driver_path': None
        }
        
        # 创建截图目录
        self.screenshot_dir = "screenshots"
        if not os.path.exists(self.screenshot_dir):
            os.makedirs(self.screenshot_dir)
    
    def setup_driver(self):
        """设置Chrome浏览器驱动"""
        try:
            chrome_options = Options()
            chrome_options.add_argument("--start-maximized")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            if self.config['chrome_driver_path']:
                service = Service(self.config['chrome_driver_path'])
                self.driver = webdriver.Chrome(service=service, options=chrome_options)
            else:
                self.driver = webdriver.Chrome(options=chrome_options)
            
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.wait = WebDriverWait(self.driver, self.config['timeout'])
            
            print("✅ 浏览器驱动设置成功")
            return True
            
        except Exception as e:
            print(f"❌ 浏览器驱动设置失败: {e}")
            return False
    
    def take_screenshot(self, step_name, description=""):
        """截图保存"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{step_name}_{timestamp}.png"
            filepath = os.path.join(self.screenshot_dir, filename)
            self.driver.save_screenshot(filepath)
            print(f"📸 截图保存: {filepath}")
            return filepath
        except Exception as e:
            print(f"❌ 截图失败: {e}")
            return None
    
    def log_result(self, step, status, message, screenshot_path=None):
        """记录测试结果"""
        result = {
            'step': step,
            'status': status,
            'message': message,
            'timestamp': datetime.now().isoformat(),
            'screenshot': screenshot_path
        }
        self.test_results.append(result)
        print(f"📝 步骤{step}: {status} - {message}")
    
    def step1_login(self):
        """步骤1：登录控制中心"""
        try:
            print("\n🔄 执行步骤1：登录控制中心")

            # 打开登录页面
            self.driver.get(self.config['url'])
            time.sleep(10)

            # 截图：登录页面
            screenshot_path = self.take_screenshot("step1_login_page", "登录页面")

            # 尝试多种方式查找用户名输入框
            username_input = None
            username_selectors = [
                (By.NAME, "username"),
                (By.ID, "username"),
                (By.XPATH, "//input[@type='text']"),
                (By.XPATH, "//input[@placeholder*='用户名']"),
                (By.XPATH, "//input[@placeholder*='账号']"),
                (By.CSS_SELECTOR, "input[type='text']")
            ]

            for selector_type, selector_value in username_selectors:
                try:
                    username_input = self.wait.until(
                        EC.element_to_be_clickable((selector_type, selector_value))
                    )
                    print(f"✅ 找到用户名输入框: {selector_type}={selector_value}")
                    break
                except:
                    continue

            if not username_input:
                raise Exception("无法找到用户名输入框")

            username_input.clear()
            username_input.send_keys(self.config['username'])
            time.sleep(3)

            # 尝试多种方式查找密码输入框
            password_input = None
            password_selectors = [
                (By.ID, "password"),
                (By.XPATH, "//input[@type='password']"),
                (By.NAME, "password"),
                (By.XPATH, "//input[@placeholder*='密码']"),
                (By.CSS_SELECTOR, "input[type='password']")
            ]

            for selector_type, selector_value in password_selectors:
                try:
                    password_input = self.driver.find_element(selector_type, selector_value)
                    print(f"✅ 找到密码输入框: {selector_type}={selector_value}")
                    break
                except:
                    continue

            if not password_input:
                raise Exception("无法找到密码输入框")

            password_input.clear()
            password_input.send_keys(self.config['password'])
            time.sleep(3)

            # 尝试多种方式查找登录按钮
            login_button = None
            login_selectors = [
                (By.XPATH, "//button[@type='submit']"),
                (By.XPATH, "//button[contains(text(), '登录')]"),
                (By.XPATH, "//button[contains(text(), '登陆')]"),
                (By.XPATH, "//input[@type='submit']"),
                (By.CSS_SELECTOR, "button[type='submit']"),
                (By.XPATH, "//button")
            ]

            for selector_type, selector_value in login_selectors:
                try:
                    login_button = self.driver.find_element(selector_type, selector_value)
                    print(f"✅ 找到登录按钮: {selector_type}={selector_value}")
                    break
                except:
                    continue

            if not login_button:
                raise Exception("无法找到登录按钮")

            login_button.click()
            time.sleep(20)

            # 检查是否登录成功（URL变化或页面内容变化）
            current_url = self.driver.current_url
            print(f"🔍 当前URL: {current_url}")

            # 截图：登录后的页面
            screenshot_path = self.take_screenshot("step1_login_success", "登录后页面")

            # 简单判断：如果URL包含登录页面特征，说明登录失败
            if "login" in current_url.lower():
                raise Exception("登录失败，仍在登录页面")

            self.log_result(1, "PASS", "成功登录控制中心", screenshot_path)
            return True

        except Exception as e:
            screenshot_path = self.take_screenshot("step1_login_error", "登录失败")
            self.log_result(1, "FAIL", f"登录失败: {str(e)}", screenshot_path)
            return False
    
    def step2_enter_terminal_list(self):
        """步骤2：进入终端列表"""
        try:
            print("\n🔄 执行步骤2：进入终端列表")
            
            # 点击终端管理菜单
            terminal_menu = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, "//span[contains(text(), '终端管理')]"))
            )
            terminal_menu.click()
            time.sleep(2)
            
            # 点击终端列表
            terminal_list = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, "//span[contains(text(), '终端列表')]"))
            )
            terminal_list.click()
            time.sleep(3)
            
            # 验证进入终端列表页面
            if "终端列表" in self.driver.page_source:
                screenshot_path = self.take_screenshot("step2_terminal_list", "进入终端列表")
                self.log_result(2, "PASS", "成功进入终端列表", screenshot_path)
                return True
            else:
                screenshot_path = self.take_screenshot("step2_failed", "进入终端列表失败")
                self.log_result(2, "FAIL", "进入终端列表失败", screenshot_path)
                return False
                
        except Exception as e:
            screenshot_path = self.take_screenshot("step2_error", "进入终端列表异常")
            self.log_result(2, "FAIL", f"进入终端列表过程中发生错误: {str(e)}", screenshot_path)
            return False
    
    def step3_select_terminal_and_scan(self):
        """步骤3：选择终端并执行病毒扫描"""
        try:
            print("\n🔄 执行步骤3：选择终端并执行病毒扫描")
            
            # 等待页面加载
            time.sleep(3)
            
            # 使用标准终端选择器选择一个终端
            print("🎯 使用标准方法选择终端...")

            # 先截图看看当前页面状态
            screenshot_path = self.take_screenshot("step3_before_select", "选择终端前的页面")

            # 创建终端选择器实例
            terminal_selector = TerminalSelector(self.driver, timeout=10)

            # 选择1个终端
            result = terminal_selector.select_terminals(count=1)

            if not result['success']:
                print(f"❌ 终端选择失败: {result['message']}")
                screenshot_path = self.take_screenshot("step3_select_failed", "终端选择失败")
                self.log_result(3, "FAIL", f"终端选择失败: {result['message']}", screenshot_path)
                return False

            print(f"✅ 成功选择终端: {result['message']}")

            # 截图选择后状态
            screenshot_path = self.take_screenshot("step3_terminal_selected", "终端选择成功")

            # 等待一下确保选择生效
            time.sleep(2)
            
            # 查找病毒扫描按钮
            print("🔍 查找病毒扫描按钮...")
            scan_button_selectors = [
                "//button[contains(text(), '病毒扫描')]",
                "//span[contains(text(), '病毒扫描')]//parent::button",
                "//*[contains(text(), '病毒扫描')]"
            ]
            
            scan_button = None
            for selector in scan_button_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    for elem in elements:
                        if elem.is_displayed() and elem.is_enabled():
                            scan_button = elem
                            print(f"✅ 找到病毒扫描按钮")
                            break
                    if scan_button:
                        break
                except:
                    continue
            
            if not scan_button:
                print("❌ 未找到病毒扫描按钮")
                screenshot_path = self.take_screenshot("step3_no_scan_button", "未找到病毒扫描按钮")
                self.log_result(3, "FAIL", "未找到病毒扫描按钮", screenshot_path)
                return False
            
            # 点击病毒扫描按钮
            print("🖱️ 点击病毒扫描按钮...")
            self.driver.execute_script("arguments[0].click();", scan_button)
            time.sleep(3)
            
            screenshot_path = self.take_screenshot("step3_scan_dialog", "病毒扫描对话框")
            self.log_result(3, "PASS", "成功选择终端并打开病毒扫描对话框", screenshot_path)
            return True

        except Exception as e:
            screenshot_path = self.take_screenshot("step3_error", "选择终端和扫描异常")
            self.log_result(3, "FAIL", f"选择终端和扫描过程中发生错误: {str(e)}", screenshot_path)
            return False

    def step4_configure_scan_task(self):
        """步骤4：配置扫描任务（选择快速扫描）"""
        try:
            print("\n🔄 执行步骤4：配置扫描任务")

            # 等待扫描配置对话框出现
            time.sleep(2)

            # 查找快速扫描选项（基于测试结果优化）
            print("🔍 查找快速扫描选项...")
            quick_scan_selectors = [
                "//span[contains(@class, 'el-radio__label') and contains(text(), '快速扫描')]",
                "//span[contains(text(), '快速扫描')]",
                "//label[contains(text(), '快速扫描')]",
                "//*[contains(text(), '快速扫描')]"
            ]

            quick_scan_option = None
            for i, selector in enumerate(quick_scan_selectors):
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    print(f"   选择器 {i+1}: 找到 {len(elements)} 个元素")
                    for j, elem in enumerate(elements):
                        if elem.is_displayed():
                            quick_scan_option = elem
                            print(f"✅ 找到快速扫描选项: 标签={elem.tag_name}, 文本='{elem.text}', 类={elem.get_attribute('class')}")
                            break
                    if quick_scan_option:
                        break
                except Exception as e:
                    print(f"   选择器 {i+1} 失败: {e}")

            if quick_scan_option:
                # 点击快速扫描选项
                print("🖱️ 选择快速扫描...")
                self.driver.execute_script("arguments[0].click();", quick_scan_option)
                time.sleep(1)
                print("✅ 快速扫描选择成功")
            else:
                print("⚠️ 未找到快速扫描选项，使用默认设置")

            # 查找确定按钮（基于测试结果优化）
            print("🔍 查找确定按钮...")
            confirm_selectors = [
                "//button[contains(text(), '确 定')]",  # 注意中间有空格
                "//button[contains(text(), '确定')]",
                "//button[contains(text(), '确认')]",
                "//div[contains(@class, 'el-dialog')]//button[contains(@class, 'el-button--primary')]",
                "//span[contains(text(), '确定')]//parent::button"
            ]

            confirm_button = None
            for i, selector in enumerate(confirm_selectors):
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    print(f"   选择器 {i+1}: 找到 {len(elements)} 个元素")
                    for elem in elements:
                        if elem.is_displayed() and elem.is_enabled():
                            # 确保是在对话框中的按钮
                            try:
                                elem.find_element(By.XPATH, "./ancestor::div[contains(@class, 'el-dialog')]")
                                confirm_button = elem
                                print(f"✅ 找到确定按钮: 文本='{elem.text}', 类={elem.get_attribute('class')}")
                                break
                            except:
                                # 不在对话框中，跳过
                                continue
                    if confirm_button:
                        break
                except Exception as e:
                    print(f"   选择器 {i+1} 失败: {e}")

            if not confirm_button:
                print("❌ 未找到确定按钮")
                screenshot_path = self.take_screenshot("step4_no_confirm", "未找到确定按钮")
                self.log_result(4, "FAIL", "未找到确定按钮", screenshot_path)
                return False

            # 点击确定按钮下发任务
            print("🖱️ 点击确定按钮下发扫描任务...")
            self.driver.execute_script("arguments[0].click();", confirm_button)
            time.sleep(3)

            screenshot_path = self.take_screenshot("step4_task_submitted", "扫描任务已下发")
            self.log_result(4, "PASS", "成功配置并下发扫描任务", screenshot_path)
            return True

        except Exception as e:
            screenshot_path = self.take_screenshot("step4_error", "配置扫描任务异常")
            self.log_result(4, "FAIL", f"配置扫描任务过程中发生错误: {str(e)}", screenshot_path)
            return False

    def step5_view_scan_tasks(self):
        """步骤5：查看扫描任务"""
        try:
            print("\n🔄 执行步骤5：查看扫描任务")

            # 等待任务下发完成
            time.sleep(3)

            # 查找扫描任务菜单
            print("🔍 查找扫描任务菜单...")
            scan_task_selectors = [
                "//span[contains(text(), '扫描任务')]",
                "//a[contains(text(), '扫描任务')]",
                "//*[contains(text(), '扫描任务')]"
            ]

            scan_task_menu = None
            for selector in scan_task_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    for elem in elements:
                        if elem.is_displayed():
                            # 确保不是在对话框中的文本
                            try:
                                parent_dialog = elem.find_element(By.XPATH, "./ancestor::div[contains(@class, 'el-dialog')]")
                                continue  # 跳过对话框中的元素
                            except:
                                pass

                            scan_task_menu = elem
                            print(f"✅ 找到扫描任务菜单")
                            break
                    if scan_task_menu:
                        break
                except:
                    continue

            if not scan_task_menu:
                print("❌ 未找到扫描任务菜单")
                screenshot_path = self.take_screenshot("step5_no_menu", "未找到扫描任务菜单")
                self.log_result(5, "FAIL", "未找到扫描任务菜单", screenshot_path)
                return False

            # 点击扫描任务菜单
            print("🖱️ 点击扫描任务菜单...")
            self.driver.execute_script("arguments[0].click();", scan_task_menu)
            time.sleep(3)

            # 验证进入扫描任务页面
            if "扫描任务" in self.driver.page_source:
                screenshot_path = self.take_screenshot("step5_scan_tasks_page", "扫描任务页面")
                self.log_result(5, "PASS", "成功进入扫描任务页面", screenshot_path)
                return True
            else:
                screenshot_path = self.take_screenshot("step5_failed", "进入扫描任务页面失败")
                self.log_result(5, "FAIL", "进入扫描任务页面失败", screenshot_path)
                return False

        except Exception as e:
            screenshot_path = self.take_screenshot("step5_error", "查看扫描任务异常")
            self.log_result(5, "FAIL", f"查看扫描任务过程中发生错误: {str(e)}", screenshot_path)
            return False

    def step6_view_task_details(self):
        """步骤6：查看任务详情"""
        try:
            print("\n🔄 执行步骤6：查看任务详情")

            # 等待页面加载
            time.sleep(3)

            # 查找最新的扫描任务（通常在第一行）
            print("🔍 查找最新的扫描任务...")
            detail_selectors = [
                "//tbody//tr[1]//button[contains(text(), '详情')]",
                "//tbody//tr[1]//span[contains(text(), '详情')]",
                "//table//tr[1]//button[contains(text(), '详情')]",
                "//tbody//tr[1]//*[contains(text(), '详情')]"
            ]

            detail_button = None
            for selector in detail_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    for elem in elements:
                        if elem.is_displayed() and elem.is_enabled():
                            detail_button = elem
                            print(f"✅ 找到详情按钮")
                            break
                    if detail_button:
                        break
                except:
                    continue

            if not detail_button:
                print("❌ 未找到详情按钮")
                screenshot_path = self.take_screenshot("step6_no_detail", "未找到详情按钮")
                self.log_result(6, "FAIL", "未找到详情按钮", screenshot_path)
                return False

            # 点击详情按钮
            print("🖱️ 点击详情按钮...")
            self.driver.execute_script("arguments[0].click();", detail_button)
            time.sleep(3)

            # 验证任务详情对话框出现
            detail_dialog_selectors = [
                "//div[contains(@class, 'el-dialog')]",
                "//*[contains(text(), '扫描任务详情')]",
                "//*[contains(text(), '任务详情')]"
            ]

            detail_dialog_found = False
            for selector in detail_dialog_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    if elements and any(elem.is_displayed() for elem in elements):
                        detail_dialog_found = True
                        print("✅ 任务详情对话框已打开")
                        break
                except:
                    continue

            if detail_dialog_found:
                screenshot_path = self.take_screenshot("step6_task_details", "任务详情")
                self.log_result(6, "PASS", "成功查看任务详情", screenshot_path)

                # 验证任务详情内容
                self.verify_task_details()
                return True
            else:
                screenshot_path = self.take_screenshot("step6_no_dialog", "任务详情对话框未出现")
                self.log_result(6, "FAIL", "任务详情对话框未出现", screenshot_path)
                return False

        except Exception as e:
            screenshot_path = self.take_screenshot("step6_error", "查看任务详情异常")
            self.log_result(6, "FAIL", f"查看任务详情过程中发生错误: {str(e)}", screenshot_path)
            return False

    def verify_task_details(self):
        """验证任务详情内容"""
        try:
            print("🔍 验证任务详情内容...")

            # 检查任务详情中的关键信息
            page_source = self.driver.page_source

            verification_items = [
                ("终端名称", "DELL2024"),
                ("扫描类型", "快速扫描"),
                ("任务状态", "已下发"),
                ("IP地址", "192.168")
            ]

            verified_items = []
            for item_name, expected_value in verification_items:
                if expected_value in page_source:
                    verified_items.append(item_name)
                    print(f"✅ {item_name}: 包含 '{expected_value}'")
                else:
                    print(f"⚠️ {item_name}: 未找到 '{expected_value}'")

            if len(verified_items) >= 2:  # 至少验证2个项目
                self.log_result("6.1", "PASS", f"任务详情验证通过，已验证: {', '.join(verified_items)}")
            else:
                self.log_result("6.1", "WARN", f"任务详情部分验证通过，已验证: {', '.join(verified_items)}")

        except Exception as e:
            self.log_result("6.1", "FAIL", f"验证任务详情时发生错误: {str(e)}")

    def run_complete_test(self):
        """运行完整的测试流程"""
        print("🚀 开始执行AR扫描任务下发自动化测试")
        print("=" * 60)

        # 设置浏览器
        if not self.setup_driver():
            return False

        try:
            # 执行测试步骤
            steps = [
                ("登录控制中心", self.step1_login),
                ("进入终端列表", self.step2_enter_terminal_list),
                ("选择终端并执行病毒扫描", self.step3_select_terminal_and_scan),
                ("配置扫描任务", self.step4_configure_scan_task),
                ("查看扫描任务", self.step5_view_scan_tasks),
                ("查看任务详情", self.step6_view_task_details)
            ]

            success_count = 0
            for step_name, step_func in steps:
                print(f"\n{'='*20} {step_name} {'='*20}")
                if step_func():
                    success_count += 1
                else:
                    print(f"❌ 步骤失败: {step_name}")
                    break

                # 步骤间等待
                time.sleep(2)

            # 生成测试报告
            self.generate_test_report(success_count, len(steps))

            return success_count == len(steps)

        except Exception as e:
            print(f"❌ 测试执行过程中发生错误: {e}")
            return False
        finally:
            # 清理资源
            if self.driver:
                time.sleep(5)  # 等待5秒以便查看最终结果
                self.driver.quit()
                print("🔚 浏览器已关闭")

    def generate_test_report(self, success_count, total_steps):
        """生成测试报告"""
        print("\n" + "="*60)
        print("📊 测试报告")
        print("="*60)

        print(f"总步骤数: {total_steps}")
        print(f"成功步骤: {success_count}")
        print(f"失败步骤: {total_steps - success_count}")
        print(f"成功率: {(success_count/total_steps)*100:.1f}%")

        print("\n📋 详细结果:")
        for i, result in enumerate(self.test_results, 1):
            status_icon = "✅" if result['status'] == "PASS" else "❌" if result['status'] == "FAIL" else "⚠️"
            print(f"{i:2d}. {status_icon} 步骤{result['step']}: {result['message']}")
            if result['screenshot']:
                print(f"    📸 截图: {result['screenshot']}")

        # 保存测试报告到文件
        report_file = f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("AR扫描任务下发自动化测试报告\n")
            f.write("="*50 + "\n")
            f.write(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"总步骤数: {total_steps}\n")
            f.write(f"成功步骤: {success_count}\n")
            f.write(f"失败步骤: {total_steps - success_count}\n")
            f.write(f"成功率: {(success_count/total_steps)*100:.1f}%\n\n")

            f.write("详细结果:\n")
            for result in self.test_results:
                f.write(f"步骤{result['step']}: {result['status']} - {result['message']}\n")
                f.write(f"时间: {result['timestamp']}\n")
                if result['screenshot']:
                    f.write(f"截图: {result['screenshot']}\n")
                f.write("-" * 30 + "\n")

        print(f"\n📄 测试报告已保存: {report_file}")


def main():
    """主函数"""
    print("🎯 AR扫描任务下发自动化测试程序")
    print("基于用户截图需求开发")
    print("测试流程：选择终端 → 病毒扫描 → 快速扫描 → 确定 → 查看任务 → 查看详情")
    print("-" * 60)

    # 创建测试实例
    test = ARScanTaskTest()

    # 运行测试
    success = test.run_complete_test()

    if success:
        print("\n🎉 所有测试步骤执行成功！")
        return 0
    else:
        print("\n💥 测试执行失败，请查看详细日志和截图")
        return 1


if __name__ == "__main__":
    exit(main())
