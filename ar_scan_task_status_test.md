# AR扫描任务状态检查测试

## 测试信息
```yaml
test_name: "AR控制中心扫描任务状态检查测试"
test_description: "检查控制中心扫描任务的状态显示和详情"
test_timeout: 300
expected_result: "扫描任务状态正确显示，任务详情完整准确"
test_environment: "Windows/Linux控制中心"
test_priority: "P1"
test_category: "终端管理/扫描任务"
```

## 前置条件
- 成功执行过病毒扫描

## 测试步骤

### 步骤1：登录控制中心
**操作描述**：使用管理员账号登录AR控制中心

**目标图片**：
![登录控制中心](images/login_center_scan_task_status.png)

**操作类型**：登录
**等待时间**：5秒
**预期结果**：成功登录控制中心

---

### 步骤2：进入扫描任务
**操作描述**：进入控制中心-终端管理-扫描任务，查看列表与详情

**目标图片**：
![进入扫描任务](images/enter_scan_task_management.png)

**操作类型**：导航
**等待时间**：3秒
**预期结果**：成功进入扫描任务管理页面

---

### 步骤3：查看扫描任务列表
**操作描述**：查看扫描任务列表的显示内容

**目标图片**：
![扫描任务列表](images/scan_task_list.png)

**操作类型**：查看
**等待时间**：5秒
**预期结果**：准确显示所下发的任务、以及执行状态

---

### 步骤4：查看任务基本信息
**操作描述**：查看任务的基本信息（任务ID、创建时间、任务类型等）

**目标图片**：
![任务基本信息](images/task_basic_info.png)

**操作类型**：查看
**等待时间**：3秒
**预期结果**：任务基本信息显示完整

---

### 步骤5：查看目标终端信息
**操作描述**：查看任务的目标终端信息

**目标图片**：
![目标终端信息](images/target_terminal_info.png)

**操作类型**：查看
**等待时间**：3秒
**预期结果**：目标终端信息显示正确

---

### 步骤6：查看任务执行状态
**操作描述**：查看任务的执行状态（待执行、执行中、已完成、失败等）

**目标图片**：
![任务执行状态](images/task_execution_status.png)

**操作类型**：查看
**等待时间**：5秒
**预期结果**：任务执行状态显示准确

---

### 步骤7：查看任务进度信息
**操作描述**：查看正在执行任务的进度信息

**目标图片**：
![任务进度信息](images/task_progress_info.png)

**操作类型**：查看
**等待时间**：10秒
**预期结果**：任务进度信息实时更新

---

### 步骤8：查看任务执行结果
**操作描述**：查看已完成任务的执行结果

**目标图片**：
![任务执行结果](images/task_execution_result.png)

**操作类型**：查看
**等待时间**：5秒
**预期结果**：任务执行结果显示详细

---

### 步骤9：查看任务详细日志
**操作描述**：点击查看任务的详细执行日志

**目标图片**：
![任务详细日志](images/task_detailed_logs.png)

**操作类型**：查看详情
**等待时间**：5秒
**预期结果**：任务详细日志显示完整

---

### 步骤10：筛选任务状态
**操作描述**：使用筛选功能按状态筛选任务

**目标图片**：
![筛选任务状态](images/filter_task_status.png)

**操作类型**：筛选
**等待时间**：5秒
**预期结果**：筛选功能正常工作

---

### 步骤11：搜索特定任务
**操作描述**：使用搜索功能查找特定任务

**目标图片**：
![搜索特定任务](images/search_specific_task.png)

**操作类型**：搜索
**等待时间**：5秒
**预期结果**：搜索功能正常工作

---

### 步骤12：导出任务报告
**操作描述**：导出扫描任务报告（如果有此功能）

**目标图片**：
![导出任务报告](images/export_task_report.png)

**操作类型**：导出
**等待时间**：10秒
**预期结果**：任务报告导出成功

---

### 步骤13：截图保存测试结果
**操作描述**：对扫描任务状态检查测试进行截图保存

**操作类型**：截图
**保存文件名**：ar_scan_task_status_test_{timestamp}.png
**预期结果**：测试结果截图保存成功

## 测试验证点
```yaml
verification_points:
  - name: "扫描任务页面正常"
    description: "扫描任务管理页面能够正常加载"
    image: "images/enter_scan_task_management.png"
    critical: true
  
  - name: "任务列表显示正确"
    description: "扫描任务列表正确显示所有任务"
    image: "images/scan_task_list.png"
    critical: true
  
  - name: "任务状态显示准确"
    description: "任务执行状态显示准确"
    image: "images/task_execution_status.png"
    critical: true
    
  - name: "任务进度实时更新"
    description: "任务进度信息能够实时更新"
    image: "images/task_progress_info.png"
    critical: true
    
  - name: "任务结果显示详细"
    description: "任务执行结果显示详细"
    image: "images/task_execution_result.png"
    critical: true
    
  - name: "任务日志完整"
    description: "任务详细日志显示完整"
    image: "images/task_detailed_logs.png"
    critical: true
    
  - name: "筛选搜索功能正常"
    description: "任务筛选和搜索功能正常"
    image: "images/filter_task_status.png"
    critical: false
```

## 测试数据
```yaml
test_data:
  task_status_types:
    - "待执行"
    - "执行中"
    - "已完成"
    - "执行失败"
    - "已取消"
    
  task_info_fields:
    basic_info:
      - "任务ID"
      - "任务名称"
      - "创建时间"
      - "任务类型"
      - "创建用户"
    
    target_info:
      - "目标终端数量"
      - "终端列表"
      - "分组信息"
    
    execution_info:
      - "开始时间"
      - "结束时间"
      - "执行进度"
      - "当前状态"
    
    result_info:
      - "扫描文件数"
      - "发现威胁数"
      - "处理结果"
      - "错误信息"
      
  filter_options:
    by_status:
      - "全部状态"
      - "待执行"
      - "执行中"
      - "已完成"
      - "失败"
    
    by_type:
      - "快速扫描"
      - "全盘扫描"
      - "自定义扫描"
    
    by_time:
      - "今天"
      - "最近7天"
      - "最近30天"
      
  search_criteria:
    - "任务ID"
    - "任务名称"
    - "目标终端"
    - "创建用户"
    
  progress_indicators:
    - "百分比进度"
    - "当前扫描路径"
    - "已扫描文件数"
    - "剩余时间估算"
```
