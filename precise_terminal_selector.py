#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精确终端选择器
确保只点击终端列表中的复选框，而不是分组树中的复选框
"""

import time
import os
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options

class PreciseTerminalSelector:
    def __init__(self):
        """初始化选择器"""
        self.driver = None
        self.wait = None
        
        # 测试配置
        self.config = {
            'url': 'http://172.16.103.202:8088',
            'username': 'admin',
            'password': 'Admin.2022',
            'timeout': 5
        }
        
        # 创建截图目录
        self.screenshot_dir = "screenshots"
        if not os.path.exists(self.screenshot_dir):
            os.makedirs(self.screenshot_dir)
    
    def setup_driver(self):
        """设置Chrome浏览器驱动"""
        try:
            chrome_options = Options()
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--window-size=1920,1080")
            
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.maximize_window()
            self.wait = WebDriverWait(self.driver, self.config['timeout'])
            
            print("✅ 浏览器驱动设置成功")
            return True
            
        except Exception as e:
            print(f"❌ 浏览器驱动设置失败: {e}")
            return False
    
    def take_screenshot(self, step_name, description=""):
        """截图功能"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{step_name}_{timestamp}.png"
            filepath = os.path.join(self.screenshot_dir, filename)
            self.driver.save_screenshot(filepath)
            print(f"📸 截图已保存: {filepath} - {description}")
            return filepath
        except Exception as e:
            print(f"❌ 截图失败: {e}")
            return None
    
    def login_and_navigate(self):
        """登录并导航到终端列表"""
        try:
            print("\n🔄 登录控制中心...")
            
            # 登录
            self.driver.get(self.config['url'])
            time.sleep(3)
            
            username_input = self.wait.until(EC.element_to_be_clickable((By.NAME, "username")))
            username_input.clear()
            username_input.send_keys(self.config['username'])
            
            password_input = self.driver.find_element(By.ID, "password")
            password_input.clear()
            password_input.send_keys(self.config['password'])
            
            login_button = self.driver.find_element(By.XPATH, "//button[@type='submit']")
            login_button.click()
            time.sleep(5)
            
            # 导航到终端列表
            print("\n🔄 导航到终端列表...")
            
            terminal_menu = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, "//span[contains(text(), '终端管理')]"))
            )
            terminal_menu.click()
            time.sleep(2)
            
            terminal_list = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, "//span[contains(text(), '终端列表')]"))
            )
            terminal_list.click()
            time.sleep(3)
            
            self.take_screenshot("page_loaded", "页面加载完成")
            
            print("✅ 成功登录并导航到终端列表")
            return True
            
        except Exception as e:
            print(f"❌ 登录或导航失败: {e}")
            return False
    
    def analyze_page_structure(self):
        """分析页面结构，区分左侧分组和右侧终端列表"""
        try:
            print("\n🔍 分析页面结构...")
            
            # 1. 查找左侧分组树
            print("\n1️⃣ 分析左侧分组树:")
            group_tree_selectors = [
                "//*[contains(@class, 'el-tree')]",
                "//*[contains(text(), '未分组终端')]",
                "//*[contains(text(), '分组')]//ancestor::div[1]"
            ]
            
            left_area_elements = []
            for selector in group_tree_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    if elements:
                        print(f"   找到分组相关元素: {len(elements)} 个 (选择器: {selector})")
                        left_area_elements.extend(elements)
                except:
                    continue
            
            # 2. 查找右侧终端列表表格
            print("\n2️⃣ 分析右侧终端列表:")
            table_selectors = [
                "//table[contains(@class, 'el-table__body')]",
                "//*[contains(@class, 'el-table__body')]",
                "//tbody"
            ]
            
            table_elements = []
            for selector in table_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    if elements:
                        print(f"   找到表格元素: {len(elements)} 个 (选择器: {selector})")
                        table_elements.extend(elements)
                except:
                    continue
            
            # 3. 分析复选框位置
            print("\n3️⃣ 分析复选框位置:")
            all_checkboxes = self.driver.find_elements(By.XPATH, "//span[contains(@class, 'el-checkbox__inner')]")
            print(f"   总共找到 {len(all_checkboxes)} 个复选框")
            
            terminal_checkboxes = []
            group_checkboxes = []
            
            for i, checkbox in enumerate(all_checkboxes):
                try:
                    if not checkbox.is_displayed():
                        continue
                    
                    # 获取复选框的位置和父级元素
                    location = checkbox.location
                    
                    # 检查是否在表格行中（终端列表的复选框）
                    try:
                        # 查找最近的tr祖先元素
                        tr_ancestor = checkbox.find_element(By.XPATH, "./ancestor::tr[1]")
                        
                        # 检查tr是否包含终端信息
                        tr_text = tr_ancestor.text

                        # 更准确的终端行判断逻辑
                        import re

                        # 判断条件：
                        # 1. 包含IP地址模式
                        # 2. 包含终端状态（在线/离线）
                        # 3. 包含操作系统信息（Windows/Linux）
                        # 4. 不包含"分组"字样

                        ip_pattern = r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}'
                        has_ip = re.search(ip_pattern, tr_text)
                        has_status = '在线' in tr_text or '离线' in tr_text
                        has_os = 'Windows' in tr_text or 'Linux' in tr_text or 'openEuler' in tr_text
                        is_not_group = '分组' not in tr_text and '未分组' not in tr_text

                        # 额外检查：确保在表格主体中（不在表头）
                        try:
                            # 检查是否有多个td单元格（终端行通常有多列）
                            td_cells = tr_ancestor.find_elements(By.TAG_NAME, "td")
                            has_multiple_cells = len(td_cells) >= 5  # 终端列表通常有多列
                        except:
                            has_multiple_cells = False

                        if (has_ip or has_status or has_os) and is_not_group and has_multiple_cells:
                            terminal_checkboxes.append({
                                'element': checkbox,
                                'index': i,
                                'location': location,
                                'row_text': tr_text[:100],
                                'cell_count': len(td_cells) if 'td_cells' in locals() else 0
                            })
                            print(f"   ✅ 终端复选框 {len(terminal_checkboxes)}: 位置{location}, 列数{len(td_cells) if 'td_cells' in locals() else 0}, 内容: {tr_text[:50]}...")
                        else:
                            group_checkboxes.append({
                                'element': checkbox,
                                'index': i,
                                'location': location,
                                'context': tr_text[:50]
                            })
                            print(f"   ⚠️  非终端复选框 {len(group_checkboxes)}: 位置{location}, 内容: {tr_text[:50]}...")
                            
                    except:
                        # 如果没有tr祖先，可能是分组树中的复选框
                        try:
                            # 检查是否在分组树中
                            tree_ancestor = checkbox.find_element(By.XPATH, "./ancestor::*[contains(@class, 'el-tree') or contains(@class, 'tree')]")
                            group_checkboxes.append({
                                'element': checkbox,
                                'index': i,
                                'location': location,
                                'context': '分组树中'
                            })
                            print(f"   ⚠️  分组树复选框 {len(group_checkboxes)}: 位置{location}")
                        except:
                            # 其他位置的复选框
                            parent_text = checkbox.find_element(By.XPATH, "./ancestor::*[1]").text[:50]
                            print(f"   ❓ 未知复选框 {i}: 位置{location}, 父级: {parent_text}...")
                
                except Exception as e:
                    print(f"   ❌ 分析复选框 {i} 失败: {e}")
            
            print(f"\n📊 分析结果:")
            print(f"   终端复选框: {len(terminal_checkboxes)} 个")
            print(f"   分组复选框: {len(group_checkboxes)} 个")
            
            return terminal_checkboxes, group_checkboxes
            
        except Exception as e:
            print(f"❌ 页面结构分析失败: {e}")
            return [], []
    
    def select_terminal_checkboxes(self, terminal_checkboxes, max_select=3):
        """精确选择终端复选框"""
        try:
            print(f"\n🎯 精确选择终端复选框 (最多选择{max_select}个)...")
            
            if not terminal_checkboxes:
                print("❌ 没有找到终端复选框")
                return False
            
            success_count = 0
            
            for i, checkbox_info in enumerate(terminal_checkboxes[:max_select]):
                try:
                    checkbox = checkbox_info['element']
                    row_text = checkbox_info['row_text']
                    
                    print(f"\n选择终端 {i+1}:")
                    print(f"   行内容: {row_text}")
                    
                    # 滚动到复选框可见
                    self.driver.execute_script("arguments[0].scrollIntoView(true);", checkbox)
                    time.sleep(1)
                    
                    # 截图选择前状态
                    self.take_screenshot(f"before_select_terminal_{i+1}", f"选择终端{i+1}前")
                    
                    # 点击复选框
                    self.driver.execute_script("arguments[0].click();", checkbox)
                    time.sleep(1)
                    
                    # 截图选择后状态
                    self.take_screenshot(f"after_select_terminal_{i+1}", f"选择终端{i+1}后")
                    
                    success_count += 1
                    print(f"   ✅ 成功选择终端 {i+1}")
                    
                except Exception as e:
                    print(f"   ❌ 选择终端 {i+1} 失败: {e}")
            
            print(f"\n📊 成功选择了 {success_count} 个终端")
            
            # 最终截图
            self.take_screenshot("final_terminal_selection", "最终终端选择结果")
            
            return success_count > 0
            
        except Exception as e:
            print(f"❌ 选择终端复选框失败: {e}")
            return False
    
    def run_precise_selection(self):
        """运行精确选择"""
        print("🎯 精确终端选择器启动")
        print("=" * 50)
        
        if not self.setup_driver():
            return False
        
        try:
            if not self.login_and_navigate():
                return False
            
            # 分析页面结构
            terminal_checkboxes, group_checkboxes = self.analyze_page_structure()
            
            if not terminal_checkboxes:
                print("❌ 未找到终端复选框")
                return False
            
            # 精确选择终端
            if not self.select_terminal_checkboxes(terminal_checkboxes):
                return False
            
            print("\n🎉 精确选择完成！")
            
            # 保持浏览器打开以便观察结果
            print("浏览器将在30秒后关闭，请观察选择结果...")
            time.sleep(30)
            
            return True
            
        except Exception as e:
            print(f"❌ 精确选择失败: {e}")
            return False
        finally:
            if self.driver:
                self.driver.quit()
                print("🔚 浏览器已关闭")

def main():
    """主函数"""
    selector = PreciseTerminalSelector()
    success = selector.run_precise_selection()
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())
