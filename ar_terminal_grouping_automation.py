#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AR终端分组自动化测试程序
基于 ar_terminal_grouping_test.md 测试用例
"""

import time
import os
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
# from selenium.webdriver.common.keys import Keys  # 暂时不需要
# from selenium.webdriver.common.action_chains import ActionChains  # 暂时不需要
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
import yaml

class ARTerminalGroupingTest:
    def __init__(self):
        """初始化测试类"""
        self.driver = None
        self.wait = None
        self.test_results = []
        
        # 测试配置
        self.config = {
            'url': 'http://**************:8088',
            'username': 'admin',
            'password': 'Admin.2022',
            'timeout': 5,
            'chrome_driver_path': None
        }
        
        # 测试数据
        self.test_groups = {
            'parent_group': '测试父分组',
            'child_group': '测试子分组',
            'edit_group': '编辑测试分组',
            'description': '自动化测试创建的分组'
        }
        
        # 创建截图目录
        self.screenshot_dir = "screenshots"
        if not os.path.exists(self.screenshot_dir):
            os.makedirs(self.screenshot_dir)
    
    def setup_driver(self):
        """设置Chrome浏览器驱动"""
        try:
            chrome_options = Options()
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--window-size=1920,1080")
            
            if self.config['chrome_driver_path']:
                service = Service(self.config['chrome_driver_path'])
                self.driver = webdriver.Chrome(service=service, options=chrome_options)
            else:
                self.driver = webdriver.Chrome(options=chrome_options)
            
            self.driver.maximize_window()
            self.wait = WebDriverWait(self.driver, self.config['timeout'])
            print("✅ 浏览器驱动设置成功")
            
        except Exception as e:
            print(f"❌ 浏览器驱动设置失败: {e}")
            raise
    
    def take_screenshot(self, step_name, description=""):
        """截图功能"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{step_name}_{timestamp}.png"
            filepath = os.path.join(self.screenshot_dir, filename)
            self.driver.save_screenshot(filepath)
            print(f"📸 截图已保存: {filepath} - {description}")
            return filepath
        except Exception as e:
            print(f"❌ 截图失败: {e}")
            return None
    
    def log_result(self, step_num, status, message, screenshot_path=None):
        """记录测试结果"""
        result = {
            'step': step_num,
            'status': status,
            'message': message,
            'screenshot': screenshot_path,
            'timestamp': datetime.now().isoformat()
        }
        self.test_results.append(result)
        
        status_icon = "✅" if status == "PASS" else "❌"
        print(f"{status_icon} 步骤{step_num}: {message}")
    
    def step1_login(self):
        """步骤1：登录控制中心"""
        try:
            print("\n🔄 执行步骤1：登录控制中心")
            
            # 访问登录页面
            self.driver.get(self.config['url'])
            time.sleep(3)
            
            # 截图：登录页面
            screenshot_path = self.take_screenshot("step1_login_page", "登录页面")
            
            # 输入用户名
            username_input = self.wait.until(EC.presence_of_element_located((By.NAME, "username")))
            username_input.clear()
            username_input.send_keys(self.config['username'])
            print(f"✅ 找到用户名输入框: name=username")
            
            # 输入密码
            password_input = self.driver.find_element(By.ID, "password")
            password_input.clear()
            password_input.send_keys(self.config['password'])
            print(f"✅ 找到密码输入框: id=password")
            
            # 点击登录按钮
            login_button = self.driver.find_element(By.XPATH, "//button[@type='submit']")
            login_button.click()
            print(f"✅ 找到登录按钮: xpath=//button[@type='submit']")
            
            # 等待登录完成
            time.sleep(10)
            
            # 验证登录成功
            current_url = self.driver.current_url
            print(f"🔍 当前URL: {current_url}")
            
            if "dashboard" in current_url or "ui" in current_url:
                # 截图：登录后页面
                screenshot_path = self.take_screenshot("step1_login_success", "登录后页面")
                self.log_result(1, "PASS", "成功登录控制中心", screenshot_path)
                return True
            else:
                raise Exception("登录后URL不正确")
                
        except Exception as e:
            screenshot_path = self.take_screenshot("step1_login_error", "登录失败")
            self.log_result(1, "FAIL", f"登录失败: {str(e)}", screenshot_path)
            return False
    
    def step2_enter_terminal_list(self):
        """步骤2：进入终端列表"""
        try:
            print("\n🔄 执行步骤2：进入终端列表")
            
            # 查找并点击终端管理菜单
            terminal_menu_selectors = [
                "//span[contains(text(), '终端管理')]",
                "//a[contains(text(), '终端管理')]",
                "//div[contains(text(), '终端管理')]",
                "//*[contains(text(), '终端')]"
            ]
            
            terminal_menu = None
            for selector in terminal_menu_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    for elem in elements:
                        if elem.is_displayed() and elem.is_enabled():
                            terminal_menu = elem
                            print(f"🎯 找到终端管理菜单: {selector}")
                            break
                    if terminal_menu:
                        break
                except:
                    continue
            
            if not terminal_menu:
                raise Exception("无法找到终端管理菜单")
            
            terminal_menu.click()
            time.sleep(3)
            
            # 查找并点击终端列表
            terminal_list_selectors = [
                "//span[contains(text(), '终端列表')]",
                "//a[contains(text(), '终端列表')]",
                "//div[contains(text(), '终端列表')]",
                "//*[contains(text(), '列表')]"
            ]
            
            terminal_list = None
            for selector in terminal_list_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    for elem in elements:
                        if elem.is_displayed() and elem.is_enabled():
                            terminal_list = elem
                            print(f"🎯 找到终端列表菜单: {selector}")
                            break
                    if terminal_list:
                        break
                except:
                    continue
            
            if terminal_list:
                terminal_list.click()
                time.sleep(5)
            
            # 截图：终端列表页面
            screenshot_path = self.take_screenshot("step2_terminal_list", "终端列表页面")
            
            self.log_result(2, "PASS", "成功进入终端列表页面", screenshot_path)
            return True
            
        except Exception as e:
            screenshot_path = self.take_screenshot("step2_terminal_error", "进入终端列表失败")
            self.log_result(2, "FAIL", f"进入终端列表失败: {str(e)}", screenshot_path)
            return False
    
    def step3_click_group_settings(self):
        """步骤3：点击终端分组设置"""
        try:
            print("\n🔄 执行步骤3：点击终端分组设置")
            
            # 查找分组设置按钮
            group_settings_selectors = [
                "//button[contains(text(), '分组设置')]",
                "//button[contains(text(), '分组管理')]",
                "//a[contains(text(), '分组设置')]",
                "//span[contains(text(), '分组设置')]",
                "//*[contains(text(), '分组')]"
            ]
            
            group_settings_button = None
            for selector in group_settings_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    for elem in elements:
                        if elem.is_displayed() and elem.is_enabled():
                            group_settings_button = elem
                            print(f"🎯 找到分组设置按钮: {selector}")
                            break
                    if group_settings_button:
                        break
                except:
                    continue
            
            if not group_settings_button:
                raise Exception("无法找到分组设置按钮")
            
            group_settings_button.click()
            time.sleep(5)
            
            # 截图：分组设置界面
            screenshot_path = self.take_screenshot("step3_group_settings", "分组设置界面")
            
            self.log_result(3, "PASS", "成功打开分组设置界面", screenshot_path)
            return True
            
        except Exception as e:
            screenshot_path = self.take_screenshot("step3_group_error", "打开分组设置失败")
            self.log_result(3, "FAIL", f"打开分组设置失败: {str(e)}", screenshot_path)
            return False

    def step4_view_existing_groups(self):
        """步骤4：查看现有分组"""
        try:
            print("\n🔄 执行步骤4：查看现有分组")

            # 查找分组列表
            group_list_selectors = [
                "//div[contains(@class, 'group-list')]",
                "//ul[contains(@class, 'group')]",
                "//div[contains(@class, 'tree')]",
                "//div[contains(@class, 'el-tree')]"
            ]

            group_list = None
            for selector in group_list_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    for elem in elements:
                        if elem.is_displayed():
                            group_list = elem
                            print(f"🎯 找到分组列表: {selector}")
                            break
                    if group_list:
                        break
                except:
                    continue

            # 截图：现有分组
            screenshot_path = self.take_screenshot("step4_existing_groups", "查看现有分组")

            self.log_result(4, "PASS", "成功查看现有分组", screenshot_path)
            return True

        except Exception as e:
            screenshot_path = self.take_screenshot("step4_view_error", "查看现有分组失败")
            self.log_result(4, "FAIL", f"查看现有分组失败: {str(e)}", screenshot_path)
            return False

    def step5_create_new_group_and_subgroup(self):
        """步骤5：新建终端分组"""
        try:
            print("\n🔄 执行步骤5：新建终端分组")

            # 查找齿轮设置按钮（在"终端分组"后面的齿轮图标）
            settings_button_selectors = [
                # 更精确地查找齿轮图标
                "//i[contains(@class, 'el-icon-setting')]",
                "//i[contains(@class, 'el-icon-s-tools')]",
                "//i[contains(@class, 'el-icon-s-operation')]",
                "//i[contains(@class, 'fa-cog')]",
                "//i[contains(@class, 'fa-gear')]",
                "//i[contains(@class, 'fa-settings')]",
                # 查找包含齿轮图标的可点击元素
                "//*[contains(@class, 'el-icon-setting')]/..",
                "//*[contains(@class, 'el-icon-s-tools')]/..",
                "//*[contains(@class, 'el-icon-s-operation')]/..",
                # 根据截图，齿轮按钮在"终端分组"文字后面
                "//span[contains(text(), '终端分组')]/following-sibling::*//*[contains(@class, 'icon')]",
                "//div[contains(text(), '终端分组')]/following-sibling::*//*[contains(@class, 'icon')]",
                "//span[contains(text(), '终端分组')]/..//*[contains(@class, 'icon')]",
                "//div[contains(text(), '终端分组')]/..//*[contains(@class, 'icon')]",
                # 通用图标选择器
                "//i[contains(@class, 'icon')]",
                "//*[contains(@class, 'icon') and contains(@class, 'clickable')]",
                "//*[@role='button' and contains(@class, 'icon')]"
            ]

            settings_button = None
            for i, selector in enumerate(settings_button_selectors):
                try:
                    print(f"🔍 尝试齿轮按钮选择器 {i+1}: {selector}")
                    elements = self.driver.find_elements(By.XPATH, selector)
                    print(f"   找到 {len(elements)} 个元素")

                    for j, elem in enumerate(elements):
                        try:
                            tag_name = elem.tag_name
                            is_displayed = elem.is_displayed()
                            is_enabled = elem.is_enabled()
                            class_name = elem.get_attribute("class") or ""
                            title = elem.get_attribute("title") or ""
                            text = elem.text.strip()
                            print(f"   元素{j+1}: <{tag_name}> text='{text}' class='{class_name}' title='{title}' 可见={is_displayed} 可用={is_enabled}")

                            if is_displayed and is_enabled:
                                settings_button = elem
                                print(f"🎯 选择齿轮按钮: <{tag_name}> text='{text}' class='{class_name}'")
                                break
                        except Exception as elem_e:
                            print(f"   元素{j+1}: 检查失败 - {elem_e}")
                            continue

                    if settings_button:
                        break
                except Exception as e:
                    print(f"❌ 选择器 {i+1} 失败: {e}")
                    continue

            if not settings_button:
                # 如果找不到齿轮按钮，尝试查找所有可见的按钮进行分析
                print("🔄 未找到齿轮按钮，分析所有可见按钮...")
                all_buttons = self.driver.find_elements(By.XPATH, "//button | //i | //span[@class*='icon'] | //*[@role='button']")
                print(f"📊 页面上共找到 {len(all_buttons)} 个可能的按钮元素")

                for i, btn in enumerate(all_buttons[:20]):  # 只检查前20个
                    try:
                        if btn.is_displayed():
                            tag_name = btn.tag_name
                            text = btn.text.strip()
                            class_name = btn.get_attribute("class") or ""
                            title = btn.get_attribute("title") or ""
                            onclick = btn.get_attribute("onclick") or ""
                            print(f"   按钮{i+1}: <{tag_name}> text='{text}' class='{class_name}' title='{title}' onclick='{onclick}'")

                            # 尝试通过各种特征识别齿轮按钮
                            if (("setting" in class_name.lower() or "gear" in class_name.lower() or
                                 "config" in class_name.lower() or "cog" in class_name.lower()) and
                                btn.is_enabled()):
                                settings_button = btn
                                print(f"🎯 通过类名特征找到可能的齿轮按钮: <{tag_name}> class='{class_name}'")
                                break
                    except Exception:
                        continue

                if not settings_button:
                    raise Exception("无法找到齿轮设置按钮")

            # 点击齿轮按钮打开下拉菜单
            print("🖱️ 点击齿轮按钮...")
            self.driver.execute_script("arguments[0].click();", settings_button)
            time.sleep(5)  # 增加等待时间让下拉菜单出现

            # 查找"新建分组"选项
            print("🔍 查找'新建分组'选项...")
            new_group_selectors = [
                "//span[contains(text(), '新建分组')]",
                "//*[contains(text(), '新建分组')]",
                "//li[contains(text(), '新建分组')]",
                "//div[contains(text(), '新建分组')]",
                "//a[contains(text(), '新建分组')]",
                # Element UI 下拉菜单项
                "//li[@class='el-dropdown-menu__item' and contains(text(), '新建分组')]",
                "//li[contains(@class, 'dropdown-menu') and contains(text(), '新建分组')]",
                # 更通用的选择器
                "//*[contains(@class, 'menu') and contains(text(), '新建分组')]",
                "//*[contains(@class, 'item') and contains(text(), '新建分组')]"
            ]

            new_group_option = None
            for selector in new_group_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    for elem in elements:
                        if elem.is_displayed() and elem.is_enabled():
                            new_group_option = elem
                            print(f"🎯 找到'新建分组'选项: {selector}")
                            break
                    if new_group_option:
                        break
                except:
                    continue

            if not new_group_option:
                raise Exception("无法找到'新建分组'选项")

            # 点击"新建分组"选项
            print("🖱️ 点击'新建分组'选项...")
            self.driver.execute_script("arguments[0].click();", new_group_option)
            time.sleep(5)  # 等待新建分组对话框完全加载

            # 输入分组名称 - 更精确地定位分组名称输入框
            name_input_selectors = [
                # 根据截图，查找"请输入内容"的输入框
                "//input[@placeholder='请输入内容']",
                "//input[contains(@placeholder, '请输入')]",
                # 查找分组名称标签后面的输入框
                "//span[contains(text(), '分组名称')]/following-sibling::*/input",
                "//span[contains(text(), '分组名称')]/..//input",
                "//label[contains(text(), '分组名称')]/following-sibling::*/input",
                "//label[contains(text(), '分组名称')]/..//input",
                # 在新建分组对话框中查找输入框
                "//div[contains(@class, 'el-dialog')]//input[@placeholder='请输入内容']",
                "//div[contains(@class, 'el-dialog')]//input[@type='text']",
                # 通用选择器
                "//input[@placeholder='请输入分组名称']",
                "//input[contains(@placeholder, '名称')]",
                "//input[@type='text']"
            ]

            name_input = None
            for i, selector in enumerate(name_input_selectors):
                try:
                    print(f"🔍 尝试名称输入框选择器 {i+1}: {selector}")
                    elements = self.driver.find_elements(By.XPATH, selector)
                    print(f"   找到 {len(elements)} 个元素")

                    for j, elem in enumerate(elements):
                        try:
                            tag_name = elem.tag_name
                            is_displayed = elem.is_displayed()
                            is_enabled = elem.is_enabled()
                            placeholder = elem.get_attribute("placeholder") or ""
                            value = elem.get_attribute("value") or ""
                            class_name = elem.get_attribute("class") or ""
                            print(f"   元素{j+1}: <{tag_name}> placeholder='{placeholder}' value='{value}' class='{class_name}' 可见={is_displayed} 可用={is_enabled}")

                            if is_displayed and is_enabled:
                                name_input = elem
                                print(f"🎯 选择名称输入框: <{tag_name}> placeholder='{placeholder}'")
                                break
                        except Exception as elem_e:
                            print(f"   元素{j+1}: 检查失败 - {elem_e}")
                            continue

                    if name_input:
                        break
                except Exception as e:
                    print(f"❌ 选择器 {i+1} 失败: {e}")
                    continue

            if name_input:
                # 使用多种方法确保输入成功（参考白名单测试的成功方法）
                print(f"📝 输入分组名称: {self.test_groups['parent_group']}")

                # 先点击输入框确保获得焦点
                print("🖱️ 点击输入框获得焦点...")
                self.driver.execute_script("arguments[0].focus();", name_input)
                self.driver.execute_script("arguments[0].click();", name_input)
                time.sleep(3)  # 增加等待时间确保输入框获得焦点

                # 方法1：常规输入
                print("🔄 方法1：常规输入...")
                name_input.clear()
                time.sleep(2)  # 增加清空后的等待时间
                name_input.send_keys(self.test_groups['parent_group'])
                time.sleep(3)  # 增加输入后的等待时间

                # 检查输入结果
                current_value = name_input.get_attribute("value")
                print(f"🔍 输入后的值: '{current_value}'")

                if current_value != self.test_groups['parent_group']:
                    print("⚠️ 常规输入失败，尝试JavaScript输入...")

                    # 方法2：JavaScript输入
                    print("🔄 方法2：JavaScript输入...")
                    self.driver.execute_script(f"arguments[0].value = '{self.test_groups['parent_group']}';", name_input)
                    # 触发多种事件确保页面识别到输入
                    self.driver.execute_script("arguments[0].dispatchEvent(new Event('input', { bubbles: true }));", name_input)
                    self.driver.execute_script("arguments[0].dispatchEvent(new Event('change', { bubbles: true }));", name_input)
                    self.driver.execute_script("arguments[0].dispatchEvent(new Event('keyup', { bubbles: true }));", name_input)
                    time.sleep(2)

                    current_value = name_input.get_attribute("value")
                    print(f"🔍 JavaScript输入后的值: '{current_value}'")

                # 最终验证
                final_value = name_input.get_attribute("value")
                print(f"📋 最终输入框的值: '{final_value}'")

                if final_value and final_value == self.test_groups['parent_group']:
                    print(f"✅ 分组名称输入成功！")
                elif final_value:
                    print(f"⚠️ 分组名称输入部分成功: '{final_value}'")
                else:
                    print(f"❌ 分组名称输入失败，输入框仍为空")
                    # 最后尝试：强制设置
                    print("🔄 最后尝试：强制设置...")
                    self.driver.execute_script(f"""
                        arguments[0].value = '{self.test_groups['parent_group']}';
                        arguments[0].setAttribute('value', '{self.test_groups['parent_group']}');
                        arguments[0].dispatchEvent(new Event('input', {{ bubbles: true }}));
                        arguments[0].dispatchEvent(new Event('change', {{ bubbles: true }}));
                    """, name_input)
                    time.sleep(1)
                    final_value = name_input.get_attribute("value")
                    print(f"📋 强制设置后的值: '{final_value}'")

            else:
                print("❌ 未找到分组名称输入框")

            # 跳过复杂的配置，直接保存基本的分组名称
            print("📝 跳过复杂配置，使用默认设置创建分组")

            # 保存分组 - 更精确地定位对话框中的确定按钮
            print("🔍 查找新建分组对话框中的确定按钮...")

            # 先等待一下确保对话框完全加载
            time.sleep(2)

            save_button_selectors = [
                # 最精确的选择器：在新建分组对话框中查找确定按钮
                "//div[contains(@class, 'el-dialog') and .//span[contains(text(), '新建分组')]]//button[contains(text(), '确定')]",
                "//div[contains(@class, 'el-dialog')]//div[contains(@class, 'el-dialog__footer')]//button[contains(text(), '确定')]",
                "//div[contains(@class, 'el-dialog')]//div[contains(@class, 'dialog-footer')]//button[contains(text(), '确定')]",
                # 查找对话框底部的按钮
                "//div[contains(@class, 'el-dialog')]//button[contains(text(), '确定')]",
                "//div[contains(@class, 'el-dialog')]//button[contains(text(), '确 定')]",
                # 通过对话框标题定位
                "//span[contains(text(), '新建分组')]/ancestor::div[contains(@class, 'el-dialog')]//button[contains(text(), '确定')]",
                # 查找绿色按钮，但限制在对话框内
                "//div[contains(@class, 'el-dialog')]//button[contains(@class, 'el-button--primary') and contains(text(), '确定')]",
                "//div[contains(@class, 'el-dialog')]//button[contains(@class, 'el-button--success')]",
                # 通用的对话框按钮查找
                "//div[contains(@class, 'el-dialog')]//button[contains(@class, 'el-button--primary')]",
                # 如果上面都找不到，查找包含确定文字的任何元素
                "//button[contains(text(), '确定')]",
                "//span[contains(text(), '确定')]/parent::button",
                "//*[contains(text(), '确定') and (self::button or parent::button)]"
            ]

            save_button = None
            for i, selector in enumerate(save_button_selectors):
                try:
                    print(f"🔍 尝试确定按钮选择器 {i+1}: {selector}")
                    elements = self.driver.find_elements(By.XPATH, selector)
                    print(f"   找到 {len(elements)} 个元素")

                    for j, elem in enumerate(elements):
                        try:
                            tag_name = elem.tag_name
                            is_displayed = elem.is_displayed()
                            is_enabled = elem.is_enabled()
                            text = elem.text.strip()
                            class_name = elem.get_attribute("class") or ""
                            print(f"   元素{j+1}: <{tag_name}> text='{text}' class='{class_name}' 可见={is_displayed} 可用={is_enabled}")

                            # 确保不是"病毒扫描"等无关按钮
                            if (is_displayed and is_enabled and
                                "病毒扫描" not in text and "扫描" not in text and
                                "查杀" not in text and "防护" not in text):
                                save_button = elem
                                print(f"🎯 选择确定按钮: <{tag_name}> text='{text}' class='{class_name}'")
                                break
                        except Exception as elem_e:
                            print(f"   元素{j+1}: 检查失败 - {elem_e}")
                            continue

                    if save_button:
                        break
                except Exception as e:
                    print(f"❌ 选择器 {i+1} 失败: {e}")
                    continue

            # 如果还是找不到，分析新建分组对话框中的所有按钮
            if not save_button:
                print("🔄 未找到确定按钮，分析新建分组对话框中的所有按钮...")

                # 先查找对话框
                dialog_selectors = [
                    "//div[contains(@class, 'el-dialog')]",
                    "//div[contains(text(), '新建分组')]/ancestor::div[contains(@class, 'dialog')]",
                    "//div[contains(text(), '新建分组')]/ancestor::div[contains(@class, 'modal')]"
                ]

                dialog_element = None
                for selector in dialog_selectors:
                    try:
                        elements = self.driver.find_elements(By.XPATH, selector)
                        for elem in elements:
                            if elem.is_displayed():
                                dialog_element = elem
                                print(f"🎯 找到对话框: {selector}")
                                break
                        if dialog_element:
                            break
                    except:
                        continue

                if dialog_element:
                    # 在对话框中查找所有按钮
                    dialog_buttons = dialog_element.find_elements(By.XPATH, ".//button")
                    print(f"📊 对话框中找到 {len(dialog_buttons)} 个按钮")

                    for i, btn in enumerate(dialog_buttons):
                        try:
                            if btn.is_displayed():
                                text = btn.text.strip()
                                class_name = btn.get_attribute("class") or ""
                                is_enabled = btn.is_enabled()
                                print(f"   对话框按钮{i+1}: text='{text}' class='{class_name}' 可用={is_enabled}")

                                # 查找确定按钮（可能是"确定"、"保存"、"提交"等）
                                if (("确定" in text or "保存" in text or "提交" in text or "完成" in text) and is_enabled):
                                    save_button = btn
                                    print(f"🎯 在对话框中找到确定按钮: text='{text}'")
                                    break
                                # 如果没有文字，但是primary按钮，也可能是确定按钮
                                elif (not text and "primary" in class_name and is_enabled):
                                    save_button = btn
                                    print(f"🎯 在对话框中找到可能的确定按钮（无文字primary按钮）: class='{class_name}'")
                                    break
                        except Exception:
                            continue
                else:
                    # 如果找不到对话框，分析页面上所有按钮
                    print("🔄 未找到对话框，分析页面上所有按钮...")
                    all_buttons = self.driver.find_elements(By.XPATH, "//button")
                    print(f"📊 页面上共找到 {len(all_buttons)} 个按钮")

                    for i, btn in enumerate(all_buttons):
                        try:
                            if btn.is_displayed():
                                text = btn.text.strip()
                                class_name = btn.get_attribute("class") or ""
                                is_enabled = btn.is_enabled()
                                print(f"   按钮{i+1}: text='{text}' class='{class_name}' 可用={is_enabled}")

                                # 查找包含"确定"的按钮，但排除"病毒扫描"等无关按钮
                                if (("确定" in text or "保存" in text) and is_enabled and "病毒扫描" not in text):
                                    save_button = btn
                                    print(f"🎯 通过文字找到确定按钮: text='{text}'")
                                    break
                        except Exception:
                            continue

            if save_button:
                print("🖱️ 点击确定按钮保存分组...")
                # 使用JavaScript点击避免被遮挡
                self.driver.execute_script("arguments[0].click();", save_button)
                time.sleep(20)  # 延长到20秒，确保父分组创建完成

                # 检测页面是否有新的分组出现
                print("🔍 检测新分组是否创建成功...")
                parent_group_created = False

                # 查找刚创建的父分组
                check_selectors = [
                    f"//span[contains(text(), '{self.test_groups['parent_group']}')]",
                    f"//*[contains(text(), '{self.test_groups['parent_group']}')]",
                    f"//div[contains(text(), '{self.test_groups['parent_group']}')]",
                    f"//li[contains(text(), '{self.test_groups['parent_group']}')]"
                ]

                for selector in check_selectors:
                    try:
                        elements = self.driver.find_elements(By.XPATH, selector)
                        for elem in elements:
                            if elem.is_displayed() and self.test_groups['parent_group'] in elem.text:
                                parent_group_created = True
                                print(f"✅ 检测到新分组已创建: '{elem.text.strip()}'")
                                break
                        if parent_group_created:
                            break
                    except:
                        continue

                if parent_group_created:
                    print("✅ 父分组创建成功")
                else:
                    print("⚠️ 未检测到新分组，但可能已创建（页面可能需要刷新）")
                    # 尝试刷新页面或重新查找
                    time.sleep(5)
                    for selector in check_selectors:
                        try:
                            elements = self.driver.find_elements(By.XPATH, selector)
                            for elem in elements:
                                if elem.is_displayed() and self.test_groups['parent_group'] in elem.text:
                                    parent_group_created = True
                                    print(f"✅ 延迟检测到新分组: '{elem.text.strip()}'")
                                    break
                            if parent_group_created:
                                break
                        except:
                            continue

                    if not parent_group_created:
                        print("❌ 父分组创建可能失败，未在页面找到新分组")

            else:
                print("❌ 未找到确定按钮")
                raise Exception("未找到确定按钮")

            # 截图：创建父分组后
            screenshot_path = self.take_screenshot("step5_parent_group_created", "创建父分组")

            # 现在创建子分组
            print("\n🔄 开始创建子分组...")
            time.sleep(5)  # 等待页面更新

            # 先尝试鼠标悬停到父分组上，可能会显示齿轮图标
            print("🖱️ 尝试鼠标悬停到父分组上...")
            parent_group_element = None
            parent_group_selectors = [
                f"//span[contains(text(), '{self.test_groups['parent_group']}')]",
                f"//div[contains(text(), '{self.test_groups['parent_group']}')]",
                f"//*[contains(text(), '{self.test_groups['parent_group']}')]"
            ]

            for selector in parent_group_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    for elem in elements:
                        if elem.is_displayed() and self.test_groups['parent_group'] in elem.text:
                            parent_group_element = elem
                            print(f"🎯 找到父分组元素: {elem.text.strip()}")
                            break
                    if parent_group_element:
                        break
                except:
                    continue

            if parent_group_element:
                # 鼠标悬停到父分组
                from selenium.webdriver.common.action_chains import ActionChains
                actions = ActionChains(self.driver)
                actions.move_to_element(parent_group_element).perform()
                time.sleep(3)  # 等待齿轮图标显示

            # 查找刚创建的父分组后面的齿轮图标
            parent_group_gear_selectors = [
                # 更精确的选择器
                f"//span[contains(text(), '{self.test_groups['parent_group']}')]/following-sibling::i[contains(@class, 'el-icon')]",
                f"//span[contains(text(), '{self.test_groups['parent_group']}')]/parent::*/following-sibling::*//i[contains(@class, 'el-icon')]",
                f"//span[contains(text(), '{self.test_groups['parent_group']}')]/ancestor::li//i[contains(@class, 'el-icon')]",
                f"//div[contains(text(), '{self.test_groups['parent_group']}')]/following-sibling::*//i[contains(@class, 'el-icon')]",
                f"//li[contains(text(), '{self.test_groups['parent_group']}')]//i[contains(@class, 'el-icon')]",
                # 通用的齿轮图标选择器
                f"//*[contains(text(), '{self.test_groups['parent_group']}')]/following-sibling::*//i[contains(@class, 'icon')]",
                f"//*[contains(text(), '{self.test_groups['parent_group']}')]/..//i[contains(@class, 'el-icon')]",
                f"//*[contains(text(), '{self.test_groups['parent_group']}')]/parent::*//i[contains(@class, 'el-icon')]",
                # 查找所有可能的齿轮图标类名
                f"//*[contains(text(), '{self.test_groups['parent_group']}')]/following-sibling::*//i[contains(@class, 'setting')]",
                f"//*[contains(text(), '{self.test_groups['parent_group']}')]/following-sibling::*//i[contains(@class, 'gear')]",
                f"//*[contains(text(), '{self.test_groups['parent_group']}')]/following-sibling::*//i[contains(@class, 'tools')]"
            ]

            parent_gear_button = None
            for i, selector in enumerate(parent_group_gear_selectors):
                try:
                    print(f"🔍 尝试父分组齿轮按钮选择器 {i+1}: {selector}")
                    elements = self.driver.find_elements(By.XPATH, selector)
                    print(f"   找到 {len(elements)} 个元素")

                    for j, elem in enumerate(elements):
                        try:
                            is_displayed = elem.is_displayed()
                            is_enabled = elem.is_enabled()
                            class_name = elem.get_attribute("class") or ""
                            tag_name = elem.tag_name
                            print(f"   元素{j+1}: <{tag_name}> class='{class_name}' 可见={is_displayed} 可用={is_enabled}")

                            # 即使不可见，如果是齿轮图标也尝试使用
                            if is_enabled and ("icon" in class_name.lower() or "setting" in class_name.lower() or "gear" in class_name.lower() or "tools" in class_name.lower()):
                                parent_gear_button = elem
                                print(f"🎯 找到父分组齿轮按钮: <{tag_name}> class='{class_name}' (可见={is_displayed})")
                                break
                        except Exception as elem_e:
                            print(f"   元素{j+1}: 检查失败 - {elem_e}")
                            continue

                    if parent_gear_button:
                        break
                except Exception as e:
                    print(f"❌ 选择器 {i+1} 失败: {e}")
                    continue

            # 如果还是找不到，查找页面上所有齿轮图标
            if not parent_gear_button:
                print("🔄 未找到特定的父分组齿轮按钮，查找页面上所有齿轮图标...")
                all_gear_icons = self.driver.find_elements(By.XPATH, "//i[contains(@class, 'el-icon') and (contains(@class, 'setting') or contains(@class, 'gear') or contains(@class, 'tools'))]")
                print(f"📊 页面上找到 {len(all_gear_icons)} 个齿轮图标")

                for i, icon in enumerate(all_gear_icons):
                    try:
                        class_name = icon.get_attribute("class") or ""
                        is_displayed = icon.is_displayed()
                        is_enabled = icon.is_enabled()
                        print(f"   齿轮图标{i+1}: class='{class_name}' 可见={is_displayed} 可用={is_enabled}")

                        # 尝试找到最后一个可用的齿轮图标（可能是新创建的父分组的）
                        if is_enabled:
                            parent_gear_button = icon
                            print(f"🎯 选择齿轮图标{i+1}: class='{class_name}'")
                    except Exception:
                        continue

            if parent_gear_button:
                # 点击父分组的齿轮按钮
                print("🖱️ 点击父分组的齿轮按钮...")
                self.driver.execute_script("arguments[0].click();", parent_gear_button)
                time.sleep(3)  # 等待菜单出现

                # 查找"添加子分组"选项
                add_child_selectors = [
                    "//span[contains(text(), '添加子分组')]",
                    "//*[contains(text(), '添加子分组')]",
                    "//li[contains(text(), '添加子分组')]",
                    "//div[contains(text(), '添加子分组')]",
                    "//a[contains(text(), '添加子分组')]"
                ]

                add_child_option = None
                for selector in add_child_selectors:
                    try:
                        elements = self.driver.find_elements(By.XPATH, selector)
                        for elem in elements:
                            if elem.is_displayed() and elem.is_enabled():
                                add_child_option = elem
                                print(f"🎯 找到'添加子分组'选项: {selector}")
                                break
                        if add_child_option:
                            break
                    except:
                        continue

                if add_child_option:
                    # 点击"添加子分组"
                    print("🖱️ 点击'添加子分组'选项...")
                    self.driver.execute_script("arguments[0].click();", add_child_option)
                    time.sleep(5)  # 等待子分组对话框加载

                    # 输入子分组名称（使用相同的输入逻辑）
                    child_name_input = None
                    for i, selector in enumerate(name_input_selectors):
                        try:
                            print(f"🔍 尝试子分组名称输入框选择器 {i+1}: {selector}")
                            elements = self.driver.find_elements(By.XPATH, selector)
                            print(f"   找到 {len(elements)} 个元素")

                            for j, elem in enumerate(elements):
                                try:
                                    is_displayed = elem.is_displayed()
                                    is_enabled = elem.is_enabled()
                                    placeholder = elem.get_attribute("placeholder") or ""
                                    value = elem.get_attribute("value") or ""
                                    print(f"   元素{j+1}: placeholder='{placeholder}' value='{value}' 可见={is_displayed} 可用={is_enabled}")

                                    if is_displayed and is_enabled:
                                        child_name_input = elem
                                        print(f"🎯 选择子分组名称输入框: placeholder='{placeholder}'")
                                        break
                                except Exception as elem_e:
                                    print(f"   元素{j+1}: 检查失败 - {elem_e}")
                                    continue

                            if child_name_input:
                                break
                        except Exception as e:
                            print(f"❌ 选择器 {i+1} 失败: {e}")
                            continue

                    if child_name_input:
                        # 输入子分组名称
                        print(f"📝 输入子分组名称: {self.test_groups['child_group']}")
                        self.driver.execute_script("arguments[0].focus();", child_name_input)
                        self.driver.execute_script("arguments[0].click();", child_name_input)
                        time.sleep(2)
                        child_name_input.clear()
                        time.sleep(1)
                        child_name_input.send_keys(self.test_groups['child_group'])
                        time.sleep(2)

                        # 验证输入
                        final_value = child_name_input.get_attribute("value")
                        print(f"📋 子分组名称输入框的值: '{final_value}'")

                        # 查找子分组的确定按钮（使用相同的逻辑）
                        child_save_button = None
                        for i, selector in enumerate(save_button_selectors):
                            try:
                                elements = self.driver.find_elements(By.XPATH, selector)
                                for elem in elements:
                                    if (elem.is_displayed() and elem.is_enabled() and
                                        "病毒扫描" not in elem.text and "扫描" not in elem.text):
                                        child_save_button = elem
                                        print(f"🎯 找到子分组确定按钮: text='{elem.text.strip()}'")
                                        break
                                if child_save_button:
                                    break
                            except:
                                continue

                        if child_save_button:
                            print("🖱️ 点击确定按钮保存子分组...")
                            self.driver.execute_script("arguments[0].click();", child_save_button)
                            time.sleep(20)  # 等待子分组创建完成

                            # 检测子分组是否创建成功
                            print("🔍 检测子分组是否创建成功...")
                            child_group_created = False

                            child_check_selectors = [
                                f"//span[contains(text(), '{self.test_groups['child_group']}')]",
                                f"//*[contains(text(), '{self.test_groups['child_group']}')]",
                                f"//div[contains(text(), '{self.test_groups['child_group']}')]",
                                f"//li[contains(text(), '{self.test_groups['child_group']}')]"
                            ]

                            for selector in child_check_selectors:
                                try:
                                    elements = self.driver.find_elements(By.XPATH, selector)
                                    for elem in elements:
                                        if elem.is_displayed() and self.test_groups['child_group'] in elem.text:
                                            child_group_created = True
                                            print(f"✅ 检测到子分组已创建: '{elem.text.strip()}'")
                                            break
                                    if child_group_created:
                                        break
                                except:
                                    continue

                            if child_group_created:
                                print("✅ 子分组创建成功")
                            else:
                                print("⚠️ 未检测到子分组，但可能已创建")
                        else:
                            print("⚠️ 未找到子分组确定按钮")
                    else:
                        print("⚠️ 未找到子分组名称输入框")
                else:
                    print("⚠️ 未找到'添加子分组'选项")
            else:
                print("⚠️ 未找到父分组的齿轮按钮")

            # 最终截图
            final_screenshot_path = self.take_screenshot("step5_group_created", "创建父分组完成")
            self.log_result(5, "PASS", "成功创建父分组", final_screenshot_path)
            return True

        except Exception as e:
            screenshot_path = self.take_screenshot("step5_create_error", "创建分组失败")
            self.log_result(5, "FAIL", f"创建分组失败: {str(e)}", screenshot_path)
            return False

    def cleanup(self):
        """清理资源"""
        try:
            if self.driver:
                self.driver.quit()
                print("🔚 浏览器已关闭")
        except Exception as e:
            print(f"⚠️ 清理资源时出错: {e}")

    def generate_report(self, success_count, total_count):
        """生成测试报告"""
        try:
            report_data = {
                'test_name': 'AR终端分组功能测试',
                'test_time': datetime.now().isoformat(),
                'total_steps': total_count,
                'passed_steps': success_count,
                'failed_steps': total_count - success_count,
                'success_rate': f"{(success_count/total_count)*100:.1f}%",
                'results': self.test_results
            }

            # 保存为YAML文件
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            report_filename = f"terminal_grouping_test_report_{timestamp}.yaml"

            with open(report_filename, 'w', encoding='utf-8') as f:
                yaml.dump(report_data, f, default_flow_style=False, allow_unicode=True)

            print(f"\n📊 测试报告已生成: {report_filename}")
            print(f"📈 测试结果: {success_count}/{total_count} 步骤通过 ({report_data['success_rate']})")

        except Exception as e:
            print(f"❌ 生成测试报告失败: {e}")

    def run_test(self):
        """运行完整测试"""
        print("🚀 开始执行AR终端分组自动化测试")
        print(f"📋 测试配置: {self.config}")

        try:
            # 设置浏览器驱动
            self.setup_driver()

            # 执行测试步骤
            steps = [
                self.step1_login,
                self.step2_enter_terminal_list,
                self.step3_click_group_settings,
                self.step4_view_existing_groups,
                self.step5_create_new_group_and_subgroup
            ]

            success_count = 0
            for i, step_func in enumerate(steps, 1):
                if step_func():
                    success_count += 1
                else:
                    print(f"❌ 步骤{i}失败，停止后续测试")
                    break

                # 步骤间等待
                time.sleep(1)

            # 生成测试报告
            self.generate_report(success_count, len(steps))

        except Exception as e:
            print(f"❌ 测试执行过程中发生错误: {e}")
        finally:
            # 清理资源
            self.cleanup()

if __name__ == "__main__":
    test = ARTerminalGroupingTest()
    test.run_test()
