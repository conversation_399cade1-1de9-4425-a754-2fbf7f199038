@echo off
chcp 65001 > nul
title E<PERSON><PERSON><PERSON><PERSON> RA<PERSON><PERSON>WA<PERSON> SIMULATION - MAXIMUM COVERAGE

color 0C
echo.
echo ████████████████████████████████████████████████████████████████████████████████
echo ██                                                                            ██
echo ██              ENHANCED RANSOMWARE SIMULATION PROGRAM                        ██
echo ██                        <PERSON><PERSON>IMUM DIRECTORY COVERAGE                          ██
echo ██                                                                            ██
echo ████████████████████████████████████████████████████████████████████████████████
echo.
color 0F

echo ⚠️  CRITICAL WARNING: CO<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> RANS<PERSON>WARE SIMULATION
echo.
echo 🎯 ENHANCED FEATURES:
echo • Scans ALL user directories and subdirectories (up to 2 levels deep)
echo • Targets 50+ directories including Desktop, Documents, Downloads, etc.
echo • Creates 28 different business-themed test directories
echo • Searches for existing files with 40+ file extensions
echo • Encrypts up to 8 existing files per directory
echo • Places ransom notes in EVERY directory (even empty ones)
echo • Covers network drives and external storage
echo • Generates realistic English ransom notes
echo • Creates comprehensive desktop ransom note
echo.
echo 📊 EXPECTED COVERAGE:
echo • User directories: Desktop, Documents, Downloads, Pictures, Videos, Music
echo • Cloud storage: OneDrive, Dropbox, Google Drive, iCloud Drive
echo • System directories: Public folders, ProgramData, Temp directories
echo • Network drives: D:, E:, F:, etc. (if available)
echo • Test directories: Financial_Reports, Customer_Database, HR_Records, etc.
echo.
echo 🔍 SIMULATION WILL:
echo • Create 100+ test files across all directories
echo • Encrypt 200+ existing files (if found)
echo • Deploy 50+ ransom notes in different locations
echo • Generate comprehensive attack statistics
echo.
echo ============================================================
echo                    ENHANCED TEST MODES
echo ============================================================
echo.
echo 1. LIGHT ENHANCED    (2 files/dir, encrypt existing, 50+ directories)
echo 2. STANDARD ENHANCED (3 files/dir, encrypt existing, 50+ directories)  
echo 3. HEAVY ENHANCED    (5 files/dir, encrypt existing, 50+ directories)
echo 4. MAXIMUM ENHANCED  (8 files/dir, encrypt existing, 50+ directories)
echo 5. SAFE ENHANCED     (3 files/dir, NO existing files, 50+ directories)
echo 6. PRESERVE MODE     (Standard + no auto-cleanup)
echo 7. CUSTOM ENHANCED   (Specify parameters)
echo 8. Exit
echo.

set /p choice=Select enhanced test mode (1-8): 

if "%choice%"=="1" (
    echo.
    echo 🚀 LAUNCHING LIGHT ENHANCED MODE...
    echo • Files per directory: 2
    echo • Encrypt existing files: YES (up to 8 per directory)
    echo • Target directories: 50+
    echo • Auto cleanup: YES
    echo.
    echo ⏳ This may take 2-3 minutes to complete...
    echo.
    ransomware_simulator_enhanced.exe --files-per-dir 2 --encrypt-existing
) else if "%choice%"=="2" (
    echo.
    echo 🚀 LAUNCHING STANDARD ENHANCED MODE...
    echo • Files per directory: 3
    echo • Encrypt existing files: YES (up to 8 per directory)
    echo • Target directories: 50+
    echo • Auto cleanup: YES
    echo.
    echo ⏳ This may take 3-4 minutes to complete...
    echo.
    ransomware_simulator_enhanced.exe --files-per-dir 3 --encrypt-existing
) else if "%choice%"=="3" (
    echo.
    echo 🚀 LAUNCHING HEAVY ENHANCED MODE...
    echo • Files per directory: 5
    echo • Encrypt existing files: YES (up to 8 per directory)
    echo • Target directories: 50+
    echo • Auto cleanup: YES
    echo.
    echo ⏳ This may take 4-5 minutes to complete...
    echo.
    ransomware_simulator_enhanced.exe --files-per-dir 5 --encrypt-existing
) else if "%choice%"=="4" (
    echo.
    echo 🚀 LAUNCHING MAXIMUM ENHANCED MODE...
    echo • Files per directory: 8
    echo • Encrypt existing files: YES (up to 8 per directory)
    echo • Target directories: 50+
    echo • Auto cleanup: YES
    echo.
    echo ⏳ This may take 5-7 minutes to complete...
    echo.
    ransomware_simulator_enhanced.exe --files-per-dir 8 --encrypt-existing
) else if "%choice%"=="5" (
    echo.
    echo 🚀 LAUNCHING SAFE ENHANCED MODE...
    echo • Files per directory: 3
    echo • Encrypt existing files: NO (safer option)
    echo • Target directories: 50+
    echo • Auto cleanup: YES
    echo.
    echo ⏳ This may take 2-3 minutes to complete...
    echo.
    ransomware_simulator_enhanced.exe --files-per-dir 3 --no-encrypt-existing
) else if "%choice%"=="6" (
    echo.
    echo 🚀 LAUNCHING PRESERVE MODE...
    echo • Files per directory: 3
    echo • Encrypt existing files: YES (up to 8 per directory)
    echo • Target directories: 50+
    echo • Auto cleanup: NO (files preserved for analysis)
    echo.
    echo ⏳ This may take 3-4 minutes to complete...
    echo.
    ransomware_simulator_enhanced.exe --files-per-dir 3 --encrypt-existing --no-cleanup
) else if "%choice%"=="7" (
    echo.
    set /p filecount=Enter files per directory (1-10): 
    echo.
    echo Choose existing file handling:
    echo 1. Encrypt existing files (more realistic, up to 8 per directory)
    echo 2. Only create new files (safer option)
    set /p existchoice=Enter choice (1-2): 
    
    if "%existchoice%"=="1" (
        echo.
        echo 🚀 LAUNCHING CUSTOM ENHANCED MODE...
        echo • Files per directory: %filecount%
        echo • Encrypt existing files: YES (up to 8 per directory)
        echo • Target directories: 50+
        echo • Auto cleanup: YES
        echo.
        echo ⏳ This may take 3-5 minutes to complete...
        echo.
        ransomware_simulator_enhanced.exe --files-per-dir %filecount% --encrypt-existing
    ) else (
        echo.
        echo 🚀 LAUNCHING CUSTOM ENHANCED MODE...
        echo • Files per directory: %filecount%
        echo • Encrypt existing files: NO
        echo • Target directories: 50+
        echo • Auto cleanup: YES
        echo.
        echo ⏳ This may take 2-4 minutes to complete...
        echo.
        ransomware_simulator_enhanced.exe --files-per-dir %filecount% --no-encrypt-existing
    )
) else if "%choice%"=="8" (
    exit
) else (
    echo.
    echo ❌ Invalid selection, please restart the program
)

echo.
echo ████████████████████████████████████████████████████████████████████████████████
echo                              ENHANCED VERIFICATION CHECKLIST
echo ████████████████████████████████████████████████████████████████████████████████
echo.
echo 🔍 COMPREHENSIVE AR SECURITY VERIFICATION:
echo.
echo ✓ 1. RANSOMWARE DETECTION COVERAGE
echo    • Check detection across ALL affected directories
echo    • Verify detection speed and accuracy
echo    • Confirm threat classification correctness
echo.
echo ✓ 2. FILE PROTECTION EFFECTIVENESS  
echo    • Verify protection across user directories
echo    • Check cloud storage folder protection
echo    • Confirm network drive monitoring
echo.
echo ✓ 3. RANSOM TRAP ACTIVATION
echo    • Check honeypot files in multiple locations
echo    • Verify trap activation across directories
echo    • Confirm malicious process termination
echo.
echo ✓ 4. BEHAVIORAL ANALYSIS DEPTH
echo    • Verify mass file operation detection
echo    • Check pattern recognition across directories
echo    • Confirm behavioral scoring accuracy
echo.
echo ✓ 5. RESPONSE SYSTEM SCALABILITY
echo    • Check response to large-scale attack
echo    • Verify system performance under load
echo    • Confirm alert prioritization
echo.
echo 📂 RANSOM NOTE LOCATIONS TO CHECK:
echo    • Desktop: !!! READ_ME_DECRYPT !!!.txt
echo    • All user directories: README_DECRYPT.txt
echo    • All test directories: README_DECRYPT.txt
echo    • Cloud storage folders: README_DECRYPT.txt
echo    • Network drives: README_DECRYPT.txt
echo.
echo 🔒 ENCRYPTED FILE LOCATIONS:
echo    • User directories (Documents, Desktop, Downloads, etc.)
echo    • Cloud storage directories
echo    • Test business directories
echo    • Network drives and external storage
echo.
echo ████████████████████████████████████████████████████████████████████████████████
pause
