@echo off
chcp 65001 > nul
title AR安全软件勒索模拟程序

echo ============================================================
echo                AR安全软件勒索模拟程序
echo ============================================================
echo.
echo ⚠️  警告: 这是一个用于测试AR安全软件的模拟程序
echo 📋 功能: 模拟勒索软件的完整攻击流程
echo 🎯 目的: 验证AR安全软件的勒索检测和防护能力
echo.
echo 程序将会:
echo • 在多个目录创建测试文件
echo • 模拟文件加密过程
echo • 在每个位置生成勒索信
echo • 创建桌面主勒索信
echo.
echo 请选择测试模式:
echo 1. 轻量测试 (每目录2个文件)
echo 2. 标准测试 (每目录3个文件) 
echo 3. 重度测试 (每目录5个文件)
echo 4. 自定义测试
echo 5. 保留文件模式 (不自动清理)
echo 6. 退出
echo.

set /p choice=请输入选择 (1-6): 

if "%choice%"=="1" (
    echo.
    echo 🚀 启动轻量测试模式...
    ransomware_simulator.exe --files-per-dir 2
) else if "%choice%"=="2" (
    echo.
    echo 🚀 启动标准测试模式...
    ransomware_simulator.exe --files-per-dir 3
) else if "%choice%"=="3" (
    echo.
    echo 🚀 启动重度测试模式...
    ransomware_simulator.exe --files-per-dir 5
) else if "%choice%"=="4" (
    echo.
    set /p filecount=请输入每目录文件数量 (1-10): 
    echo 🚀 启动自定义测试模式...
    ransomware_simulator.exe --files-per-dir %filecount%
) else if "%choice%"=="5" (
    echo.
    echo 🚀 启动保留文件模式...
    ransomware_simulator.exe --files-per-dir 3 --no-cleanup
) else if "%choice%"=="6" (
    exit
) else (
    echo.
    echo ❌ 无效选择，请重新运行程序
)

echo.
echo ============================================================
echo 测试完成！请检查以下项目:
echo 1. AR控制中心是否产生勒索告警
echo 2. 文件实时防护是否触发拦截
echo 3. 勒索诱捕功能是否生效
echo 4. 恶意进程是否被及时终止
echo ============================================================
echo.
pause
