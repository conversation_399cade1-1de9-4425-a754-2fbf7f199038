# RANS<PERSON>WARE BEHAVIOR SIMULATION PROGRAM

## ⚠️ IMPORTANT WARNING
This is a **SIMULATION PROGRAM** designed specifically for testing AR security software. It does **NOT** contain real malware or cause actual harm. All "encryption" is simulated using simple file renaming and base64 encoding.

## 🎯 PURPOSE
- Test AR security software's ransomware detection capabilities
- Verify file protection mechanisms
- Validate ransom trap (honeypot) functionality
- Check behavioral analysis and response systems

## 🚀 QUICK START

### Method 1: Graphical Launcher (Recommended)
Double-click `ransomware_launcher.bat` and follow the menu prompts.

### Method 2: Command Line
```cmd
# Standard test (3 files per directory, encrypt existing files)
ransomware_simulator.exe

# Light test (2 files per directory)
ransomware_simulator.exe --files-per-dir 2

# Safe test (don't encrypt existing files)
ransomware_simulator.exe --no-encrypt-existing

# Preserve files (don't auto-cleanup)
ransomware_simulator.exe --no-cleanup

# Heavy test (5 files per directory)
ransomware_simulator.exe --files-per-dir 5
```

## 📋 SIMULATION FEATURES

### 🔍 Target Discovery
The program will scan and target these locations:
- **User Directories**: Desktop, Documents, Downloads, Pictures, Videos, Music
- **Current Directory**: Where the program is executed
- **Temporary Directories**: System temp folders
- **Created Test Directories**: Multiple subdirectories with business-like names

### 📄 File Operations
1. **Existing File Encryption**: Searches for real files with common extensions (.txt, .doc, .pdf, .jpg, etc.)
2. **Test File Creation**: Creates realistic business files (Financial_Report.xlsx, Customer_Database.docx, etc.)
3. **Simulated Encryption**: Renames files with .encrypted extension and encodes content
4. **Ransom Note Placement**: Creates README_DECRYPT.txt in every affected directory

### 📝 Ransom Notes
- **Language**: English (realistic ransomware language)
- **Content**: Authentic-looking ransom demands with Bitcoin payment, TOR links, threats
- **Locations**: Every directory + main desktop note
- **Filename**: `README_DECRYPT.txt` and `!!! READ_ME_DECRYPT !!!.txt`

## 🎛️ COMMAND LINE OPTIONS

| Option | Description | Default |
|--------|-------------|---------|
| `--files-per-dir N` | Number of test files to create per directory | 3 |
| `--encrypt-existing` | Encrypt existing files found in directories | True |
| `--no-encrypt-existing` | Don't encrypt existing files (safer) | False |
| `--no-cleanup` | Don't automatically delete test files | False |

## 📊 TEST MODES

### 1. Light Test
- **Files per directory**: 2
- **Encrypt existing**: Yes
- **Best for**: Quick functionality check

### 2. Standard Test (Default)
- **Files per directory**: 3
- **Encrypt existing**: Yes
- **Best for**: Regular testing

### 3. Heavy Test
- **Files per directory**: 5
- **Encrypt existing**: Yes
- **Best for**: Stress testing

### 4. Safe Test
- **Files per directory**: 3
- **Encrypt existing**: No
- **Best for**: Testing without touching real files

## 🔍 WHAT TO VERIFY AFTER TESTING

### AR Control Center Checks
- [ ] Ransomware alerts generated
- [ ] Correct threat classification
- [ ] Timeline of events logged
- [ ] Affected file count accuracy

### File Protection Verification
- [ ] Real-time protection triggered
- [ ] File access attempts blocked
- [ ] Encryption process interrupted
- [ ] Original files preserved

### Ransom Trap (Honeypot) Testing
- [ ] Trap files accessed by simulator
- [ ] Malicious process detected
- [ ] Process termination executed
- [ ] Alert generated for trap activation

### Behavioral Analysis
- [ ] Suspicious file operations logged
- [ ] Mass file modification detected
- [ ] Rapid file creation patterns identified
- [ ] Behavioral scoring calculated

### Response Actions
- [ ] Configured policies executed
- [ ] Network isolation triggered (if configured)
- [ ] Administrator notifications sent
- [ ] Incident response workflow initiated

## 📂 FILE LOCATIONS

### Ransom Notes Will Appear In:
- `Desktop/!!! READ_ME_DECRYPT !!!.txt` (Main ransom note)
- `Documents/README_DECRYPT.txt`
- `Downloads/README_DECRYPT.txt`
- `Pictures/README_DECRYPT.txt`
- `Current_Directory/README_DECRYPT.txt`
- `Temp_Directory/README_DECRYPT.txt`
- All created test subdirectories

### Encrypted Files Will Be Found In:
- All above directories with `.encrypted` extension
- Original files will be replaced (in simulation)

## 🧹 CLEANUP

### Automatic Cleanup (Default)
The program automatically removes all test files and ransom notes after completion.

### Manual Cleanup (--no-cleanup)
If you used `--no-cleanup`, manually delete:
- All files with `.encrypted` extension
- All `README_DECRYPT.txt` files
- Desktop `!!! READ_ME_DECRYPT !!!.txt`
- Test directories created in temp folder

## ⚠️ SAFETY NOTES

1. **No Real Encryption**: Files are only renamed and base64 encoded
2. **No Network Activity**: No actual communication with ransomware servers
3. **No System Changes**: No registry modifications or system file changes
4. **Reversible**: All operations can be undone
5. **Test Environment**: Recommended to run in isolated test environment

## 🔧 TROUBLESHOOTING

### Program Won't Start
- Ensure you have administrator privileges
- Check antivirus isn't blocking the executable
- Verify Windows Defender exclusions if needed

### No Files Created
- Check directory permissions
- Ensure sufficient disk space
- Verify user account has write access

### AR Software Not Detecting
- Confirm AR software is running and updated
- Check protection policies are enabled
- Verify real-time protection is active
- Review AR software logs for any errors

## 📞 SUPPORT

For issues with this simulation program:
1. Check AR security software documentation
2. Review AR control center logs
3. Contact AR technical support team
4. Provide simulation logs and AR alert details

---

**Version**: 2.0  
**Last Updated**: 2025-07-30  
**Compatibility**: Windows 10/11, AR Security Software v2.2+
