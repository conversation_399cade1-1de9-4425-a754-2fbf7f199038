#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AR控制中心UI选择器规则库
自动生成时间: 2025-08-05 13:11:44
"""

class ARSelectors:
    """AR控制中心UI选择器"""

    # 通用选择器
    TERMINAL_CHECKBOXES = "//span[contains(@class, 'el-checkbox__inner')]"
    MAIN_TABLE = "//table[contains(@class, 'el-table')]"
    DIALOG = "//div[contains(@class, 'el-dialog')]"
    CONFIRM_BUTTON = "//button[contains(text(), '确 定')]"
    CANCEL_BUTTON = "//button[contains(text(), '取 消')]"

    class 首页:
        """页面: 首页"""
        # 按钮
        最近7天 = "//button[contains(text(), '最近7天')]"
        最近30天 = "//button[contains(text(), '最近30天')]"


    class 终端管理:
        """页面: 终端管理"""
        # 按钮
        最近7天 = "//button[contains(text(), '最近7天')]"
        最近30天 = "//button[contains(text(), '最近30天')]"


    class 终端列表:
        """页面: 终端列表"""
        # 按钮
        病毒扫描 = "//button[contains(text(), '病毒扫描')]"
        终端升级 = "//button[contains(text(), '终端升级')]"
        移动分组 = "//button[contains(text(), '移动分组')]"
        刷新 = "//button[contains(text(), '刷新')]"
        搜索 = "//button[contains(text(), '搜索')]"

        # 输入框
        终端别名IP操作系统 = "//input[@placeholder='终端别名/IP/操作系统']"
        请选择 = "//input[@placeholder='请选择']"
        INPUT_NUMBER = "//input[@type='number']"


    class 扫描任务:
        """页面: 扫描任务"""
        # 按钮
        刷新 = "//button[contains(text(), '刷新')]"
        搜索 = "//button[contains(text(), '搜索')]"
        详情 = "//button[contains(text(), '详情')]"

        # 输入框
        任务序号 = "//input[@placeholder='任务序号']"
        请选择 = "//input[@placeholder='请选择']"
        INPUT_NUMBER = "//input[@type='number']"


    class 升级任务:
        """页面: 升级任务"""
        # 按钮
        刷新 = "//button[contains(text(), '刷新')]"
        搜索 = "//button[contains(text(), '搜索')]"

        # 输入框
        任务序号 = "//input[@placeholder='任务序号']"


    class 计划任务:
        """页面: 计划任务"""
        # 按钮
        创建 = "//button[contains(text(), '创建')]"
        刷新 = "//button[contains(text(), '刷新')]"
        搜索 = "//button[contains(text(), '搜索')]"

        # 输入框
        任务名称 = "//input[@placeholder='任务名称']"


    class 防护策略:
        """页面: 防护策略"""
        # 按钮
        创建 = "//button[contains(text(), '创建')]"
        刷新 = "//button[contains(text(), '刷新')]"
        搜索 = "//button[contains(text(), '搜索')]"

        # 输入框
        任务名称 = "//input[@placeholder='任务名称']"


    class 分组策略:
        """页面: 分组策略"""
        # 按钮
        新增策略 = "//button[contains(text(), '新增策略')]"
        重新分配 = "//button[contains(text(), '重新分配')]"
        取消 = "//button[contains(text(), '取消')]"
        保存 = "//button[contains(text(), '保存')]"

        # 输入框
        请选择 = "//input[@placeholder='请选择']"


    class 白名单:
        """页面: 白名单"""
        # 按钮
        新增 = "//button[contains(text(), '新增')]"
        搜索 = "//button[contains(text(), '搜索')]"
        修改 = "//button[contains(text(), '修改')]"
        删除 = "//button[contains(text(), '删除')]"

        # 输入框
        白名单备注 = "//input[@placeholder='白名单/备注']"
        请选择 = "//input[@placeholder='请选择']"
        INPUT_NUMBER = "//input[@type='number']"


    class 事件日志:
        """页面: 事件日志"""
        # 按钮
        新增 = "//button[contains(text(), '新增')]"
        搜索 = "//button[contains(text(), '搜索')]"
        修改 = "//button[contains(text(), '修改')]"
        删除 = "//button[contains(text(), '删除')]"

        # 输入框
        白名单备注 = "//input[@placeholder='白名单/备注']"
        请选择 = "//input[@placeholder='请选择']"
        INPUT_NUMBER = "//input[@type='number']"


    class 病毒日志:
        """页面: 病毒日志"""
        # 按钮
        刷新 = "//button[contains(text(), '刷新')]"
        搜索 = "//button[contains(text(), '搜索')]"

        # 输入框
        开始日期 = "//input[@placeholder='开始日期']"
        结束日期 = "//input[@placeholder='结束日期']"
        任务序号终端别名病毒名称IP = "//input[@placeholder='任务序号/终端别名/病毒名称/IP']"


    class 文件实时防护日志:
        """页面: 文件实时防护日志"""
        # 按钮
        刷新 = "//button[contains(text(), '刷新')]"
        搜索 = "//button[contains(text(), '搜索')]"

        # 输入框
        开始日期 = "//input[@placeholder='开始日期']"
        结束日期 = "//input[@placeholder='结束日期']"
        终端别名病毒名称IP = "//input[@placeholder='终端别名/病毒名称/IP']"


    class 终端运行日志:
        """页面: 终端运行日志"""
        # 按钮
        刷新 = "//button[contains(text(), '刷新')]"
        搜索 = "//button[contains(text(), '搜索')]"

        # 输入框
        开始日期 = "//input[@placeholder='开始日期']"
        结束日期 = "//input[@placeholder='结束日期']"
        请选择功能模块 = "//input[@placeholder='请选择功能模块']"
        终端别名IP = "//input[@placeholder='终端别名/IP']"
        请选择 = "//input[@placeholder='请选择']"
        INPUT_NUMBER = "//input[@type='number']"


    class 中心操作日志:
        """页面: 中心操作日志"""
        # 按钮
        刷新 = "//button[contains(text(), '刷新')]"
        搜索 = "//button[contains(text(), '搜索')]"

        # 输入框
        开始日期 = "//input[@placeholder='开始日期']"
        结束日期 = "//input[@placeholder='结束日期']"
        请选择功能模块 = "//input[@placeholder='请选择功能模块']"
        请选择 = "//input[@placeholder='请选择']"
        INPUT_NUMBER = "//input[@type='number']"


