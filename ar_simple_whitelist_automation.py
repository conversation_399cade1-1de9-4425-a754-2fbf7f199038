#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AR简单白名单创建自动化测试程序
基于 ar_simple_whitelist_create_test.md 测试用例
"""

import time
import os
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
import yaml

class ARWhitelistTest:
    def __init__(self, config_file="test_config.yaml"):
        """初始化测试类"""
        self.driver = None
        self.wait = None
        self.config = self.load_config(config_file)
        self.test_results = []
        self.screenshot_dir = "screenshots"
        self.created_rule_path = None  # 记录创建的规则路径，用于后续修改和删除

        # 添加备注信息用于搜索测试
        self.path_remark = "文件路径白名单测试备注"
        self.hash_remark = "文件哈希白名单测试备注"

        # 添加备注信息用于搜索测试
        self.path_remark = "文件路径白名单测试备注"
        self.hash_remark = "文件哈希白名单测试备注"
        
        # 创建截图目录
        if not os.path.exists(self.screenshot_dir):
            os.makedirs(self.screenshot_dir)
    
    def load_config(self, config_file):
        """加载测试配置"""
        default_config = {
            'url': 'http://172.16.103.202:8088',
            'username': 'admin',
            'password': 'Admin.2022',
            'test_path': 'D:\\泯然\\auto_test',
            'modified_path': 'D:\\泯然\\auto_test\\modified',
            'test_file': 'D:\\泯然\\auto_test\\test_file.txt',  # 用于哈希白名单的测试文件
            'timeout': 5,
            'chrome_driver_path': None  # 如果为None，使用系统PATH中的chromedriver
        }
        
        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                user_config = yaml.safe_load(f)
                default_config.update(user_config)
        
        return default_config
    
    def setup_driver(self):
        """设置Chrome浏览器驱动"""
        chrome_options = Options()
        # 基本稳定性设置
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--disable-extensions')
        chrome_options.add_argument('--disable-plugins')
        chrome_options.add_argument('--disable-images')  # 禁用图片加载
        # chrome_options.add_argument('--disable-javascript')  # 注释掉，需要JS支持
        chrome_options.add_argument('--window-size=1920,1080')
        chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')

        # 内存和性能优化
        chrome_options.add_argument('--memory-pressure-off')
        chrome_options.add_argument('--max_old_space_size=4096')
        chrome_options.add_argument('--disable-background-timer-throttling')
        chrome_options.add_argument('--disable-renderer-backgrounding')
        chrome_options.add_argument('--disable-backgrounding-occluded-windows')

        if self.config['chrome_driver_path']:
            service = Service(self.config['chrome_driver_path'])
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
        else:
            self.driver = webdriver.Chrome(options=chrome_options)

        self.wait = WebDriverWait(self.driver, self.config['timeout'])
        self.driver.maximize_window()
    
    def take_screenshot(self, step_name, description=""):
        """截图并保存"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{step_name}_{timestamp}.png"
        filepath = os.path.join(self.screenshot_dir, filename)
        
        self.driver.save_screenshot(filepath)
        print(f"📸 截图已保存: {filepath} - {description}")
        return filepath
    
    def log_result(self, step, status, message, screenshot_path=None):
        """记录测试结果"""
        result = {
            'step': step,
            'status': status,
            'message': message,
            'timestamp': datetime.now().isoformat(),
            'screenshot': screenshot_path
        }
        self.test_results.append(result)
        
        status_icon = "✅" if status == "PASS" else "❌"
        print(f"{status_icon} 步骤{step}: {message}")
    
    def step1_login(self):
        """步骤1：登录控制中心"""
        try:
            print("\n🔄 执行步骤1：登录控制中心")

            # 打开登录页面
            self.driver.get(self.config['url'])
            time.sleep(10)

            # 截图：登录页面
            screenshot_path = self.take_screenshot("step1_login_page", "登录页面")

            # 尝试多种方式查找用户名输入框
            username_input = None
            username_selectors = [
                (By.NAME, "username"),
                (By.ID, "username"),
                (By.XPATH, "//input[@type='text']"),
                (By.XPATH, "//input[@placeholder*='用户名']"),
                (By.XPATH, "//input[@placeholder*='账号']"),
                (By.CSS_SELECTOR, "input[type='text']")
            ]

            for selector_type, selector_value in username_selectors:
                try:
                    username_input = self.wait.until(
                        EC.element_to_be_clickable((selector_type, selector_value))
                    )
                    print(f"✅ 找到用户名输入框: {selector_type}={selector_value}")
                    break
                except:
                    continue

            if not username_input:
                raise Exception("无法找到用户名输入框")

            username_input.clear()
            username_input.send_keys(self.config['username'])
            time.sleep(3)

            # 尝试多种方式查找密码输入框
            password_input = None
            password_selectors = [
                (By.ID, "password"),
                (By.XPATH, "//input[@type='password']"),
                (By.NAME, "password"),
                (By.XPATH, "//input[@placeholder*='密码']"),
                (By.CSS_SELECTOR, "input[type='password']")
            ]

            for selector_type, selector_value in password_selectors:
                try:
                    password_input = self.driver.find_element(selector_type, selector_value)
                    print(f"✅ 找到密码输入框: {selector_type}={selector_value}")
                    break
                except:
                    continue

            if not password_input:
                raise Exception("无法找到密码输入框")

            password_input.clear()
            password_input.send_keys(self.config['password'])
            time.sleep(3)

            # 尝试多种方式查找登录按钮
            login_button = None
            login_selectors = [
                (By.XPATH, "//button[@type='submit']"),
                (By.XPATH, "//button[contains(text(), '登录')]"),
                (By.XPATH, "//button[contains(text(), '登陆')]"),
                (By.XPATH, "//input[@type='submit']"),
                (By.CSS_SELECTOR, "button[type='submit']"),
                (By.XPATH, "//button")
            ]

            for selector_type, selector_value in login_selectors:
                try:
                    login_button = self.driver.find_element(selector_type, selector_value)
                    print(f"✅ 找到登录按钮: {selector_type}={selector_value}")
                    break
                except:
                    continue

            if not login_button:
                raise Exception("无法找到登录按钮")

            login_button.click()
            time.sleep(20)

            # 检查是否登录成功（URL变化或页面内容变化）
            current_url = self.driver.current_url
            print(f"🔍 当前URL: {current_url}")

            # 截图：登录后的页面
            screenshot_path = self.take_screenshot("step1_login_success", "登录后页面")

            # 简单判断：如果URL包含登录页面特征，说明登录失败
            if "login" in current_url.lower():
                raise Exception("登录失败，仍在登录页面")

            self.log_result(1, "PASS", "成功登录控制中心", screenshot_path)
            return True

        except Exception as e:
            screenshot_path = self.take_screenshot("step1_login_error", "登录失败")
            self.log_result(1, "FAIL", f"登录失败: {str(e)}", screenshot_path)
            return False
    
    def step2_enter_whitelist(self):
        """步骤2：进入白名单管理"""
        try:
            print("\n🔄 执行步骤2：进入白名单管理")
            
            # 查找并点击防护策略菜单
            policy_menu = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, "//span[contains(text(), '防护策略')]"))
            )
            policy_menu.click()
            time.sleep(5)

            # 查找并点击白名单子菜单
            whitelist_menu = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, "//span[contains(text(), '白名单')]"))
            )
            whitelist_menu.click()
            time.sleep(10)
            
            # 截图：白名单管理页面
            screenshot_path = self.take_screenshot("step2_whitelist_page", "白名单管理页面")
            
            self.log_result(2, "PASS", "成功进入白名单管理页面", screenshot_path)
            return True
            
        except Exception as e:
            screenshot_path = self.take_screenshot("step2_whitelist_error", "进入白名单失败")
            self.log_result(2, "FAIL", f"进入白名单管理失败: {str(e)}", screenshot_path)
            return False
    
    def step3_click_add(self):
        """步骤3：点击添加白名单"""
        try:
            print("\n🔄 执行步骤3：点击添加白名单")

            # 先截图当前页面状态
            screenshot_path = self.take_screenshot("step3_before_add", "添加前的白名单页面")

            # 根据实际测试，按钮文字是"新增"
            add_button = None
            add_selectors = [
                # 最精确的选择器 - 找到了"新增"按钮
                (By.XPATH, "//button[contains(text(), '新增')]"),
                (By.XPATH, "//button[text()='新增']"),
                # 备用选择器
                (By.XPATH, "//button[contains(text(), '新建')]"),
                (By.XPATH, "//button[contains(text(), '添加')]"),
                (By.XPATH, "//*[contains(text(), '新增')]"),
                (By.XPATH, "//*[contains(text(), '新建')]"),
            ]

            for i, (selector_type, selector_value) in enumerate(add_selectors):
                try:
                    print(f"🔍 尝试选择器 {i+1}: {selector_type}={selector_value}")
                    elements = self.driver.find_elements(selector_type, selector_value)

                    if elements:
                        print(f"✅ 找到 {len(elements)} 个匹配元素")
                        for j, elem in enumerate(elements):
                            try:
                                text = elem.text.strip()
                                is_displayed = elem.is_displayed()
                                is_enabled = elem.is_enabled()
                                print(f"   元素{j+1}: 文字='{text}' 可见={is_displayed} 可用={is_enabled}")

                                if is_displayed and is_enabled and ("添加" in text or "新建" in text or "新增" in text or not text):
                                    add_button = elem
                                    print(f"🎯 选择元素{j+1}作为添加按钮")
                                    break
                            except Exception as elem_e:
                                print(f"   元素{j+1}: 检查失败 - {elem_e}")
                                continue

                    if add_button:
                        break

                except Exception as e:
                    print(f"❌ 选择器 {i+1} 失败: {e}")
                    continue

            if not add_button:
                raise Exception("无法找到添加按钮")

            # 点击添加按钮
            print("🖱️ 点击添加按钮...")
            self.driver.execute_script("arguments[0].scrollIntoView(true);", add_button)
            time.sleep(2)
            add_button.click()
            time.sleep(10)

            # 截图：添加白名单对话框
            screenshot_path = self.take_screenshot("step3_add_dialog", "添加白名单对话框")

            self.log_result(3, "PASS", "成功打开添加白名单对话框", screenshot_path)
            return True

        except Exception as e:
            screenshot_path = self.take_screenshot("step3_add_error", "点击添加失败")
            self.log_result(3, "FAIL", f"点击添加白名单失败: {str(e)}", screenshot_path)
            return False
    
    def step4_select_path_type(self):
        """步骤4：确认路径类型已选择"""
        try:
            print("\n🔄 执行步骤4：确认路径类型已选择")

            # 根据截图，默认已经选择了"文件路径"，我们只需要确认
            # 查找"文件路径"选项是否已选中
            try:
                # 查找文件路径的单选按钮或选项
                path_option = self.driver.find_element(By.XPATH, "//*[contains(text(), '文件路径')]")
                print("✅ 找到文件路径选项")

                # 检查是否已选中（通常已选中的会有特殊样式）
                parent = path_option.find_element(By.XPATH, "./..")
                classes = parent.get_attribute("class")
                print(f"🔍 文件路径选项的class: {classes}")

            except Exception as e:
                print(f"⚠️ 无法确认文件路径选项状态: {e}")

            time.sleep(3)

            # 截图：确认路径类型
            screenshot_path = self.take_screenshot("step4_confirm_type", "确认路径类型")

            self.log_result(4, "PASS", "文件路径类型默认已选择", screenshot_path)
            return True

        except Exception as e:
            screenshot_path = self.take_screenshot("step4_select_error", "确认类型失败")
            self.log_result(4, "FAIL", f"确认路径类型失败: {str(e)}", screenshot_path)
            return False
    
    def step5_input_path_and_remark(self):
        """步骤5：输入文件路径和备注"""
        try:
            print("\n🔄 执行步骤5：输入文件路径和备注")

            # 根据截图，查找文件路径输入框
            path_input = None
            path_selectors = [
                # 根据截图中的提示文字查找
                (By.XPATH, "//input[contains(@placeholder, 'C:')]"),
                (By.XPATH, "//input[contains(@placeholder, '例如')]"),
                (By.XPATH, "//input[contains(@placeholder, 'abc.exe')]"),
                (By.XPATH, "//input[contains(@placeholder, 'temp')]"),
                # 通用选择器
                (By.XPATH, "//input[@type='text']"),
                (By.XPATH, "//input[not(@type)]"),
                (By.CSS_SELECTOR, "input[type='text']"),
                (By.CSS_SELECTOR, "input"),
            ]

            for i, (selector_type, selector_value) in enumerate(path_selectors):
                try:
                    print(f"🔍 尝试路径输入框选择器 {i+1}: {selector_type}={selector_value}")
                    elements = self.driver.find_elements(selector_type, selector_value)

                    if elements:
                        print(f"✅ 找到 {len(elements)} 个输入框")
                        for j, elem in enumerate(elements):
                            try:
                                placeholder = elem.get_attribute("placeholder")
                                is_displayed = elem.is_displayed()
                                is_enabled = elem.is_enabled()
                                tag_name = elem.tag_name
                                print(f"   输入框{j+1}: <{tag_name}> placeholder='{placeholder}' 可见={is_displayed} 可用={is_enabled}")

                                # 优先选择有路径相关placeholder的输入框
                                if is_displayed and is_enabled and placeholder and ("C:" in placeholder or "temp" in placeholder or "abc" in placeholder):
                                    path_input = elem
                                    print(f"🎯 选择输入框{j+1}作为路径输入框（匹配placeholder）")
                                    break
                                elif is_displayed and is_enabled and not path_input:
                                    path_input = elem
                                    print(f"🎯 选择输入框{j+1}作为备选路径输入框")

                            except Exception as elem_e:
                                print(f"   输入框{j+1}: 检查失败 - {elem_e}")
                                continue

                    if path_input:
                        break

                except Exception as e:
                    print(f"❌ 选择器 {i+1} 失败: {e}")
                    continue

            if not path_input:
                raise Exception("无法找到路径输入框")

            # 输入路径 - 使用多种方法确保输入成功
            print(f"📝 输入路径: {self.config['test_path']}")

            # 先点击输入框确保获得焦点
            print("🖱️ 点击输入框获得焦点...")
            self.driver.execute_script("arguments[0].focus();", path_input)
            path_input.click()
            time.sleep(1)

            # 方法1：常规输入
            print("🔄 方法1：常规输入...")
            path_input.clear()
            time.sleep(1)
            path_input.send_keys(self.config['test_path'])
            time.sleep(3)

            # 检查输入结果
            current_value = path_input.get_attribute("value")
            print(f"🔍 输入后的值: '{current_value}'")

            if current_value != self.config['test_path']:
                print("⚠️ 常规输入失败，尝试其他方法...")

                # 方法2：JavaScript输入
                print("🔄 方法2：JavaScript输入...")
                self.driver.execute_script(f"arguments[0].value = '{self.config['test_path']}';", path_input)
                # 触发多种事件确保页面识别到输入
                self.driver.execute_script("arguments[0].dispatchEvent(new Event('input', { bubbles: true }));", path_input)
                self.driver.execute_script("arguments[0].dispatchEvent(new Event('change', { bubbles: true }));", path_input)
                self.driver.execute_script("arguments[0].dispatchEvent(new Event('keyup', { bubbles: true }));", path_input)
                time.sleep(2)

                current_value = path_input.get_attribute("value")
                print(f"🔍 JavaScript输入后的值: '{current_value}'")

                if current_value != self.config['test_path']:
                    # 方法3：逐字符输入
                    print("🔄 尝试逐字符输入...")
                    path_input.clear()
                    time.sleep(1)
                    for char in self.config['test_path']:
                        path_input.send_keys(char)
                        time.sleep(0.05)

                    current_value = path_input.get_attribute("value")
                    print(f"🔍 逐字符输入后的值: '{current_value}'")

            # 最终验证
            final_value = path_input.get_attribute("value")
            print(f"📋 最终输入框的值: '{final_value}'")

            if final_value and final_value == self.config['test_path']:
                print(f"✅ 路径输入成功！")
            elif final_value:
                print(f"⚠️ 路径输入部分成功: '{final_value}'")
            else:
                print(f"❌ 路径输入失败，输入框仍为空")
                # 最后尝试：强制设置
                print("🔄 最后尝试：强制设置...")
                self.driver.execute_script(f"""
                    arguments[0].value = '{self.config['test_path']}';
                    arguments[0].setAttribute('value', '{self.config['test_path']}');
                    arguments[0].dispatchEvent(new Event('input', {{ bubbles: true }}));
                    arguments[0].dispatchEvent(new Event('change', {{ bubbles: true }}));
                """, path_input)
                time.sleep(1)
                final_value = path_input.get_attribute("value")
                print(f"📋 强制设置后的值: '{final_value}'")

            time.sleep(3)

            # 截图：输入文件路径
            screenshot_path = self.take_screenshot("step5_input_path", "输入文件路径")

            # 输入备注
            print(f"📝 输入备注: {self.path_remark}")
            try:
                # 查找备注输入框
                remark_input = None
                remark_selectors = [
                    # 根据截图，备注输入框在"备注"标签后面
                    "//label[contains(text(), '备注')]/following-sibling::*/input",
                    "//span[contains(text(), '备注')]/following-sibling::*/input",
                    "//div[contains(text(), '备注')]/following-sibling::*/input",
                    "//label[contains(text(), '备注')]/..//input",
                    "//span[contains(text(), '备注')]/..//input",
                    "//div[contains(text(), '备注')]/..//input",
                    # 通用选择器
                    "//textarea[@placeholder='请输入备注']",
                    "//textarea[contains(@placeholder, '备注')]",
                    "//input[@placeholder='请输入备注']",
                    "//input[contains(@placeholder, '备注')]",
                    # 最后尝试所有可见的输入框
                    "//input[@type='text']",
                    "//textarea"
                ]

                for i, selector in enumerate(remark_selectors):
                    try:
                        print(f"🔍 尝试备注输入框选择器 {i+1}: {selector}")
                        elements = self.driver.find_elements(By.XPATH, selector)
                        print(f"   找到 {len(elements)} 个元素")

                        for j, elem in enumerate(elements):
                            try:
                                placeholder = elem.get_attribute("placeholder") or ""
                                tag_name = elem.tag_name
                                is_displayed = elem.is_displayed()
                                is_enabled = elem.is_enabled()
                                class_name = elem.get_attribute("class") or ""
                                print(f"   元素{j+1}: <{tag_name}> placeholder='{placeholder}' class='{class_name}' 可见={is_displayed} 可用={is_enabled}")

                                if is_displayed and is_enabled:
                                    remark_input = elem
                                    print(f"🎯 选择备注输入框: <{tag_name}> placeholder='{placeholder}'")
                                    break
                            except Exception as elem_e:
                                print(f"   元素{j+1}: 检查失败 - {elem_e}")
                                continue

                        if remark_input:
                            break
                    except Exception as e:
                        print(f"❌ 选择器 {i+1} 失败: {e}")
                        continue

                if remark_input:
                    # 先点击获得焦点
                    remark_input.click()
                    time.sleep(1)

                    # 清空并输入
                    remark_input.clear()
                    time.sleep(1)
                    remark_input.send_keys(self.path_remark)
                    time.sleep(2)

                    # 验证输入结果
                    current_value = remark_input.get_attribute("value") or remark_input.text
                    print(f"📋 备注输入框当前值: '{current_value}'")

                    if current_value == self.path_remark:
                        print("✅ 备注输入成功")
                    else:
                        print(f"⚠️ 备注输入可能不完整，期望: '{self.path_remark}', 实际: '{current_value}'")

                    # 截图：输入备注后
                    screenshot_path = self.take_screenshot("step5_input_remark", "输入文件路径和备注")
                else:
                    print("⚠️ 未找到备注输入框，跳过备注输入")

            except Exception as remark_e:
                print(f"⚠️ 备注输入失败: {remark_e}")

            self.log_result(5, "PASS", f"成功输入文件路径和备注: {self.config['test_path']}", screenshot_path)
            return True

        except Exception as e:
            screenshot_path = self.take_screenshot("step5_input_error", "输入路径失败")
            self.log_result(5, "FAIL", f"输入文件路径失败: {str(e)}", screenshot_path)
            return False
    
    def step6_save_rule(self):
        """步骤6：保存白名单规则"""
        try:
            print("\n🔄 执行步骤6：保存白名单规则")

            # 根据截图，查找绿色的"保存"按钮
            save_button = None
            save_selectors = [
                (By.XPATH, "//button[contains(text(), '保存')]"),
                (By.XPATH, "//button[text()='保存']"),
                (By.XPATH, "//button[contains(text(), '确定')]"),
                (By.XPATH, "//button[contains(text(), '提交')]"),
                (By.XPATH, "//*[contains(text(), '保存')]"),
                (By.CSS_SELECTOR, "button[class*='btn']"),
                (By.XPATH, "//button"),
            ]

            for i, (selector_type, selector_value) in enumerate(save_selectors):
                try:
                    print(f"🔍 尝试保存按钮选择器 {i+1}: {selector_type}={selector_value}")
                    elements = self.driver.find_elements(selector_type, selector_value)

                    if elements:
                        print(f"✅ 找到 {len(elements)} 个按钮")
                        for j, elem in enumerate(elements):
                            try:
                                text = elem.text.strip()
                                is_displayed = elem.is_displayed()
                                is_enabled = elem.is_enabled()
                                print(f"   按钮{j+1}: 文字='{text}' 可见={is_displayed} 可用={is_enabled}")

                                if is_displayed and is_enabled and ("保存" in text or "确定" in text or "提交" in text):
                                    save_button = elem
                                    print(f"🎯 选择按钮{j+1}作为保存按钮")
                                    break
                            except Exception as elem_e:
                                print(f"   按钮{j+1}: 检查失败 - {elem_e}")
                                continue

                    if save_button:
                        break

                except Exception as e:
                    print(f"❌ 选择器 {i+1} 失败: {e}")
                    continue

            if not save_button:
                raise Exception("无法找到保存按钮")

            # 点击保存按钮
            print("🖱️ 点击保存按钮...")
            save_button.click()
            time.sleep(10)

            # 截图：保存成功
            screenshot_path = self.take_screenshot("step6_save_success", "保存白名单规则")

            self.log_result(6, "PASS", "成功保存白名单规则", screenshot_path)
            return True

        except Exception as e:
            screenshot_path = self.take_screenshot("step6_save_error", "保存失败")
            self.log_result(6, "FAIL", f"保存白名单规则失败: {str(e)}", screenshot_path)
            return False
    
    def step7_verify_created(self):
        """步骤7：验证白名单创建成功"""
        try:
            print("\n🔄 执行步骤7：验证白名单创建成功")
            
            # 在白名单列表中查找刚创建的规则
            time.sleep(10)
            rule_found = False
            
            try:
                # 查找包含测试路径的表格行
                rule_row = self.driver.find_element(By.XPATH, f"//tr[contains(., '{self.config['test_path']}')]")
                rule_found = True
            except:
                # 如果没找到，尝试刷新页面再查找
                self.driver.refresh()
                time.sleep(10)
                rule_row = self.driver.find_element(By.XPATH, f"//tr[contains(., '{self.config['test_path']}')]")
                rule_found = True
            
            # 截图：验证创建成功
            screenshot_path = self.take_screenshot("step7_verify_success", "验证白名单创建成功")
            
            if rule_found:
                self.log_result(7, "PASS", "白名单规则创建成功并显示在列表中", screenshot_path)
                # 记录创建的规则路径，用于后续操作
                self.created_rule_path = self.config['test_path']
                return True
            else:
                self.log_result(7, "FAIL", "白名单规则未在列表中找到", screenshot_path)
                return False
            
        except Exception as e:
            screenshot_path = self.take_screenshot("step7_verify_error", "验证失败")
            self.log_result(7, "FAIL", f"验证白名单创建失败: {str(e)}", screenshot_path)
            return False

    def step8_edit_whitelist(self):
        """步骤8：修改白名单规则"""
        try:
            print("\n🔄 执行步骤8：修改白名单规则")

            if not self.created_rule_path:
                raise Exception("没有找到已创建的白名单规则")

            # 查找包含创建路径的表格行，然后找到编辑按钮
            rule_row = self.wait.until(
                EC.presence_of_element_located((By.XPATH, f"//tr[contains(., '{self.created_rule_path}')]"))
            )

            # 在该行中查找编辑按钮（可能是"编辑"、"修改"、图标等）
            edit_button = None
            edit_selectors = [
                ".//button[contains(text(), '编辑')]",
                ".//button[contains(text(), '修改')]",
                ".//a[contains(text(), '编辑')]",
                ".//a[contains(text(), '修改')]",
                ".//button[contains(@title, '编辑')]",
                ".//button[contains(@title, '修改')]",
                ".//i[contains(@class, 'edit')]/..",
                ".//button",  # 最后尝试该行的任何按钮
            ]

            for selector in edit_selectors:
                try:
                    buttons = rule_row.find_elements(By.XPATH, selector)
                    for button in buttons:
                        if button.is_displayed() and button.is_enabled():
                            button_text = button.text.strip()
                            title = button.get_attribute("title")
                            print(f"🔍 找到按钮: 文字='{button_text}' title='{title}'")

                            if "编辑" in button_text or "修改" in button_text or "编辑" in (title or ""):
                                edit_button = button
                                print(f"🎯 选择编辑按钮")
                                break
                    if edit_button:
                        break
                except:
                    continue

            if not edit_button:
                # 如果没找到明确的编辑按钮，尝试双击行
                print("⚠️ 未找到编辑按钮，尝试双击表格行...")
                self.driver.execute_script("arguments[0].scrollIntoView(true);", rule_row)
                time.sleep(1)
                self.driver.execute_script("arguments[0].dispatchEvent(new Event('dblclick', { bubbles: true }));", rule_row)
            else:
                # 点击编辑按钮
                print("🖱️ 点击编辑按钮...")
                self.driver.execute_script("arguments[0].scrollIntoView(true);", edit_button)
                time.sleep(1)
                edit_button.click()

            time.sleep(10)

            # 截图：编辑对话框
            screenshot_path = self.take_screenshot("step8_edit_dialog", "编辑白名单对话框")

            self.log_result(8, "PASS", "成功打开编辑白名单对话框", screenshot_path)
            return True

        except Exception as e:
            screenshot_path = self.take_screenshot("step8_edit_error", "编辑失败")
            self.log_result(8, "FAIL", f"打开编辑对话框失败: {str(e)}", screenshot_path)
            return False

    def step9_update_path(self):
        """步骤9：更新白名单路径"""
        try:
            print("\n🔄 执行步骤9：更新白名单路径")

            # 查找路径输入框（根据截图，应该有预填充的路径）
            path_input = None
            path_selectors = [
                (By.XPATH, "//input[@type='text']"),
                (By.CSS_SELECTOR, "input[type='text']"),
                (By.XPATH, "//input[contains(@placeholder, 'C:')]"),
            ]

            print("🔍 查找路径输入框...")
            for i, (selector_type, selector_value) in enumerate(path_selectors):
                try:
                    print(f"🔍 尝试选择器 {i+1}: {selector_type}={selector_value}")
                    elements = self.driver.find_elements(selector_type, selector_value)

                    if elements:
                        print(f"✅ 找到 {len(elements)} 个输入框")
                        for j, elem in enumerate(elements):
                            try:
                                current_value = elem.get_attribute("value")
                                placeholder = elem.get_attribute("placeholder")
                                is_displayed = elem.is_displayed()
                                is_enabled = elem.is_enabled()
                                print(f"   输入框{j+1}: value='{current_value}' placeholder='{placeholder}' 可见={is_displayed} 可用={is_enabled}")

                                if is_displayed and is_enabled:
                                    # 优先选择路径相关的输入框
                                    if placeholder and ("C:" in placeholder or "temp" in placeholder or "abc" in placeholder or ".exe" in placeholder):
                                        path_input = elem
                                        print(f"🎯 选择输入框{j+1}作为路径输入框（匹配路径placeholder）")
                                        break
                                    elif not path_input:  # 如果还没找到路径输入框，暂时记录这个作为备选
                                        if "备注" not in (placeholder or ""):  # 排除备注输入框
                                            path_input = elem
                                            print(f"🎯 选择输入框{j+1}作为备选路径输入框")
                                            # 不break，继续寻找更合适的
                            except Exception as elem_e:
                                print(f"   输入框{j+1}: 检查失败 - {elem_e}")
                                continue

                    if path_input:
                        break

                except Exception as e:
                    print(f"❌ 选择器 {i+1} 失败: {e}")
                    continue

            if not path_input:
                raise Exception("无法找到路径输入框")

            # 修改路径 - 按照正确的步骤：删除原有内容，输入新内容，保存
            print(f"📝 将路径从 '{self.created_rule_path}' 修改为: {self.config['modified_path']}")

            # 步骤1：点击输入框获得焦点
            print("🖱️ 步骤1：点击输入框获得焦点...")
            self.driver.execute_script("arguments[0].focus();", path_input)
            path_input.click()
            time.sleep(1)

            # 步骤2：清空原有路径（删除原来的）
            print("🗑️ 步骤2：清空原有路径...")
            current_value = path_input.get_attribute("value")
            print(f"   当前路径: '{current_value}'")

            # 使用多种方法清空
            path_input.clear()
            time.sleep(1)

            # 验证是否清空成功
            after_clear = path_input.get_attribute("value")
            if after_clear:
                print(f"⚠️ clear()方法未完全清空，剩余: '{after_clear}'，尝试其他方法...")
                # 使用全选+删除
                path_input.send_keys(Keys.CONTROL + "a")
                time.sleep(0.5)
                path_input.send_keys(Keys.DELETE)
                time.sleep(1)

                after_select_delete = path_input.get_attribute("value")
                if after_select_delete:
                    print(f"⚠️ 全选删除也未完全清空，剩余: '{after_select_delete}'，使用JavaScript清空...")
                    self.driver.execute_script("arguments[0].value = '';", path_input)
                    self.driver.execute_script("arguments[0].dispatchEvent(new Event('input', { bubbles: true }));", path_input)
                    time.sleep(1)

            final_clear_check = path_input.get_attribute("value")
            if final_clear_check:
                print(f"⚠️ 仍有残留内容: '{final_clear_check}'")
            else:
                print("✅ 原有路径已清空")

            # 步骤3：输入新的路径
            print(f"📝 步骤3：输入新路径 '{self.config['modified_path']}'...")
            path_input.send_keys(self.config['modified_path'])
            time.sleep(3)

            # 验证输入结果
            current_value = path_input.get_attribute("value")
            print(f"🔍 输入后的值: '{current_value}'")

            if current_value != self.config['modified_path']:
                print("⚠️ 常规输入失败，尝试JavaScript输入...")
                self.driver.execute_script(f"arguments[0].value = '{self.config['modified_path']}';", path_input)
                self.driver.execute_script("arguments[0].dispatchEvent(new Event('input', { bubbles: true }));", path_input)
                self.driver.execute_script("arguments[0].dispatchEvent(new Event('change', { bubbles: true }));", path_input)
                time.sleep(2)

                current_value = path_input.get_attribute("value")
                print(f"🔍 JavaScript输入后的值: '{current_value}'")

            # 最终验证
            final_value = path_input.get_attribute("value")
            if final_value == self.config['modified_path']:
                print(f"✅ 路径修改成功: '{final_value}'")
            else:
                print(f"⚠️ 路径修改可能不完整: 期望'{self.config['modified_path']}'，实际'{final_value}'")

            # 步骤4：保存修改 - 查找绿色的"保存"按钮
            print("🔍 步骤4：查找保存按钮...")
            save_button = None
            save_selectors = [
                "//button[contains(text(), '保存')]",
                "//button[text()='保存']",
                "//button[contains(@class, 'save')]",
                "//*[contains(text(), '保存')]",
                "//button[@type='submit']"
            ]

            for i, selector in enumerate(save_selectors):
                try:
                    print(f"🔍 尝试选择器 {i+1}: {selector}")
                    buttons = self.driver.find_elements(By.XPATH, selector)
                    print(f"   找到 {len(buttons)} 个按钮")

                    for j, button in enumerate(buttons):
                        try:
                            button_text = button.text.strip()
                            is_displayed = button.is_displayed()
                            is_enabled = button.is_enabled()
                            print(f"   按钮{j+1}: 文字='{button_text}' 可见={is_displayed} 可用={is_enabled}")

                            if is_displayed and is_enabled and ("保存" in button_text or button_text == "保存"):
                                save_button = button
                                print(f"🎯 选择保存按钮: '{button_text}'")
                                break
                        except Exception as btn_e:
                            print(f"   按钮{j+1}: 检查失败 - {btn_e}")
                            continue

                    if save_button:
                        break

                except Exception as e:
                    print(f"❌ 选择器 {i+1} 失败: {e}")
                    continue

            if not save_button:
                raise Exception("无法找到保存按钮")

            print("🖱️ 点击保存按钮...")
            self.driver.execute_script("arguments[0].scrollIntoView(true);", save_button)
            time.sleep(1)
            save_button.click()
            time.sleep(10)

            # 截图：修改成功
            screenshot_path = self.take_screenshot("step9_update_success", "更新白名单路径成功")

            # 更新记录的路径
            self.created_rule_path = self.config['modified_path']

            self.log_result(9, "PASS", f"成功更新白名单路径为: {self.config['modified_path']}", screenshot_path)
            return True

        except Exception as e:
            screenshot_path = self.take_screenshot("step9_update_error", "更新路径失败")
            self.log_result(9, "FAIL", f"更新白名单路径失败: {str(e)}", screenshot_path)
            return False

    def step10_create_hash_whitelist(self):
        """步骤10：点击新增创建文件哈希白名单"""
        try:
            print("\n🔄 执行步骤10：点击新增创建文件哈希白名单")

            # 检查浏览器状态，如果已关闭则重新初始化
            try:
                current_url = self.driver.current_url
                print(f"🌐 当前页面: {current_url}")

                # 刷新页面以确保状态正常
                print("🔄 刷新页面以确保状态正常...")
                self.driver.refresh()
                time.sleep(10)

            except:
                print("🔄 浏览器已关闭，重新初始化...")
                self.setup_driver()

                # 重新登录
                print("🔐 重新登录...")
                self.driver.get(self.config['url'])
                time.sleep(10)

                username_input = self.wait.until(EC.element_to_be_clickable((By.ID, "username")))
                username_input.clear()
                username_input.send_keys(self.config['username'])

                password_input = self.driver.find_element(By.ID, "password")
                password_input.clear()
                password_input.send_keys(self.config['password'])

                login_button = self.driver.find_element(By.XPATH, "//button[@type='submit']")
                login_button.click()
                time.sleep(15)

                # 重新进入白名单页面
                print("📋 重新进入白名单页面...")
                policy_menu = self.wait.until(EC.element_to_be_clickable((By.XPATH, "//span[contains(text(), '防护策略')]")))
                policy_menu.click()
                time.sleep(5)

                whitelist_menu = self.wait.until(EC.element_to_be_clickable((By.XPATH, "//span[contains(text(), '白名单')]")))
                whitelist_menu.click()
                time.sleep(10)

            # 首先创建一个测试文件
            test_file_path = self.config['test_file']
            print(f"📁 创建测试文件: {test_file_path}")

            import os
            os.makedirs(os.path.dirname(test_file_path), exist_ok=True)
            with open(test_file_path, 'w', encoding='utf-8') as f:
                f.write("这是一个用于测试文件哈希白名单的测试文件\n")
                f.write("Test file for hash whitelist automation\n")
                f.write(f"Created at: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")

            print(f"✅ 测试文件已创建: {test_file_path}")

            # 点击新增按钮（完全复制步骤3的逻辑）
            print("🖱️ 点击新增按钮...")

            # 先截图当前页面状态
            screenshot_path = self.take_screenshot("step11_before_add", "添加前的白名单页面")

            # 使用和步骤3完全相同的逻辑
            add_button = None
            add_selectors = [
                # 最精确的选择器 - 找到了"新增"按钮
                (By.XPATH, "//button[contains(text(), '新增')]"),
                (By.XPATH, "//button[text()='新增']"),
                # 备用选择器
                (By.XPATH, "//button[contains(text(), '新建')]"),
                (By.XPATH, "//button[contains(text(), '添加')]"),
                (By.XPATH, "//*[contains(text(), '新增')]"),
                (By.XPATH, "//*[contains(text(), '新建')]"),
            ]

            for i, (selector_type, selector_value) in enumerate(add_selectors):
                try:
                    print(f"🔍 尝试选择器 {i+1}: {selector_type}={selector_value}")
                    elements = self.driver.find_elements(selector_type, selector_value)

                    if elements:
                        print(f"✅ 找到 {len(elements)} 个匹配元素")
                        for j, elem in enumerate(elements):
                            try:
                                text = elem.text.strip()
                                is_displayed = elem.is_displayed()
                                is_enabled = elem.is_enabled()
                                print(f"   元素{j+1}: 文字='{text}' 可见={is_displayed} 可用={is_enabled}")

                                if is_displayed and is_enabled and text in ['新增', '新建', '添加']:
                                    add_button = elem
                                    print(f"🎯 选择按钮: '{text}'")
                                    break
                            except Exception as elem_e:
                                print(f"   元素{j+1}: 检查失败 - {elem_e}")
                                continue

                    if add_button:
                        break

                except Exception as e:
                    print(f"❌ 选择器 {i+1} 失败: {e}")
                    continue

            if not add_button:
                raise Exception("无法找到新增按钮")

            # 点击新增按钮
            print("🖱️ 点击新增按钮...")
            add_button.click()
            time.sleep(5)

            # 截图：打开添加对话框
            screenshot_path = self.take_screenshot("step11_add_dialog", "打开添加文件哈希白名单对话框")

            self.log_result(10, "PASS", "成功打开文件哈希白名单对话框", screenshot_path)
            return True

        except Exception as e:
            screenshot_path = self.take_screenshot("step10_hash_error", "创建文件哈希白名单失败")
            self.log_result(10, "FAIL", f"创建文件哈希白名单失败: {str(e)}", screenshot_path)
            return False

    def step11_select_hash_type(self):
        """步骤11：选择文件哈希值类型"""
        try:
            print("\n🔄 执行步骤11：选择文件哈希值类型")

            # 查找并点击"文件哈希值"选项（类似步骤4，但选择不同选项）
            try:
                # 查找文件哈希值的单选按钮或选项
                hash_option = self.driver.find_element(By.XPATH, "//*[contains(text(), '文件哈希值')]")
                print("✅ 找到文件哈希值选项")

                # 点击选择文件哈希值
                hash_option.click()
                print("🎯 已选择文件哈希值类型")

            except Exception as e:
                print(f"⚠️ 无法找到文件哈希值选项，尝试选择第二个单选按钮: {e}")
                # 如果找不到文字，尝试选择第二个单选按钮（通常哈希是第二个选项）
                try:
                    radios = self.driver.find_elements(By.XPATH, "//input[@type='radio']")
                    if len(radios) >= 2:
                        second_radio = radios[1]  # 第二个单选按钮
                        if not second_radio.is_selected():
                            second_radio.click()
                            print("🎯 已选择第二个单选按钮（文件哈希值）")
                except Exception as radio_e:
                    print(f"❌ 选择单选按钮也失败: {radio_e}")
                    raise Exception("无法选择文件哈希值类型")

            time.sleep(3)

            # 截图：选择哈希类型
            screenshot_path = self.take_screenshot("step12_hash_type", "选择文件哈希值类型")

            self.log_result(11, "PASS", "成功选择文件哈希值类型", screenshot_path)
            return True

        except Exception as e:
            screenshot_path = self.take_screenshot("step11_hash_type_error", "选择文件哈希值类型失败")
            self.log_result(11, "FAIL", f"选择文件哈希值类型失败: {str(e)}", screenshot_path)
            return False

    def step12_select_file_and_save(self):
        """步骤12：选择文件并保存哈希白名单"""
        try:
            print("\n🔄 执行步骤12：选择文件并保存哈希白名单")

            # 点击选取文件按钮
            print("🖱️ 查找并点击选取文件按钮...")
            select_file_button = None

            # 查找选取文件按钮
            buttons = self.driver.find_elements(By.XPATH, "//button")
            for button in buttons:
                try:
                    button_text = button.text.strip()
                    if button.is_displayed() and button.is_enabled() and ("选取" in button_text or "选择" in button_text or "浏览" in button_text):
                        select_file_button = button
                        print(f"🎯 找到选取文件按钮: '{button_text}'")
                        break
                except:
                    continue

            if not select_file_button:
                raise Exception("无法找到选取文件按钮")

            # 点击选取文件按钮
            select_file_button.click()
            time.sleep(5)

            # 截图：点击选取文件后
            screenshot_path = self.take_screenshot("step13_click_select_file", "点击选取文件按钮")

            # 处理文件选择
            print("📁 处理文件选择...")

            # 方法1：尝试直接发送文件路径到文件输入框
            try:
                file_input = self.driver.find_element(By.XPATH, "//input[@type='file']")
                file_input.send_keys(self.config['test_file'])
                print("✅ 通过文件输入框选择文件")
                time.sleep(3)
            except:
                # 方法2：使用pyautogui处理文件对话框
                print("🔄 使用pyautogui处理文件对话框...")
                try:
                    import pyautogui
                    time.sleep(3)

                    # 输入文件路径
                    pyautogui.hotkey('ctrl', 'l')  # 打开地址栏
                    time.sleep(1)
                    pyautogui.write(self.config['test_file'])
                    time.sleep(1)
                    pyautogui.press('enter')
                    time.sleep(3)
                    print("✅ 通过pyautogui选择文件")
                except:
                    print("⚠️ 自动文件选择失败，请手动选择文件")
                    time.sleep(15)  # 给用户时间手动选择

            # 截图：文件选择后
            screenshot_path = self.take_screenshot("step12_file_selected", "文件选择完成")

            # 输入备注
            print(f"📝 输入备注: {self.hash_remark}")
            try:
                # 查找备注输入框
                remark_input = None
                remark_selectors = [
                    # 根据截图，备注输入框在"备注"标签后面
                    "//label[contains(text(), '备注')]/following-sibling::*/input",
                    "//span[contains(text(), '备注')]/following-sibling::*/input",
                    "//div[contains(text(), '备注')]/following-sibling::*/input",
                    "//label[contains(text(), '备注')]/..//input",
                    "//span[contains(text(), '备注')]/..//input",
                    "//div[contains(text(), '备注')]/..//input",
                    # 通用选择器
                    "//textarea[@placeholder='请输入备注']",
                    "//textarea[contains(@placeholder, '备注')]",
                    "//input[@placeholder='请输入备注']",
                    "//input[contains(@placeholder, '备注')]",
                    # 最后尝试所有可见的输入框
                    "//input[@type='text']",
                    "//textarea"
                ]

                for i, selector in enumerate(remark_selectors):
                    try:
                        print(f"🔍 尝试备注输入框选择器 {i+1}: {selector}")
                        elements = self.driver.find_elements(By.XPATH, selector)
                        print(f"   找到 {len(elements)} 个元素")

                        for j, elem in enumerate(elements):
                            try:
                                placeholder = elem.get_attribute("placeholder") or ""
                                tag_name = elem.tag_name
                                is_displayed = elem.is_displayed()
                                is_enabled = elem.is_enabled()
                                class_name = elem.get_attribute("class") or ""
                                print(f"   元素{j+1}: <{tag_name}> placeholder='{placeholder}' class='{class_name}' 可见={is_displayed} 可用={is_enabled}")

                                if is_displayed and is_enabled:
                                    remark_input = elem
                                    print(f"🎯 选择备注输入框: <{tag_name}> placeholder='{placeholder}'")
                                    break
                            except Exception as elem_e:
                                print(f"   元素{j+1}: 检查失败 - {elem_e}")
                                continue

                        if remark_input:
                            break
                    except Exception as e:
                        print(f"❌ 选择器 {i+1} 失败: {e}")
                        continue

                if remark_input:
                    # 先点击获得焦点
                    remark_input.click()
                    time.sleep(1)

                    # 清空并输入
                    remark_input.clear()
                    time.sleep(1)
                    remark_input.send_keys(self.hash_remark)
                    time.sleep(2)

                    # 验证输入结果
                    current_value = remark_input.get_attribute("value") or remark_input.text
                    print(f"📋 备注输入框当前值: '{current_value}'")

                    if current_value == self.hash_remark:
                        print("✅ 备注输入成功")
                    else:
                        print(f"⚠️ 备注输入可能不完整，期望: '{self.hash_remark}', 实际: '{current_value}'")

                    # 截图：输入备注后
                    screenshot_path = self.take_screenshot("step12_input_remark", "输入文件哈希备注")
                else:
                    print("⚠️ 未找到备注输入框，跳过备注输入")

            except Exception as remark_e:
                print(f"⚠️ 备注输入失败: {remark_e}")

            # 点击保存按钮
            print("🖱️ 查找并点击保存按钮...")
            save_button = None

            buttons = self.driver.find_elements(By.XPATH, "//button")
            for button in buttons:
                try:
                    button_text = button.text.strip()
                    if button.is_displayed() and button.is_enabled() and "保存" in button_text:
                        save_button = button
                        print(f"🎯 找到保存按钮: '{button_text}'")
                        break
                except:
                    continue

            if not save_button:
                raise Exception("无法找到保存按钮")

            save_button.click()
            time.sleep(10)

            # 截图：保存后
            screenshot_path = self.take_screenshot("step12_hash_saved", "文件哈希白名单保存成功")

            self.log_result(12, "PASS", "成功选择文件并保存哈希白名单", screenshot_path)
            return True

        except Exception as e:
            screenshot_path = self.take_screenshot("step12_save_error", "保存文件哈希白名单失败")
            self.log_result(12, "FAIL", f"保存文件哈希白名单失败: {str(e)}", screenshot_path)
            return False

    def step13_test_search_by_path(self):
        """步骤13：测试按文件路径搜索"""
        try:
            print("\n🔄 执行步骤13：测试按文件路径搜索")

            # 查找搜索框
            search_input = None
            search_selectors = [
                "//input[@placeholder='请输入白名单或备注进行搜索']",
                "//input[@placeholder='请输入搜索内容']",
                "//input[@placeholder='搜索']",
                "//input[contains(@placeholder, '搜索')]",
                "//input[contains(@placeholder, '白名单')]",
                "//input[contains(@placeholder, '备注')]",
                "//input[@type='text']",
                "//input[@class='el-input__inner']"
            ]

            for i, selector in enumerate(search_selectors):
                try:
                    print(f"🔍 尝试搜索框选择器 {i+1}: {selector}")
                    inputs = self.driver.find_elements(By.XPATH, selector)
                    print(f"   找到 {len(inputs)} 个输入框")

                    for j, input_elem in enumerate(inputs):
                        try:
                            placeholder = input_elem.get_attribute("placeholder") or ""
                            is_displayed = input_elem.is_displayed()
                            is_enabled = input_elem.is_enabled()
                            print(f"   输入框{j+1}: placeholder='{placeholder}' 可见={is_displayed} 可用={is_enabled}")

                            if is_displayed and is_enabled:
                                search_input = input_elem
                                print(f"🎯 选择搜索框: placeholder='{placeholder}'")
                                break
                        except Exception as input_e:
                            print(f"   输入框{j+1}: 检查失败 - {input_e}")
                            continue

                    if search_input:
                        break

                except Exception as e:
                    print(f"❌ 选择器 {i+1} 失败: {e}")
                    continue

            if not search_input:
                raise Exception("无法找到搜索框")

            # 搜索文件路径白名单
            search_input.clear()
            search_input.send_keys(self.config['test_path'])
            search_input.send_keys(Keys.ENTER)
            time.sleep(5)

            # 截图：搜索结果
            screenshot_path = self.take_screenshot("step13_search_path", "按文件路径搜索结果")

            # 验证搜索结果
            rows = self.driver.find_elements(By.XPATH, "//tbody/tr")
            found_path = False
            for row in rows:
                if self.config['test_path'] in row.text:
                    found_path = True
                    break

            if not found_path:
                raise Exception("未找到文件路径白名单搜索结果")

            self.log_result(13, "PASS", "成功按文件路径搜索", screenshot_path)
            return True

        except Exception as e:
            screenshot_path = self.take_screenshot("step13_search_error", "按文件路径搜索失败")
            self.log_result(13, "FAIL", f"按文件路径搜索失败: {str(e)}", screenshot_path)
            return False

    def step14_test_search_by_remark(self):
        """步骤14：测试按备注搜索"""
        try:
            print("\n🔄 执行步骤14：测试按备注搜索")

            # 查找搜索框（使用和步骤13相同的逻辑）
            search_input = None
            search_selectors = [
                "//input[@placeholder='请输入白名单或备注进行搜索']",
                "//input[@placeholder='请输入搜索内容']",
                "//input[@placeholder='搜索']",
                "//input[contains(@placeholder, '搜索')]",
                "//input[contains(@placeholder, '白名单')]",
                "//input[contains(@placeholder, '备注')]",
                "//input[@type='text']",
                "//input[@class='el-input__inner']"
            ]

            for selector in search_selectors:
                try:
                    inputs = self.driver.find_elements(By.XPATH, selector)
                    for input_elem in inputs:
                        try:
                            if input_elem.is_displayed() and input_elem.is_enabled():
                                search_input = input_elem
                                break
                        except:
                            continue
                    if search_input:
                        break
                except:
                    continue

            if not search_input:
                raise Exception("无法找到搜索框")

            # 搜索哈希白名单的备注
            search_input.clear()
            search_input.send_keys(self.hash_remark)
            search_input.send_keys(Keys.ENTER)
            time.sleep(5)

            # 截图：搜索结果
            screenshot_path = self.take_screenshot("step14_search_remark", "按备注搜索结果")

            # 验证搜索结果
            rows = self.driver.find_elements(By.XPATH, "//tbody/tr")
            found_remark = False
            for row in rows:
                if self.hash_remark in row.text:
                    found_remark = True
                    break

            if not found_remark:
                raise Exception("未找到备注搜索结果")

            self.log_result(14, "PASS", "成功按备注搜索", screenshot_path)
            return True

        except Exception as e:
            screenshot_path = self.take_screenshot("step14_search_error", "按备注搜索失败")
            self.log_result(14, "FAIL", f"按备注搜索失败: {str(e)}", screenshot_path)
            return False

    def step15_clear_search(self):
        """步骤15：清空搜索条件"""
        try:
            print("\n🔄 执行步骤15：清空搜索条件")

            # 查找搜索框
            search_input = None
            search_selectors = [
                "//input[@placeholder='请输入白名单或备注进行搜索']",
                "//input[@placeholder='请输入搜索内容']",
                "//input[@placeholder='搜索']",
                "//input[contains(@placeholder, '搜索')]",
                "//input[contains(@placeholder, '白名单')]",
                "//input[contains(@placeholder, '备注')]",
                "//input[@type='text']",
                "//input[@class='el-input__inner']"
            ]

            for selector in search_selectors:
                try:
                    inputs = self.driver.find_elements(By.XPATH, selector)
                    for input_elem in inputs:
                        try:
                            if input_elem.is_displayed() and input_elem.is_enabled():
                                search_input = input_elem
                                break
                        except:
                            continue
                    if search_input:
                        break
                except:
                    continue

            if not search_input:
                raise Exception("无法找到搜索框")

            # 清空搜索框
            search_input.clear()
            search_input.send_keys(Keys.ENTER)
            time.sleep(5)

            # 截图：清空搜索后
            screenshot_path = self.take_screenshot("step15_clear_search", "清空搜索条件")

            # 验证显示所有记录
            rows = self.driver.find_elements(By.XPATH, "//tbody/tr")
            if len(rows) < 2:
                raise Exception("清空搜索后应该显示所有白名单记录")

            self.log_result(15, "PASS", "成功清空搜索条件", screenshot_path)
            return True

        except Exception as e:
            screenshot_path = self.take_screenshot("step15_clear_error", "清空搜索条件失败")
            self.log_result(15, "FAIL", f"清空搜索条件失败: {str(e)}", screenshot_path)
            return False

    def step16_delete_hash_whitelist(self):
        """步骤16：删除文件哈希白名单"""
        try:
            print("\n🔄 执行步骤16：删除文件哈希白名单")

            # 简单方法：直接查找最后一行（刚创建的哈希白名单）
            print("🔍 查找最新的哈希白名单规则...")
            time.sleep(5)

            # 查找表格中的最后一行（最新创建的）
            all_rows = self.driver.find_elements(By.XPATH, "//tbody/tr")
            if not all_rows:
                raise Exception("未找到任何白名单规则")

            rule_row = all_rows[-1]  # 选择最后一行
            print(f"✅ 选择最后一行作为要删除的哈希白名单规则")

            # 截图：删除前
            screenshot_path = self.take_screenshot("step14_before_delete", "删除哈希白名单前")

            # 在该行中查找删除按钮（完全复制步骤10的逻辑）
            print("🔍 查找删除按钮...")
            delete_button = None

            delete_selectors = [
                ".//button[contains(text(), '删除')]",
                ".//a[contains(text(), '删除')]",
                ".//button[contains(@title, '删除')]",
                ".//i[contains(@class, 'delete')]/..",
                ".//i[contains(@class, 'remove')]/..",
                ".//i[contains(@class, 'trash')]/..",
                ".//button[contains(@class, 'delete')]",
                ".//button[contains(@class, 'remove')]",
                ".//button",  # 最后尝试该行的所有按钮
            ]

            for i, selector in enumerate(delete_selectors):
                try:
                    print(f"🔍 尝试选择器 {i+1}: {selector}")
                    buttons = rule_row.find_elements(By.XPATH, selector)
                    print(f"   找到 {len(buttons)} 个按钮")

                    for j, button in enumerate(buttons):
                        try:
                            button_text = button.text.strip()
                            title = button.get_attribute("title")
                            class_name = button.get_attribute("class")
                            is_displayed = button.is_displayed()
                            is_enabled = button.is_enabled()
                            print(f"   按钮{j+1}: 文字='{button_text}' title='{title}' class='{class_name}' 可见={is_displayed} 可用={is_enabled}")

                            if is_displayed and is_enabled:
                                # 优先选择明确标识为删除的按钮
                                if ("删除" in button_text or "删除" in (title or "") or
                                    "delete" in (class_name or "").lower() or
                                    "remove" in (class_name or "").lower() or
                                    "trash" in (class_name or "").lower()):
                                    delete_button = button
                                    print(f"🎯 选择删除按钮（明确标识）")
                                    break
                                elif not delete_button and selector == ".//button":
                                    # 如果是最后一个选择器（所有按钮），且还没找到删除按钮
                                    # 选择最后一个按钮（通常删除按钮在最后）
                                    if j == len(buttons) - 1:
                                        delete_button = button
                                        print(f"🎯 选择最后一个按钮作为删除按钮")
                        except Exception as btn_e:
                            print(f"   按钮{j+1}: 检查失败 - {btn_e}")
                            continue

                    if delete_button:
                        break

                except Exception as e:
                    print(f"❌ 选择器 {i+1} 失败: {e}")
                    continue

            if not delete_button:
                raise Exception("无法找到删除按钮")

            # 点击删除按钮
            print("🖱️ 点击删除按钮...")
            self.driver.execute_script("arguments[0].scrollIntoView(true);", delete_button)
            time.sleep(1)
            delete_button.click()
            time.sleep(5)

            # 处理确认对话框（完全复制步骤10的逻辑）
            print("🔍 查找确认对话框...")
            confirm_button = None
            confirm_selectors = [
                "//button[contains(text(), '确定')]",
                "//button[text()='确定']",
                "//button[contains(text(), '确认')]",
                "//button[contains(text(), '删除')]",
                "//button[contains(@class, 'confirm')]",
                "//*[contains(text(), '确定')]"
            ]

            for i, selector in enumerate(confirm_selectors):
                try:
                    print(f"🔍 尝试确认按钮选择器 {i+1}: {selector}")
                    buttons = self.driver.find_elements(By.XPATH, selector)
                    print(f"   找到 {len(buttons)} 个按钮")

                    for j, button in enumerate(buttons):
                        try:
                            button_text = button.text.strip()
                            is_displayed = button.is_displayed()
                            is_enabled = button.is_enabled()
                            print(f"   按钮{j+1}: 文字='{button_text}' 可见={is_displayed} 可用={is_enabled}")

                            if is_displayed and is_enabled and ("确定" in button_text or "确认" in button_text):
                                confirm_button = button
                                print(f"🎯 选择确认按钮: '{button_text}'")
                                break
                        except Exception as btn_e:
                            print(f"   按钮{j+1}: 检查失败 - {btn_e}")
                            continue

                    if confirm_button:
                        break

                except Exception as e:
                    print(f"❌ 选择器 {i+1} 失败: {e}")
                    continue

            if confirm_button:
                print("🖱️ 点击确认按钮...")
                confirm_button.click()
                time.sleep(5)
                print("✅ 已点击确认按钮")
            else:
                print("⚠️ 未找到确认对话框，可能已直接删除")
                time.sleep(3)

            # 截图：删除后
            screenshot_path = self.take_screenshot("step14_hash_deleted", "文件哈希白名单删除成功")

            # 清理测试文件
            try:
                import os
                if os.path.exists(self.config['test_file']):
                    os.remove(self.config['test_file'])
                    print(f"🗑️ 已清理测试文件: {self.config['test_file']}")
            except:
                pass

            self.log_result(16, "PASS", "成功删除文件哈希白名单", screenshot_path)
            return True

        except Exception as e:
            screenshot_path = self.take_screenshot("step16_hash_delete_error", "删除文件哈希白名单失败")
            self.log_result(16, "FAIL", f"删除文件哈希白名单失败: {str(e)}", screenshot_path)
            return False

    def run_test(self):
        """运行完整测试"""
        print("🚀 开始执行AR简单白名单创建自动化测试")
        print(f"📋 测试配置: {self.config}")
        
        try:
            # 设置浏览器驱动
            self.setup_driver()
            
            # 执行测试步骤
            steps = [
                self.step1_login,
                self.step2_enter_whitelist,
                self.step3_click_add,
                self.step4_select_path_type,
                self.step5_input_path_and_remark,
                self.step6_save_rule,
                self.step7_verify_created,
                self.step8_edit_whitelist,
                self.step9_update_path,
                self.step10_create_hash_whitelist,
                self.step11_select_hash_type,
                self.step12_select_file_and_save,
                self.step13_test_search_by_path,
                self.step14_test_search_by_remark,
                self.step15_clear_search,
                self.step16_delete_hash_whitelist
            ]
            
            success_count = 0
            for i, step_func in enumerate(steps, 1):
                if step_func():
                    success_count += 1
                else:
                    print(f"❌ 步骤{i}失败，停止后续测试")
                    break
                
                # 步骤间等待
                time.sleep(1)
            
            # 生成测试报告
            self.generate_report(success_count, len(steps))
            
        except Exception as e:
            print(f"❌ 测试执行异常: {str(e)}")
        finally:
            if self.driver:
                self.driver.quit()
                print("🔚 浏览器已关闭")
    
    def generate_report(self, success_count, total_count):
        """生成测试报告"""
        print(f"\n📊 测试完成！成功: {success_count}/{total_count}")
        
        # 保存详细报告到文件
        report_file = f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.yaml"
        report_data = {
            'test_name': 'AR完整白名单管理测试（创建+修改+删除）',
            'execution_time': datetime.now().isoformat(),
            'config': self.config,
            'summary': {
                'total_steps': total_count,
                'success_steps': success_count,
                'success_rate': f"{success_count/total_count*100:.1f}%"
            },
            'results': self.test_results
        }
        
        with open(report_file, 'w', encoding='utf-8') as f:
            yaml.dump(report_data, f, default_flow_style=False, allow_unicode=True)
        
        print(f"📄 详细报告已保存: {report_file}")

if __name__ == "__main__":
    # 运行测试
    test = ARWhitelistTest()
    test.run_test()
