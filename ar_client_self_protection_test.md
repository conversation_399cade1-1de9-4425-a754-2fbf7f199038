# AR客户端自我保护测试

## 测试信息
```yaml
test_name: "AR客户端自我保护功能测试"
test_description: "测试AR客户端的自我保护功能"
test_timeout: 300
expected_result: "客户端自我保护功能正常工作，能够防止恶意操作"
test_environment: "Windows/Linux客户端"
test_priority: "P0"
test_category: "客户端功能测试"
```

## 前置条件
- mr_mainsvc服务已启动

## 测试步骤

### 步骤1：检查服务运行状态
**操作描述**：确认mr_mainsvc服务正在运行

**目标图片**：
![服务运行状态](images/service_running_status.png)

**操作类型**：状态检查
**等待时间**：5秒
**预期结果**：mr_mainsvc服务正在运行

---

### 步骤2：Windows客户端 - 尝试修改安装目录文件
**操作描述**：尝试在edr安装目录下增、删、改文件

**目标图片**：
![尝试修改安装文件](images/attempt_modify_install_files.png)

**操作类型**：文件操作
**等待时间**：10秒
**预期结果**：被拒绝

---

### 步骤3：Windows客户端 - 尝试结束EDR进程
**操作描述**：尝试结束EDR进程

**目标图片**：
![尝试结束进程](images/attempt_kill_edr_process.png)

**操作类型**：进程操作
**等待时间**：10秒
**预期结果**：被拒绝

---

### 步骤4：Linux客户端 - 尝试结束EDR进程
**操作描述**：尝试结束EDR的进程

**目标图片**：
![Linux尝试结束进程](images/linux_attempt_kill_process.png)

**操作类型**：进程操作
**等待时间**：10秒
**预期结果**：进程被结束，但过一会儿之后会自动再运行

---

### 步骤5：验证进程自动恢复
**操作描述**：在Linux系统中验证进程是否自动恢复

**目标图片**：
![进程自动恢复](images/process_auto_recovery.png)

**操作类型**：验证
**等待时间**：60秒
**预期结果**：EDR进程自动重新启动

---

### 步骤6：测试文件保护范围
**操作描述**：测试不同类型文件的保护情况

**目标图片**：
![文件保护范围](images/file_protection_scope.png)

**操作类型**：文件操作测试
**等待时间**：30秒
**预期结果**：关键文件受到保护

---

### 步骤7：测试注册表保护（Windows）
**操作描述**：尝试修改AR相关的注册表项

**目标图片**：
![注册表保护](images/registry_protection.png)

**操作类型**：注册表操作
**等待时间**：10秒
**预期结果**：注册表修改被阻止

---

### 步骤8：验证服务保护
**操作描述**：尝试停止或修改AR服务

**目标图片**：
![服务保护验证](images/service_protection_verification.png)

**操作类型**：服务操作
**等待时间**：10秒
**预期结果**：服务操作被阻止或自动恢复

---

### 步骤9：检查保护日志
**操作描述**：查看自我保护相关的日志记录

**目标图片**：
![保护日志](images/protection_logs.png)

**操作类型**：日志查看
**等待时间**：10秒
**预期结果**：保护事件被正确记录

---

### 步骤10：截图保存测试结果
**操作描述**：对客户端自我保护测试进行截图保存

**操作类型**：截图
**保存文件名**：ar_client_self_protection_test_{timestamp}.png
**预期结果**：测试结果截图保存成功

## 测试验证点
```yaml
verification_points:
  - name: "Windows文件保护正常"
    description: "Windows客户端文件修改被正确阻止"
    image: "images/attempt_modify_install_files.png"
    critical: true
  
  - name: "Windows进程保护正常"
    description: "Windows客户端进程终止被正确阻止"
    image: "images/attempt_kill_edr_process.png"
    critical: true
  
  - name: "Linux进程自动恢复"
    description: "Linux客户端进程能够自动恢复"
    image: "images/process_auto_recovery.png"
    critical: true
    
  - name: "服务保护正常"
    description: "AR服务受到保护"
    image: "images/service_protection_verification.png"
    critical: true
    
  - name: "保护日志正确记录"
    description: "自我保护事件被正确记录"
    image: "images/protection_logs.png"
    critical: false
```

## 测试数据
```yaml
test_data:
  protected_components:
    windows:
      files:
        - "C:\\Program Files (x86)\\mredrclient\\*.exe"
        - "C:\\Program Files (x86)\\mredrclient\\*.dll"
        - "C:\\Program Files (x86)\\mredrclient\\*.cfg"
      processes:
        - "mr_mainsvc.exe"
        - "mredrclient.exe"
      services:
        - "mr_mainsvc"
      registry:
        - "HKEY_LOCAL_MACHINE\\SOFTWARE\\mredrclient"
        
    linux:
      files:
        - "/opt/apps/mredrclient/*"
      processes:
        - "mr_mainsvc"
        - "mr_enginesvc"
      services:
        - "mr_mainsvc"
        - "mr_enginesvc"
        
  protection_mechanisms:
    - "文件访问控制"
    - "进程保护"
    - "服务保护"
    - "注册表保护"
    - "自动恢复"
    
  test_operations:
    - "删除文件"
    - "修改文件"
    - "重命名文件"
    - "终止进程"
    - "停止服务"
    - "修改注册表"
    
  expected_behaviors:
    windows:
      file_operations: "被拒绝"
      process_kill: "被拒绝"
    linux:
      process_kill: "进程自动恢复"
      
  recovery_time: "60秒内"
```
