# AR安全软件病毒模拟程序

## 📋 程序说明
这是一个用于测试AR安全软件防护功能的病毒行为模拟程序。
程序会模拟各种恶意行为，但不会造成任何实际危害。

## 🚀 使用方法

### 方法1: 图形化启动器
双击运行 `virus_simulator_launcher.bat`，按提示选择测试类型。

### 方法2: 命令行运行
```cmd
# 文件感染模拟
virus_simulator.exe --type file_infection

# 勒索软件模拟  
virus_simulator.exe --type ransomware

# 注册表修改模拟
virus_simulator.exe --type registry

# 网络活动模拟
virus_simulator.exe --type network

# 进程注入模拟
virus_simulator.exe --type injection

# 完整测试
virus_simulator.exe --type all

# 保留测试文件(不自动清理)
virus_simulator.exe --type all --no-cleanup
```

## 🎯 测试类型说明

| 类型 | 说明 | 模拟行为 |
|------|------|----------|
| file_infection | 文件感染 | 创建模拟病毒文件，修改文件内容 |
| ransomware | 勒索软件 | 创建文档文件，模拟加密，生成勒索信息 |
| registry | 注册表修改 | 模拟访问注册表，创建启动项脚本 |
| network | 网络活动 | 模拟可疑网络连接，生成网络日志 |
| injection | 进程注入 | 模拟进程注入，多线程恶意活动 |
| all | 完整测试 | 执行所有上述测试类型 |

## ⚠️ 注意事项

1. **安全性**: 程序仅模拟恶意行为，不包含真实恶意代码
2. **测试环境**: 建议在测试环境中运行，避免在生产环境使用
3. **AR软件**: 确保AR安全软件已正确安装并运行
4. **文件清理**: 程序会自动清理测试文件，使用--no-cleanup可保留
5. **管理员权限**: 某些测试可能需要管理员权限

## 📊 预期结果

运行程序后，AR安全软件应该能够:
- 检测到模拟的恶意文件
- 触发相应的防护机制
- 在控制中心生成告警日志
- 根据策略执行相应的处置动作

## 🔧 技术支持

如有问题，请联系AR安全软件技术支持团队。

---
版本: 1.0
更新时间: 2025-07-30
