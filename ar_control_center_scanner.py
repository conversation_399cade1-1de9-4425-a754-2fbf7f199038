#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AR控制中心页面扫描器
扫描所有页面的UI元素，生成自动化测试规则库

功能：
1. 登录控制中心
2. 遍历所有菜单页面
3. 分析每个页面的UI元素（按钮、输入框、表格等）
4. 生成标准化的选择器规则
5. 保存为JSON和Markdown格式的规则库
"""

import time
import json
import os
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options

class ARControlCenterScanner:
    def __init__(self):
        self.driver = None
        self.wait = None
        self.scan_results = {}
        self.scanned_urls = set()  # 记录已扫描的URL，避免重复

        self.config = {
            'url': 'http://172.16.103.202:8088',
            'username': 'admin',
            'password': 'Admin.2022',
            'timeout': 10
        }

        # 创建输出目录
        self.output_dir = "ar_ui_rules"
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
    
    def setup_driver(self):
        """设置浏览器"""
        try:
            chrome_options = Options()
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--window-size=1920,1080")
            
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.maximize_window()
            self.wait = WebDriverWait(self.driver, self.config['timeout'])
            
            print("✅ 浏览器设置成功")
            return True
        except Exception as e:
            print(f"❌ 浏览器设置失败: {e}")
            return False
    
    def login(self):
        """登录控制中心"""
        try:
            print("🔄 登录控制中心...")
            
            self.driver.get(self.config['url'])
            time.sleep(3)
            
            # 登录
            username_input = self.wait.until(EC.element_to_be_clickable((By.NAME, "username")))
            username_input.clear()
            username_input.send_keys(self.config['username'])
            
            password_input = self.driver.find_element(By.ID, "password")
            password_input.clear()
            password_input.send_keys(self.config['password'])
            
            login_button = self.driver.find_element(By.XPATH, "//button[@type='submit']")
            login_button.click()
            time.sleep(5)
            
            print("✅ 登录成功")
            return True
            
        except Exception as e:
            print(f"❌ 登录失败: {e}")
            return False
    
    def analyze_page_elements(self, page_name, page_url):
        """分析页面元素"""
        try:
            print(f"\n🔍 分析页面: {page_name}")
            
            # 等待页面加载
            time.sleep(3)
            
            page_data = {
                'page_name': page_name,
                'page_url': page_url,
                'scan_time': datetime.now().isoformat(),
                'elements': {
                    'buttons': [],
                    'inputs': [],
                    'tables': [],
                    'links': [],
                    'checkboxes': [],
                    'radios': [],
                    'selects': [],
                    'dialogs': [],
                    'menus': []
                }
            }
            
            # 分析按钮
            self._analyze_buttons(page_data)
            
            # 分析输入框
            self._analyze_inputs(page_data)
            
            # 分析表格
            self._analyze_tables(page_data)
            
            # 分析链接
            self._analyze_links(page_data)
            
            # 分析复选框
            self._analyze_checkboxes(page_data)
            
            # 分析单选框
            self._analyze_radios(page_data)
            
            # 分析下拉框
            self._analyze_selects(page_data)
            
            # 分析对话框
            self._analyze_dialogs(page_data)
            
            # 分析菜单
            self._analyze_menus(page_data)
            
            return page_data
            
        except Exception as e:
            print(f"❌ 分析页面失败: {e}")
            return None
    
    def _analyze_buttons(self, page_data):
        """分析按钮"""
        try:
            button_selectors = [
                "//button",
                "//input[@type='button']",
                "//input[@type='submit']",
                "//*[contains(@class, 'el-button')]",
                "//*[contains(@class, 'btn')]"
            ]
            
            buttons = []
            for selector in button_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    for elem in elements:
                        if elem.is_displayed():
                            button_info = {
                                'text': elem.text.strip(),
                                'class': elem.get_attribute('class'),
                                'id': elem.get_attribute('id'),
                                'type': elem.get_attribute('type'),
                                'xpath': self._get_element_xpath(elem),
                                'tag_name': elem.tag_name
                            }
                            if button_info not in buttons:
                                buttons.append(button_info)
                except:
                    continue
            
            page_data['elements']['buttons'] = buttons
            print(f"   找到 {len(buttons)} 个按钮")
            
        except Exception as e:
            print(f"   分析按钮失败: {e}")
    
    def _analyze_inputs(self, page_data):
        """分析输入框"""
        try:
            input_selectors = [
                "//input",
                "//textarea",
                "//*[contains(@class, 'el-input')]//input",
                "//*[contains(@class, 'el-textarea')]//textarea"
            ]
            
            inputs = []
            for selector in input_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    for elem in elements:
                        if elem.is_displayed():
                            input_info = {
                                'name': elem.get_attribute('name'),
                                'id': elem.get_attribute('id'),
                                'type': elem.get_attribute('type'),
                                'placeholder': elem.get_attribute('placeholder'),
                                'class': elem.get_attribute('class'),
                                'xpath': self._get_element_xpath(elem),
                                'tag_name': elem.tag_name
                            }
                            if input_info not in inputs:
                                inputs.append(input_info)
                except:
                    continue
            
            page_data['elements']['inputs'] = inputs
            print(f"   找到 {len(inputs)} 个输入框")
            
        except Exception as e:
            print(f"   分析输入框失败: {e}")
    
    def _analyze_tables(self, page_data):
        """分析表格"""
        try:
            table_selectors = [
                "//table",
                "//*[contains(@class, 'el-table')]",
                "//*[contains(@class, 'table')]"
            ]
            
            tables = []
            for selector in table_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    for elem in elements:
                        if elem.is_displayed():
                            # 获取表头
                            headers = []
                            try:
                                header_elements = elem.find_elements(By.XPATH, ".//th")
                                headers = [th.text.strip() for th in header_elements if th.text.strip()]
                            except:
                                pass
                            
                            table_info = {
                                'class': elem.get_attribute('class'),
                                'id': elem.get_attribute('id'),
                                'headers': headers,
                                'xpath': self._get_element_xpath(elem),
                                'tag_name': elem.tag_name
                            }
                            if table_info not in tables:
                                tables.append(table_info)
                except:
                    continue
            
            page_data['elements']['tables'] = tables
            print(f"   找到 {len(tables)} 个表格")
            
        except Exception as e:
            print(f"   分析表格失败: {e}")
    
    def _analyze_checkboxes(self, page_data):
        """分析复选框"""
        try:
            checkbox_selectors = [
                "//input[@type='checkbox']",
                "//span[contains(@class, 'el-checkbox__inner')]",
                "//*[contains(@class, 'el-checkbox')]"
            ]
            
            checkboxes = []
            for selector in checkbox_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    for elem in elements:
                        if elem.is_displayed():
                            # 获取关联的标签文本
                            label_text = ""
                            try:
                                # 查找相关的标签
                                parent = elem.find_element(By.XPATH, "./ancestor::label[1]")
                                label_text = parent.text.strip()
                            except:
                                try:
                                    parent = elem.find_element(By.XPATH, "./ancestor::*[contains(@class, 'checkbox')][1]")
                                    label_text = parent.text.strip()
                                except:
                                    pass
                            
                            checkbox_info = {
                                'label_text': label_text,
                                'class': elem.get_attribute('class'),
                                'id': elem.get_attribute('id'),
                                'name': elem.get_attribute('name'),
                                'xpath': self._get_element_xpath(elem),
                                'tag_name': elem.tag_name
                            }
                            if checkbox_info not in checkboxes:
                                checkboxes.append(checkbox_info)
                except:
                    continue
            
            page_data['elements']['checkboxes'] = checkboxes
            print(f"   找到 {len(checkboxes)} 个复选框")
            
        except Exception as e:
            print(f"   分析复选框失败: {e}")
    
    def _get_element_xpath(self, element):
        """获取元素的XPath"""
        try:
            return self.driver.execute_script("""
                function getXPath(element) {
                    if (element.id !== '') {
                        return "//*[@id='" + element.id + "']";
                    }
                    if (element === document.body) {
                        return '/html/body';
                    }
                    var ix = 0;
                    var siblings = element.parentNode.childNodes;
                    for (var i = 0; i < siblings.length; i++) {
                        var sibling = siblings[i];
                        if (sibling === element) {
                            return getXPath(element.parentNode) + '/' + element.tagName.toLowerCase() + '[' + (ix + 1) + ']';
                        }
                        if (sibling.nodeType === 1 && sibling.tagName === element.tagName) {
                            ix++;
                        }
                    }
                }
                return getXPath(arguments[0]);
            """, element)
        except:
            return ""
    
    def get_all_menu_items(self):
        """获取所有菜单项"""
        try:
            print("\n🔍 获取所有菜单项...")
            
            menu_items = []
            
            # 查找主菜单
            main_menu_selectors = [
                "//ul[contains(@class, 'el-menu')]//span",
                "//nav//a",
                "//aside//a",
                "//*[contains(@class, 'menu')]//span",
                "//*[contains(@class, 'sidebar')]//span"
            ]
            
            for selector in main_menu_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    for elem in elements:
                        if elem.is_displayed() and elem.text.strip():
                            menu_text = elem.text.strip()
                            if menu_text and len(menu_text) < 50:  # 过滤掉太长的文本
                                menu_items.append({
                                    'text': menu_text,
                                    'element': elem,
                                    'xpath': self._get_element_xpath(elem)
                                })
                except:
                    continue
            
            # 去重
            unique_menus = []
            seen_texts = set()
            for item in menu_items:
                if item['text'] not in seen_texts:
                    unique_menus.append(item)
                    seen_texts.add(item['text'])
            
            print(f"找到 {len(unique_menus)} 个菜单项")
            for item in unique_menus:
                print(f"   - {item['text']}")
            
            return unique_menus

        except Exception as e:
            print(f"❌ 获取菜单项失败: {e}")
            return []

    def _analyze_links(self, page_data):
        """分析链接"""
        try:
            links = []
            elements = self.driver.find_elements(By.XPATH, "//a")
            for elem in elements:
                if elem.is_displayed() and elem.text.strip():
                    link_info = {
                        'text': elem.text.strip(),
                        'href': elem.get_attribute('href'),
                        'class': elem.get_attribute('class'),
                        'id': elem.get_attribute('id'),
                        'xpath': self._get_element_xpath(elem)
                    }
                    if link_info not in links:
                        links.append(link_info)

            page_data['elements']['links'] = links
            print(f"   找到 {len(links)} 个链接")
        except Exception as e:
            print(f"   分析链接失败: {e}")

    def _analyze_radios(self, page_data):
        """分析单选框"""
        try:
            radio_selectors = [
                "//input[@type='radio']",
                "//span[contains(@class, 'el-radio__inner')]",
                "//*[contains(@class, 'el-radio')]"
            ]

            radios = []
            for selector in radio_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    for elem in elements:
                        if elem.is_displayed():
                            label_text = ""
                            try:
                                parent = elem.find_element(By.XPATH, "./ancestor::*[contains(@class, 'radio')][1]")
                                label_text = parent.text.strip()
                            except:
                                pass

                            radio_info = {
                                'label_text': label_text,
                                'class': elem.get_attribute('class'),
                                'name': elem.get_attribute('name'),
                                'value': elem.get_attribute('value'),
                                'xpath': self._get_element_xpath(elem)
                            }
                            if radio_info not in radios:
                                radios.append(radio_info)
                except:
                    continue

            page_data['elements']['radios'] = radios
            print(f"   找到 {len(radios)} 个单选框")
        except Exception as e:
            print(f"   分析单选框失败: {e}")

    def _analyze_selects(self, page_data):
        """分析下拉框"""
        try:
            select_selectors = [
                "//select",
                "//*[contains(@class, 'el-select')]",
                "//*[contains(@class, 'dropdown')]"
            ]

            selects = []
            for selector in select_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    for elem in elements:
                        if elem.is_displayed():
                            select_info = {
                                'class': elem.get_attribute('class'),
                                'id': elem.get_attribute('id'),
                                'name': elem.get_attribute('name'),
                                'xpath': self._get_element_xpath(elem)
                            }
                            if select_info not in selects:
                                selects.append(select_info)
                except:
                    continue

            page_data['elements']['selects'] = selects
            print(f"   找到 {len(selects)} 个下拉框")
        except Exception as e:
            print(f"   分析下拉框失败: {e}")

    def _analyze_dialogs(self, page_data):
        """分析对话框"""
        try:
            dialog_selectors = [
                "//div[contains(@class, 'el-dialog')]",
                "//div[contains(@class, 'modal')]",
                "//div[contains(@class, 'dialog')]"
            ]

            dialogs = []
            for selector in dialog_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    for elem in elements:
                        if elem.is_displayed():
                            dialog_info = {
                                'class': elem.get_attribute('class'),
                                'id': elem.get_attribute('id'),
                                'text': elem.text[:100] if elem.text else "",
                                'xpath': self._get_element_xpath(elem)
                            }
                            if dialog_info not in dialogs:
                                dialogs.append(dialog_info)
                except:
                    continue

            page_data['elements']['dialogs'] = dialogs
            print(f"   找到 {len(dialogs)} 个对话框")
        except Exception as e:
            print(f"   分析对话框失败: {e}")

    def _analyze_menus(self, page_data):
        """分析菜单"""
        try:
            menu_selectors = [
                "//ul[contains(@class, 'el-menu')]",
                "//nav",
                "//*[contains(@class, 'menu')]",
                "//*[contains(@class, 'sidebar')]"
            ]

            menus = []
            for selector in menu_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    for elem in elements:
                        if elem.is_displayed():
                            menu_info = {
                                'class': elem.get_attribute('class'),
                                'id': elem.get_attribute('id'),
                                'xpath': self._get_element_xpath(elem)
                            }
                            if menu_info not in menus:
                                menus.append(menu_info)
                except:
                    continue

            page_data['elements']['menus'] = menus
            print(f"   找到 {len(menus)} 个菜单容器")
        except Exception as e:
            print(f"   分析菜单失败: {e}")

    def scan_all_pages(self):
        """扫描所有页面"""
        try:
            print("\n🚀 开始扫描AR控制中心所有页面...")

            # 获取所有菜单项
            menu_items = self.get_all_menu_items()

            # 扫描主页面
            main_url = self.driver.current_url
            if main_url not in self.scanned_urls:
                main_page_data = self.analyze_page_elements("主页面", main_url)
                if main_page_data:
                    self.scan_results["main_page"] = main_page_data
                    self.scanned_urls.add(main_url)

            # 遍历每个菜单项
            for i, menu_item in enumerate(menu_items):
                try:
                    print(f"\n{'='*20} 扫描菜单: {menu_item['text']} ({i+1}/{len(menu_items)}) {'='*20}")

                    # 点击菜单项
                    self.driver.execute_script("arguments[0].click();", menu_item['element'])
                    time.sleep(3)

                    # 检查URL是否已扫描
                    current_url = self.driver.current_url
                    if current_url in self.scanned_urls:
                        print(f"   ⚠️ 页面已扫描过，跳过: {current_url}")
                        continue

                    # 分析页面
                    page_data = self.analyze_page_elements(menu_item['text'], current_url)

                    if page_data:
                        safe_key = menu_item['text'].replace(' ', '_').replace('/', '_')
                        self.scan_results[safe_key] = page_data
                        self.scanned_urls.add(current_url)

                    # 检查是否有子菜单（只扫描新页面）
                    self._scan_submenus(menu_item['text'])

                except Exception as e:
                    print(f"   ❌ 扫描菜单 {menu_item['text']} 失败: {e}")
                    continue

            print(f"\n✅ 扫描完成！共扫描了 {len(self.scan_results)} 个页面")
            print(f"   已扫描URL: {len(self.scanned_urls)} 个")
            return True

        except Exception as e:
            print(f"❌ 扫描失败: {e}")
            return False

    def _scan_submenus(self, parent_menu):
        """扫描子菜单"""
        try:
            # 查找子菜单项
            submenu_selectors = [
                "//ul[contains(@class, 'el-menu')]//ul//span",
                "//*[contains(@class, 'submenu')]//span",
                "//*[contains(@class, 'sub')]//a"
            ]

            for selector in submenu_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    for elem in elements:
                        if elem.is_displayed() and elem.text.strip():
                            submenu_text = elem.text.strip()
                            if len(submenu_text) < 50:
                                try:
                                    self.driver.execute_script("arguments[0].click();", elem)
                                    time.sleep(2)

                                    current_url = self.driver.current_url

                                    # 检查是否已扫描过
                                    if current_url in self.scanned_urls:
                                        print(f"      子菜单页面已扫描过，跳过: {submenu_text}")
                                        continue

                                    page_data = self.analyze_page_elements(f"{parent_menu}-{submenu_text}", current_url)

                                    if page_data:
                                        safe_key = f"{parent_menu}_{submenu_text}".replace(' ', '_').replace('/', '_')
                                        self.scan_results[safe_key] = page_data
                                        self.scanned_urls.add(current_url)

                                except:
                                    continue
                except:
                    continue

        except Exception as e:
            print(f"   扫描子菜单失败: {e}")

    def generate_rules(self):
        """生成规则库"""
        try:
            print("\n📝 生成规则库...")

            # 生成JSON格式规则
            json_file = os.path.join(self.output_dir, "ar_ui_rules.json")
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(self.scan_results, f, ensure_ascii=False, indent=2)

            # 生成Markdown格式规则
            md_file = os.path.join(self.output_dir, "ar_ui_rules.md")
            self._generate_markdown_rules(md_file)

            # 生成Python规则类
            py_file = os.path.join(self.output_dir, "ar_ui_selectors.py")
            self._generate_python_rules(py_file)

            print(f"✅ 规则库生成完成:")
            print(f"   JSON规则: {json_file}")
            print(f"   Markdown文档: {md_file}")
            print(f"   Python选择器: {py_file}")

            return True

        except Exception as e:
            print(f"❌ 生成规则库失败: {e}")
            return False

    def _generate_markdown_rules(self, md_file):
        """生成Markdown格式的规则文档"""
        try:
            with open(md_file, 'w', encoding='utf-8') as f:
                f.write("# AR控制中心UI元素规则库\n\n")
                f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                f.write("## 页面概览\n\n")

                for page_key, page_data in self.scan_results.items():
                    f.write(f"- **{page_data['page_name']}** ({page_key})\n")

                f.write("\n---\n\n")

                # 为每个页面生成详细规则
                for page_key, page_data in self.scan_results.items():
                    f.write(f"## {page_data['page_name']}\n\n")
                    f.write(f"**页面URL**: {page_data['page_url']}\n\n")

                    # 按钮规则
                    if page_data['elements']['buttons']:
                        f.write("### 按钮选择器\n\n")
                        for btn in page_data['elements']['buttons']:
                            if btn['text']:
                                f.write(f"- **{btn['text']}**: `{btn['xpath']}`\n")
                        f.write("\n")

                    # 输入框规则
                    if page_data['elements']['inputs']:
                        f.write("### 输入框选择器\n\n")
                        for inp in page_data['elements']['inputs']:
                            name = inp['name'] or inp['id'] or inp['placeholder'] or "未知输入框"
                            f.write(f"- **{name}**: `{inp['xpath']}`\n")
                        f.write("\n")

                    # 表格规则
                    if page_data['elements']['tables']:
                        f.write("### 表格选择器\n\n")
                        for table in page_data['elements']['tables']:
                            headers = ", ".join(table['headers']) if table['headers'] else "无表头"
                            f.write(f"- **表格** (列: {headers}): `{table['xpath']}`\n")
                        f.write("\n")

                    # 复选框规则
                    if page_data['elements']['checkboxes']:
                        f.write("### 复选框选择器\n\n")
                        for cb in page_data['elements']['checkboxes']:
                            label = cb['label_text'] or "未知复选框"
                            f.write(f"- **{label}**: `{cb['xpath']}`\n")
                        f.write("\n")

                    f.write("---\n\n")

        except Exception as e:
            print(f"生成Markdown规则失败: {e}")

    def _generate_python_rules(self, py_file):
        """生成Python选择器类"""
        try:
            with open(py_file, 'w', encoding='utf-8') as f:
                f.write('#!/usr/bin/env python3\n')
                f.write('# -*- coding: utf-8 -*-\n')
                f.write('"""\n')
                f.write('AR控制中心UI选择器规则库\n')
                f.write(f'自动生成时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}\n')
                f.write('"""\n\n')

                f.write('class ARUISelectors:\n')
                f.write('    """AR控制中心UI选择器集合"""\n\n')

                # 为每个页面生成选择器类
                for page_key, page_data in self.scan_results.items():
                    class_name = page_key.upper().replace('-', '_')
                    f.write(f'    class {class_name}:\n')
                    f.write(f'        """页面: {page_data["page_name"]}"""\n')
                    f.write(f'        PAGE_URL = "{page_data["page_url"]}"\n\n')

                    # 按钮选择器
                    if page_data['elements']['buttons']:
                        f.write('        # 按钮选择器\n')
                        for btn in page_data['elements']['buttons']:
                            if btn['text']:
                                var_name = self._to_var_name(btn['text'])
                                f.write(f'        {var_name}_BUTTON = "{btn["xpath"]}"\n')
                        f.write('\n')

                    # 输入框选择器
                    if page_data['elements']['inputs']:
                        f.write('        # 输入框选择器\n')
                        for inp in page_data['elements']['inputs']:
                            name = inp['name'] or inp['id'] or inp['placeholder']
                            if name:
                                var_name = self._to_var_name(name)
                                f.write(f'        {var_name}_INPUT = "{inp["xpath"]}"\n')
                        f.write('\n')

                    # 表格选择器
                    if page_data['elements']['tables']:
                        f.write('        # 表格选择器\n')
                        for i, table in enumerate(page_data['elements']['tables']):
                            f.write(f'        TABLE_{i+1} = "{table["xpath"]}"\n')
                        f.write('\n')

                    f.write('\n')

                # 添加便捷方法
                f.write('    @staticmethod\n')
                f.write('    def get_button_selector(page, button_text):\n')
                f.write('        """根据页面和按钮文本获取选择器"""\n')
                f.write('        # 实现动态选择器查找逻辑\n')
                f.write('        pass\n\n')

        except Exception as e:
            print(f"生成Python规则失败: {e}")

    def _to_var_name(self, text):
        """将文本转换为变量名"""
        import re
        # 移除特殊字符，转换为大写，用下划线连接
        clean_text = re.sub(r'[^\w\s]', '', text)
        return re.sub(r'\s+', '_', clean_text.strip()).upper()

    def run_scan(self):
        """运行完整扫描"""
        print("🔍 AR控制中心页面扫描器")
        print("=" * 60)

        if not self.setup_driver():
            return False

        try:
            if not self.login():
                return False

            if not self.scan_all_pages():
                return False

            if not self.generate_rules():
                return False

            print("\n🎉 扫描完成！")
            print(f"共扫描了 {len(self.scan_results)} 个页面")
            print(f"规则库已保存到 {self.output_dir} 目录")

            return True

        except Exception as e:
            print(f"❌ 扫描过程失败: {e}")
            return False
        finally:
            if self.driver:
                self.driver.quit()
                print("🔚 浏览器已关闭")

def main():
    """主函数"""
    scanner = ARControlCenterScanner()
    success = scanner.run_scan()
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())
