# AR安全软件勒索软件检测测试

## 测试信息
```yaml
test_name: "AR勒索软件检测测试"
test_description: "测试AR软件对勒索软件的检测和阻止能力"
test_timeout: 300
expected_result: "AR软件成功检测并阻止勒索软件"
test_environment: "Windows 11 + 火绒安全软件"
```

## 测试步骤

### 步骤1：启动勒索软件模拟器
**操作描述**：双击桌面上的勒索软件模拟器图标，启动测试程序

**目标图片**：
![勒索软件模拟器图标](images/ransomware_simulator_icon.png)

**操作类型**：点击
**等待时间**：3秒
**预期结果**：程序启动，显示主界面

---

### 步骤2：点击开始测试按钮
**操作描述**：在程序主界面中，点击"开始测试"按钮启动勒索行为模拟

**目标图片**：
![开始测试按钮](images/start_test_button.png)

**操作类型**：点击
**等待时间**：2秒
**预期结果**：程序开始执行勒索软件模拟

---

### 步骤3：等待AR软件检测
**操作描述**：等待AR安全软件检测到勒索行为并弹出警报窗口

**目标图片**：
![AR警报窗口](images/ar_alert_window.png)

**操作类型**：等待
**等待时间**：30秒
**预期结果**：AR软件弹出勒索软件检测警报

---

### 步骤4：验证检测成功
**操作描述**：确认AR软件显示"勒索软件已被阻止"的提示信息

**目标图片**：
![勒索软件已阻止提示](images/ransomware_blocked_message.png)

**操作类型**：验证
**等待时间**：5秒
**预期结果**：显示勒索软件被成功阻止的消息

---

### 步骤5：截图保存结果
**操作描述**：对当前屏幕进行截图，保存测试结果

**操作类型**：截图
**保存文件名**：test_result_{timestamp}.png
**预期结果**：截图文件保存成功

## 测试验证点
```yaml
verification_points:
  - name: "AR警报触发"
    description: "AR软件是否及时检测到勒索行为"
    image: "images/ar_alert_window.png"
    critical: true
  
  - name: "勒索行为阻止"
    description: "AR软件是否成功阻止了勒索行为"
    image: "images/ransomware_blocked_message.png"
    critical: true
  
  - name: "系统文件保护"
    description: "系统重要文件是否受到保护"
    critical: true
```

## 测试数据
```yaml
test_data:
  target_files:
    - "C:\\Users\\<USER>\\Documents\\test_document.txt"
    - "C:\\Users\\<USER>\\Pictures\\test_image.jpg"
  
  ar_software:
    name: "火绒安全软件"
    version: "5.0.74"
    
  system_info:
    os: "Windows 11"
    architecture: "x64"
```
