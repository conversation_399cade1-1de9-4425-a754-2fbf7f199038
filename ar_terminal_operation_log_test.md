# AR终端操作日志测试

## 测试信息
```yaml
test_name: "AR控制中心终端操作日志检查测试"
test_description: "检查控制中心终端操作日志的显示和功能"
test_timeout: 180
expected_result: "终端操作日志正确显示各类终端操作记录"
test_environment: "Windows/Linux控制中心"
test_priority: "P1"
test_category: "事件日志/终端操作"
```

## 前置条件
- 执行过终端相关操作（扫描、升级、安装、卸载等）

## 测试步骤

### 步骤1：登录控制中心
**操作描述**：使用管理员账号登录AR控制中心

**目标图片**：
![登录控制中心](images/login_center_terminal_operation_log.png)

**操作类型**：登录
**等待时间**：5秒
**预期结果**：成功登录控制中心

---

### 步骤2：进入事件日志
**操作描述**：进入控制中心-事件日志菜单

**目标图片**：
![进入事件日志](images/enter_event_logs_terminal_op.png)

**操作类型**：导航
**等待时间**：3秒
**预期结果**：成功进入事件日志页面

---

### 步骤3：点击终端操作日志
**操作描述**：点击事件日志-终端操作

**目标图片**：
![点击终端操作日志](images/click_terminal_operation_logs.png)

**操作类型**：点击
**等待时间**：3秒
**预期结果**：进入终端操作日志页面

---

### 步骤4：查看终端操作日志列表
**操作描述**：查看终端操作日志的列表显示

**目标图片**：
![终端操作日志列表](images/terminal_operation_log_list.png)

**操作类型**：查看
**等待时间**：5秒
**预期结果**：有对应的终端操作的日志

---

### 步骤5：查看扫描操作日志
**操作描述**：查看终端扫描操作的日志记录

**目标图片**：
![扫描操作日志](images/scan_operation_logs.png)

**操作类型**：查看
**等待时间**：5秒
**预期结果**：扫描操作日志显示完整

---

### 步骤6：查看升级操作日志
**操作描述**：查看终端升级操作的日志记录

**目标图片**：
![升级操作日志](images/upgrade_operation_logs.png)

**操作类型**：查看
**等待时间**：5秒
**预期结果**：升级操作日志显示完整

---

### 步骤7：查看安装操作日志
**操作描述**：查看终端安装操作的日志记录

**目标图片**：
![安装操作日志](images/install_operation_logs.png)

**操作类型**：查看
**等待时间**：5秒
**预期结果**：安装操作日志显示完整

---

### 步骤8：查看卸载操作日志
**操作描述**：查看终端卸载操作的日志记录

**目标图片**：
![卸载操作日志](images/uninstall_operation_logs.png)

**操作类型**：查看
**等待时间**：5秒
**预期结果**：卸载操作日志显示完整

---

### 步骤9：查看策略同步日志
**操作描述**：查看策略同步操作的日志记录

**目标图片**：
![策略同步日志](images/policy_sync_logs.png)

**操作类型**：查看
**等待时间**：5秒
**预期结果**：策略同步日志显示完整

---

### 步骤10：查看日志详细信息
**操作描述**：点击某条日志，查看详细信息

**目标图片**：
![日志详细信息](images/operation_log_details.png)

**操作类型**：查看详情
**等待时间**：5秒
**预期结果**：日志详细信息显示完整

---

### 步骤11：验证日志字段完整性
**操作描述**：验证日志中各字段信息的完整性

**目标图片**：
![日志字段完整性](images/log_fields_completeness.png)

**操作类型**：验证
**等待时间**：5秒
**预期结果**：操作时间、终端信息、操作类型、操作结果等字段显示完整

---

### 步骤12：测试日志筛选功能
**操作描述**：使用时间、终端、操作类型等条件筛选操作日志

**目标图片**：
![操作日志筛选](images/operation_log_filter.png)

**操作类型**：筛选
**等待时间**：5秒
**预期结果**：筛选功能正常工作

---

### 步骤13：测试日志搜索功能
**操作描述**：使用关键词搜索特定的操作日志

**目标图片**：
![操作日志搜索](images/operation_log_search.png)

**操作类型**：搜索
**等待时间**：5秒
**预期结果**：搜索功能正常工作

---

### 步骤14：截图保存测试结果
**操作描述**：对终端操作日志测试进行截图保存

**操作类型**：截图
**保存文件名**：ar_terminal_operation_log_test_{timestamp}.png
**预期结果**：测试结果截图保存成功

## 测试验证点
```yaml
verification_points:
  - name: "终端操作日志页面正常"
    description: "终端操作日志页面能够正常加载"
    image: "images/click_terminal_operation_logs.png"
    critical: true
  
  - name: "操作日志记录完整"
    description: "有对应的终端操作的日志"
    image: "images/terminal_operation_log_list.png"
    critical: true
  
  - name: "扫描操作日志正确"
    description: "扫描操作日志显示完整"
    image: "images/scan_operation_logs.png"
    critical: true
    
  - name: "升级操作日志正确"
    description: "升级操作日志显示完整"
    image: "images/upgrade_operation_logs.png"
    critical: true
    
  - name: "安装操作日志正确"
    description: "安装操作日志显示完整"
    image: "images/install_operation_logs.png"
    critical: true
    
  - name: "卸载操作日志正确"
    description: "卸载操作日志显示完整"
    image: "images/uninstall_operation_logs.png"
    critical: true
    
  - name: "策略同步日志正确"
    description: "策略同步日志显示完整"
    image: "images/policy_sync_logs.png"
    critical: true
    
  - name: "日志字段完整"
    description: "日志各字段信息显示完整"
    image: "images/log_fields_completeness.png"
    critical: true
    
  - name: "筛选搜索功能正常"
    description: "日志筛选和搜索功能正常"
    image: "images/operation_log_filter.png"
    critical: false
```

## 测试数据
```yaml
test_data:
  operation_types:
    - "病毒扫描"
    - "软件升级"
    - "病毒库升级"
    - "客户端安装"
    - "客户端卸载"
    - "策略同步"
    - "配置更新"
    - "重启服务"
    
  log_fields:
    required_fields:
      - "操作时间"
      - "终端别名"
      - "终端IP"
      - "操作类型"
      - "操作结果"
      - "操作用户"
    
    optional_fields:
      - "操作详情"
      - "错误信息"
      - "执行时长"
      - "任务ID"
      
  operation_results:
    - "成功"
    - "失败"
    - "部分成功"
    - "超时"
    - "取消"
    
  filter_options:
    time_range:
      - "今天"
      - "最近7天"
      - "最近30天"
      - "自定义时间"
    
    operation_type:
      - "扫描操作"
      - "升级操作"
      - "安装操作"
      - "卸载操作"
      - "策略操作"
    
    result_filter:
      - "成功"
      - "失败"
      - "全部"
    
    terminal_filter:
      - "按终端别名"
      - "按IP地址"
      - "按分组"
      
  search_criteria:
    - "终端别名"
    - "操作类型"
    - "操作结果"
    - "操作用户"
    
  sample_log_entries:
    scan_operation:
      time: "2024-01-15 14:30:25"
      terminal: "PC-001"
      operation: "病毒扫描"
      result: "成功"
      details: "全盘扫描完成，发现2个威胁"
      
    upgrade_operation:
      time: "2024-01-15 15:00:00"
      terminal: "PC-002"
      operation: "软件升级"
      result: "成功"
      details: "客户端升级到v2.1.0.130"
```
