# AR Windows客户端卸载测试

## 测试信息
```yaml
test_name: "AR Windows客户端手动卸载测试"
test_description: "测试Windows系统上AR客户端的卸载功能"
test_timeout: 300
expected_result: "AR客户端成功卸载，相关文件和服务被清理"
test_environment: "Windows终端"
test_priority: "P0"
test_category: "客户端/安装卸载"
```

## 前置条件
- 终端安装成功Windows客户端

## 测试步骤

### 步骤1：打开控制面板
**操作描述**：终端进入控制面板进行手动卸载

**目标图片**：
![打开控制面板](images/open_control_panel.png)

**操作类型**：点击
**等待时间**：3秒
**预期结果**：成功打开Windows控制面板

---

### 步骤2：进入程序和功能
**操作描述**：在控制面板中点击"程序和功能"

**目标图片**：
![程序和功能](images/programs_and_features.png)

**操作类型**：点击
**等待时间**：3秒
**预期结果**：成功进入程序和功能界面

---

### 步骤3：找到AR客户端程序
**操作描述**：在程序列表中找到AR客户端程序

**目标图片**：
![找到AR客户端](images/find_ar_client_program.png)

**操作类型**：查找
**等待时间**：5秒
**预期结果**：在程序列表中找到AR客户端

---

### 步骤4：执行卸载操作
**操作描述**：右键点击AR客户端程序，选择卸载

**目标图片**：
![执行卸载操作](images/execute_uninstall_operation.png)

**操作类型**：右键卸载
**等待时间**：5秒
**预期结果**：开始执行卸载程序

---

### 步骤5：确认卸载
**操作描述**：在卸载确认对话框中点击确认

**目标图片**：
![确认卸载](images/confirm_uninstall.png)

**操作类型**：点击确认
**等待时间**：60秒
**预期结果**：卸载程序开始执行

---

### 步骤6：使用命令行卸载（备选方法）
**操作描述**：使用命令 msiexec /x {C0821885-3B3A-42B3-9978-E3CB7F82A7F3} 卸载

**目标图片**：
![命令行卸载](images/command_line_uninstall.png)

**操作类型**：命令执行
**等待时间**：60秒
**预期结果**：命令行卸载执行成功

---

### 步骤7：验证控制中心终端消失
**操作描述**：检查AR控制中心终端列表中，终端是否消失

**目标图片**：
![控制中心终端消失](images/center_terminal_disappeared.png)

**操作类型**：验证
**等待时间**：30秒
**预期结果**：客户端卸载成功，AR控制中心终端列表中，终端消失

---

### 步骤8：验证Windows文件夹删除
**操作描述**：检查Windows相关文件夹是否被删除

**目标图片**：
![Windows文件夹删除](images/windows_folders_deleted.png)

**操作类型**：验证
**等待时间**：5秒
**预期结果**：Windows相关文件夹被删除

---

### 步骤9：检查终端运行日志
**操作描述**：在AR控制中心的终端运行日志中查看相关日志记录

**目标图片**：
![终端运行日志](images/terminal_operation_log_uninstall.png)

**操作类型**：验证
**等待时间**：10秒
**预期结果**：AR控制中心的终端运行日志，有相关日志记录

---

### 步骤10：验证系统服务清理
**操作描述**：检查Windows系统中AR相关服务是否被清理

**目标图片**：
![系统服务清理](images/system_services_cleanup.png)

**操作类型**：验证
**等待时间**：5秒
**预期结果**：AR相关的系统服务被清理

---

### 步骤11：验证注册表清理
**操作描述**：检查Windows注册表中AR相关项是否被清理

**目标图片**：
![注册表清理](images/registry_cleanup.png)

**操作类型**：验证
**等待时间**：5秒
**预期结果**：注册表中AR相关项被清理

---

### 步骤12：截图保存测试结果
**操作描述**：对卸载过程和结果进行截图保存

**操作类型**：截图
**保存文件名**：ar_windows_client_uninstall_test_{timestamp}.png
**预期结果**：测试结果截图保存成功

## 测试验证点
```yaml
verification_points:
  - name: "卸载程序正常启动"
    description: "能够正常启动AR客户端卸载程序"
    image: "images/execute_uninstall_operation.png"
    critical: true
  
  - name: "卸载过程正常完成"
    description: "卸载过程能够正常完成"
    image: "images/confirm_uninstall.png"
    critical: true
  
  - name: "控制中心终端消失"
    description: "卸载后终端从控制中心列表中消失"
    image: "images/center_terminal_disappeared.png"
    critical: true
    
  - name: "安装文件夹被删除"
    description: "AR客户端安装文件夹被正确删除"
    image: "images/windows_folders_deleted.png"
    critical: true
    
  - name: "系统服务被清理"
    description: "AR相关的系统服务被正确清理"
    image: "images/system_services_cleanup.png"
    critical: true
    
  - name: "卸载日志正确记录"
    description: "控制中心正确记录终端卸载日志"
    image: "images/terminal_operation_log_uninstall.png"
    critical: true
```

## 测试数据
```yaml
test_data:
  uninstall_methods:
    - method: "控制面板卸载"
      description: "通过Windows控制面板程序和功能卸载"
    - method: "命令行卸载"
      command: "msiexec /x {C0821885-3B3A-42B3-9978-E3CB7F82A7F3}"
      description: "通过msiexec命令行卸载"
      
  cleanup_items:
    folders:
      - "C:\\Program Files (x86)\\mredrclient"
      - "C:\\ProgramData\\mredrclient"
    services:
      - "mr_mainsvc"
    registry_keys:
      - "HKEY_LOCAL_MACHINE\\SOFTWARE\\mredrclient"
      - "HKEY_CURRENT_USER\\SOFTWARE\\mredrclient"
      
  verification_points:
    - "程序列表中AR客户端消失"
    - "安装目录被删除"
    - "系统服务被移除"
    - "注册表项被清理"
    - "控制中心终端列表更新"
    - "卸载日志记录完整"
```
