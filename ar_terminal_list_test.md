# AR终端列表显示测试

## 测试信息
```yaml
test_name: "AR控制中心终端列表显示内容检查"
test_description: "检查控制中心终端列表的显示内容和功能"
test_timeout: 180
expected_result: "终端列表正确显示所有终端信息，筛选和字段显示功能正常"
test_environment: "Windows/Linux控制中心"
test_priority: "P1"
test_category: "控制中心/终端列表"
```

## 前置条件
- 已安装客户端
- 控制中心中有多个终端

## 测试步骤

### 步骤1：登录控制中心
**操作描述**：使用浏览器登录AR控制中心

**目标图片**：
![登录控制中心](images/login_center_terminal_list.png)

**操作类型**：登录
**等待时间**：5秒
**预期结果**：成功登录控制中心

---

### 步骤2：进入终端列表
**操作描述**：进入控制中心-终端管理-终端列表，查看列表内容

**目标图片**：
![终端列表界面](images/terminal_list_interface.png)

**操作类型**：导航
**等待时间**：3秒
**预期结果**：成功进入终端列表页面

---

### 步骤3：检查终端状态显示
**操作描述**：查看终端别名的颜色显示状态

**目标图片**：
![终端状态显示](images/terminal_status_display.png)

**操作类型**：验证
**等待时间**：5秒
**预期结果**：防勒索客户端在线状态为绿色字体，离线为灰色字体，红色为有风险终端，黄色表示有扫描任务在进行

---

### 步骤4：检查软件版本显示
**操作描述**：查看防勒索软件版本的显示颜色

**目标图片**：
![软件版本显示](images/software_version_display.png)

**操作类型**：验证
**等待时间**：3秒
**预期结果**：最新防勒索软件版本为黑色字体，需要升级版本为红色字体

---

### 步骤5：测试别名筛选功能
**操作描述**：通过搜索别名信息查询相关客户端

**目标图片**：
![别名筛选功能](images/filter_by_alias.png)

**操作类型**：输入搜索
**等待时间**：3秒
**预期结果**：通过搜索别名等信息可查询到相关客户端

---

### 步骤6：测试IP筛选功能
**操作描述**：通过搜索IP地址查询相关客户端

**目标图片**：
![IP筛选功能](images/filter_by_ip.png)

**操作类型**：输入搜索
**等待时间**：3秒
**预期结果**：通过搜索IP等信息可查询到相关客户端

---

### 步骤7：测试操作系统筛选
**操作描述**：通过操作系统类型筛选终端

**目标图片**：
![操作系统筛选](images/filter_by_os.png)

**操作类型**：筛选操作
**等待时间**：3秒
**预期结果**：能够按操作系统类型正确筛选终端

---

### 步骤8：测试自定义列表显示字段
**操作描述**：点击"自定义列表显示字段"可调出所需字段信息

**目标图片**：
![自定义显示字段](images/custom_display_fields.png)

**操作类型**：点击配置
**等待时间**：3秒
**预期结果**：点击"自定义列表显示字段"可调出所需字段信息

---

### 步骤9：调整显示字段
**操作描述**：选择或隐藏某些字段，测试字段显示调整功能

**目标图片**：
![调整显示字段](images/adjust_display_fields.png)

**操作类型**：字段配置
**等待时间**：3秒
**预期结果**：能够成功调出或者隐藏某些字段

---

### 步骤10：验证字段调整效果
**操作描述**：确认字段调整后的显示效果

**目标图片**：
![字段调整效果](images/field_adjustment_result.png)

**操作类型**：验证
**等待时间**：3秒
**预期结果**：列表显示按照设置的字段正确显示

---

### 步骤11：截图保存测试结果
**操作描述**：对终端列表显示效果进行截图保存

**操作类型**：截图
**保存文件名**：ar_terminal_list_test_{timestamp}.png
**预期结果**：测试结果截图保存成功

## 测试验证点
```yaml
verification_points:
  - name: "终端列表正常加载"
    description: "终端列表页面能够正常加载显示"
    image: "images/terminal_list_interface.png"
    critical: true
  
  - name: "终端状态颜色正确"
    description: "终端状态用不同颜色正确显示"
    image: "images/terminal_status_display.png"
    critical: true
  
  - name: "软件版本颜色正确"
    description: "软件版本状态用不同颜色正确显示"
    image: "images/software_version_display.png"
    critical: true
    
  - name: "别名筛选功能正常"
    description: "通过别名能够正确筛选终端"
    image: "images/filter_by_alias.png"
    critical: true
    
  - name: "IP筛选功能正常"
    description: "通过IP地址能够正确筛选终端"
    image: "images/filter_by_ip.png"
    critical: true
    
  - name: "自定义字段功能正常"
    description: "自定义列表显示字段功能正常工作"
    image: "images/custom_display_fields.png"
    critical: true
```

## 测试数据
```yaml
test_data:
  terminal_status_colors:
    online: "绿色字体"
    offline: "灰色字体"
    risk: "红色字体"
    scanning: "黄色字体"
    
  software_version_colors:
    latest: "黑色字体"
    need_upgrade: "红色字体"
    
  filter_criteria:
    - "终端别名"
    - "IP地址"
    - "操作系统"
    
  display_fields:
    - "终端别名"
    - "IP地址"
    - "操作系统"
    - "软件版本"
    - "在线状态"
    - "最后心跳时间"
    - "防护策略"
    - "硬件信息"
    
  test_search_terms:
    alias: "test-pc-01"
    ip: "*************"
    os: "Windows 11"
```
