# AR告警通知测试

## 测试信息
```yaml
test_name: "AR控制中心告警通知测试"
test_description: "测试AR控制中心的告警通知功能"
test_timeout: 600
expected_result: "告警通知功能正常，能够及时准确地发送告警信息"
test_environment: "Windows/Linux控制中心"
test_priority: "P0"
test_category: "告警管理测试"
```

## 前置条件
- AR控制中心已安装并运行
- 邮件服务器配置正确（如需邮件通知）

## 测试步骤

### 步骤1：登录控制中心
**操作描述**：使用管理员账号登录AR控制中心

**目标图片**：
![登录控制中心](images/login_center_alert_notification.png)

**操作类型**：登录
**等待时间**：5秒
**预期结果**：成功登录控制中心

---

### 步骤2：进入告警设置
**操作描述**：进入控制中心-系统管理-告警设置

**目标图片**：
![进入告警设置](images/enter_alert_settings.png)

**操作类型**：导航
**等待时间**：3秒
**预期结果**：成功进入告警设置页面

---

### 步骤3：查看告警类型配置
**操作描述**：查看系统支持的告警类型配置

**目标图片**：
![告警类型配置](images/alert_types_configuration.png)

**操作类型**：配置查看
**等待时间**：5秒
**预期结果**：显示各种告警类型的配置选项

---

### 步骤4：配置病毒检测告警
**操作描述**：配置病毒检测相关的告警设置

**目标图片**：
![病毒检测告警配置](images/virus_detection_alert_config.png)

**操作类型**：告警配置
**等待时间**：30秒
**预期结果**：成功配置病毒检测告警

---

### 步骤5：配置系统异常告警
**操作描述**：配置系统异常相关的告警设置

**目标图片**：
![系统异常告警配置](images/system_exception_alert_config.png)

**操作类型**：告警配置
**等待时间**：30秒
**预期结果**：成功配置系统异常告警

---

### 步骤6：配置终端离线告警
**操作描述**：配置终端离线相关的告警设置

**目标图片**：
![终端离线告警配置](images/terminal_offline_alert_config.png)

**操作类型**：告警配置
**等待时间**：30秒
**预期结果**：成功配置终端离线告警

---

### 步骤7：配置通知方式
**操作描述**：配置告警的通知方式（邮件、短信等）

**目标图片**：
![通知方式配置](images/notification_methods_config.png)

**操作类型**：通知配置
**等待时间**：60秒
**预期结果**：成功配置告警通知方式

---

### 步骤8：设置通知接收人
**操作描述**：设置告警通知的接收人员

**目标图片**：
![通知接收人设置](images/notification_recipients_setup.png)

**操作类型**：接收人配置
**等待时间**：30秒
**预期结果**：成功设置通知接收人

---

### 步骤9：测试病毒检测告警
**操作描述**：触发病毒检测事件，测试告警通知

**目标图片**：
![病毒检测告警测试](images/virus_detection_alert_test.png)

**操作类型**：告警测试
**等待时间**：120秒
**预期结果**：病毒检测告警正常触发和通知

---

### 步骤10：测试系统异常告警
**操作描述**：模拟系统异常，测试告警通知

**目标图片**：
![系统异常告警测试](images/system_exception_alert_test.png)

**操作类型**：告警测试
**等待时间**：120秒
**预期结果**：系统异常告警正常触发和通知

---

### 步骤11：测试终端离线告警
**操作描述**：模拟终端离线，测试告警通知

**目标图片**：
![终端离线告警测试](images/terminal_offline_alert_test.png)

**操作类型**：告警测试
**等待时间**：300秒
**预期结果**：终端离线告警正常触发和通知

---

### 步骤12：验证邮件通知
**操作描述**：验证邮件告警通知是否正常发送

**目标图片**：
![邮件通知验证](images/email_notification_verification.png)

**操作类型**：邮件验证
**等待时间**：60秒
**预期结果**：邮件告警通知正常发送

---

### 步骤13：查看告警历史
**操作描述**：查看系统告警的历史记录

**目标图片**：
![告警历史查看](images/alert_history_view.png)

**操作类型**：历史查看
**等待时间**：30秒
**预期结果**：告警历史记录显示完整

---

### 步骤14：测试告警确认功能
**操作描述**：测试告警的确认和处理功能

**目标图片**：
![告警确认功能](images/alert_acknowledgment_function.png)

**操作类型**：告警确认
**等待时间**：30秒
**预期结果**：告警确认功能正常工作

---

### 步骤15：截图保存测试结果
**操作描述**：对告警通知测试进行截图保存

**操作类型**：截图
**保存文件名**：ar_alert_notification_test_{timestamp}.png
**预期结果**：测试结果截图保存成功

## 测试验证点
```yaml
verification_points:
  - name: "告警类型配置完整"
    description: "系统支持的告警类型配置完整"
    image: "images/alert_types_configuration.png"
    critical: true
  
  - name: "病毒检测告警配置正常"
    description: "病毒检测告警配置功能正常"
    image: "images/virus_detection_alert_config.png"
    critical: true
  
  - name: "系统异常告警配置正常"
    description: "系统异常告警配置功能正常"
    image: "images/system_exception_alert_config.png"
    critical: true
    
  - name: "终端离线告警配置正常"
    description: "终端离线告警配置功能正常"
    image: "images/terminal_offline_alert_config.png"
    critical: true
    
  - name: "通知方式配置正常"
    description: "告警通知方式配置功能正常"
    image: "images/notification_methods_config.png"
    critical: true
    
  - name: "通知接收人设置正常"
    description: "通知接收人设置功能正常"
    image: "images/notification_recipients_setup.png"
    critical: true
    
  - name: "病毒检测告警触发正常"
    description: "病毒检测告警正常触发和通知"
    image: "images/virus_detection_alert_test.png"
    critical: true
    
  - name: "系统异常告警触发正常"
    description: "系统异常告警正常触发和通知"
    image: "images/system_exception_alert_test.png"
    critical: true
    
  - name: "终端离线告警触发正常"
    description: "终端离线告警正常触发和通知"
    image: "images/terminal_offline_alert_test.png"
    critical: true
    
  - name: "邮件通知功能正常"
    description: "邮件告警通知功能正常"
    image: "images/email_notification_verification.png"
    critical: true
    
  - name: "告警历史记录完整"
    description: "告警历史记录显示完整"
    image: "images/alert_history_view.png"
    critical: true
    
  - name: "告警确认功能正常"
    description: "告警确认功能正常工作"
    image: "images/alert_acknowledgment_function.png"
    critical: true
```

## 测试数据
```yaml
test_data:
  alert_types:
    security_alerts:
      - "病毒检测告警"
      - "勒索软件告警"
      - "恶意行为告警"
      - "入侵检测告警"
    
    system_alerts:
      - "服务异常告警"
      - "系统资源告警"
      - "数据库异常告警"
      - "网络异常告警"
    
    operational_alerts:
      - "终端离线告警"
      - "任务失败告警"
      - "升级失败告警"
      - "授权到期告警"
      
  notification_methods:
    - "页面通知"
    - "邮件通知"
    - "短信通知"
    - "微信通知"
    - "钉钉通知"
    
  alert_levels:
    - "紧急 (Critical)"
    - "重要 (High)"
    - "中等 (Medium)"
    - "低级 (Low)"
    - "信息 (Info)"
    
  notification_settings:
    email_config:
      - "SMTP服务器"
      - "端口号"
      - "用户名"
      - "密码"
      - "发送方邮箱"
    
    sms_config:
      - "短信网关"
      - "API密钥"
      - "签名"
      
  recipient_types:
    - "系统管理员"
    - "安全管理员"
    - "运维人员"
    - "业务负责人"
    
  alert_triggers:
    - "实时触发"
    - "批量触发"
    - "定时检查触发"
    - "阈值触发"
    
  alert_content:
    - "告警时间"
    - "告警类型"
    - "告警级别"
    - "告警描述"
    - "影响范围"
    - "处理建议"
    
  alert_status:
    - "未处理"
    - "处理中"
    - "已处理"
    - "已忽略"
    - "已关闭"
```
