#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度复选框分析程序
分析隐藏的复选框和Element UI的实现方式
"""

import time
import os
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options

class DeepCheckboxAnalyzer:
    def __init__(self):
        """初始化分析器"""
        self.driver = None
        self.wait = None
        
        # 测试配置
        self.config = {
            'url': 'http://172.16.103.202:8088',
            'username': 'admin',
            'password': 'Admin.2022',
            'timeout': 5
        }
    
    def setup_driver(self):
        """设置Chrome浏览器驱动"""
        try:
            chrome_options = Options()
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--window-size=1920,1080")
            
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.maximize_window()
            self.wait = WebDriverWait(self.driver, self.config['timeout'])
            
            print("✅ 浏览器驱动设置成功")
            return True
            
        except Exception as e:
            print(f"❌ 浏览器驱动设置失败: {e}")
            return False
    
    def login_and_navigate(self):
        """登录并导航到终端列表"""
        try:
            # 登录
            self.driver.get(self.config['url'])
            time.sleep(3)
            
            username_input = self.wait.until(EC.element_to_be_clickable((By.NAME, "username")))
            username_input.clear()
            username_input.send_keys(self.config['username'])
            
            password_input = self.driver.find_element(By.ID, "password")
            password_input.clear()
            password_input.send_keys(self.config['password'])
            
            login_button = self.driver.find_element(By.XPATH, "//button[@type='submit']")
            login_button.click()
            time.sleep(5)
            
            # 导航到终端列表
            terminal_menu = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, "//span[contains(text(), '终端管理')]"))
            )
            terminal_menu.click()
            time.sleep(2)
            
            terminal_list = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, "//span[contains(text(), '终端列表')]"))
            )
            terminal_list.click()
            time.sleep(3)
            
            print("✅ 成功登录并导航到终端列表")
            return True
            
        except Exception as e:
            print(f"❌ 登录或导航失败: {e}")
            return False
    
    def analyze_hidden_checkboxes(self):
        """分析隐藏的复选框"""
        try:
            print("\n🔍 深度分析隐藏复选框...")
            
            # 1. 分析所有复选框的CSS属性
            all_checkboxes = self.driver.find_elements(By.XPATH, "//input[@type='checkbox']")
            print(f"\n找到 {len(all_checkboxes)} 个复选框，分析CSS属性:")
            
            for i, checkbox in enumerate(all_checkboxes[:10]):  # 只分析前10个
                try:
                    # 获取CSS属性
                    display = self.driver.execute_script("return window.getComputedStyle(arguments[0]).display;", checkbox)
                    visibility = self.driver.execute_script("return window.getComputedStyle(arguments[0]).visibility;", checkbox)
                    opacity = self.driver.execute_script("return window.getComputedStyle(arguments[0]).opacity;", checkbox)
                    position = self.driver.execute_script("return window.getComputedStyle(arguments[0]).position;", checkbox)
                    
                    print(f"  复选框 {i+1}: display={display}, visibility={visibility}, opacity={opacity}, position={position}")
                    
                    # 尝试强制显示
                    if display == 'none' or visibility == 'hidden' or opacity == '0':
                        self.driver.execute_script("""
                            arguments[0].style.display = 'block';
                            arguments[0].style.visibility = 'visible';
                            arguments[0].style.opacity = '1';
                            arguments[0].style.position = 'static';
                        """, checkbox)
                        print(f"    已尝试强制显示复选框 {i+1}")
                        
                except Exception as e:
                    print(f"  复选框 {i+1}: 分析失败 - {e}")
            
            # 2. 查找Element UI的复选框实现
            print(f"\n🔍 查找Element UI复选框实现:")
            
            # 查找el-checkbox组件
            el_checkboxes = self.driver.find_elements(By.XPATH, "//*[contains(@class, 'el-checkbox')]")
            print(f"找到 {len(el_checkboxes)} 个el-checkbox组件")
            
            for i, el_checkbox in enumerate(el_checkboxes[:5]):
                try:
                    is_displayed = el_checkbox.is_displayed()
                    class_name = el_checkbox.get_attribute("class")
                    print(f"  el-checkbox {i+1}: 显示={is_displayed}, class='{class_name}'")
                    
                    if is_displayed:
                        # 尝试点击
                        print(f"    尝试点击 el-checkbox {i+1}")
                        self.driver.execute_script("arguments[0].click();", el_checkbox)
                        time.sleep(1)
                        
                except Exception as e:
                    print(f"  el-checkbox {i+1}: 操作失败 - {e}")
            
            # 3. 查找表格行的点击区域
            print(f"\n🔍 查找表格行点击区域:")
            
            # 查找表格行
            table_rows = self.driver.find_elements(By.XPATH, "//tr[contains(@class, 'el-table__row')]")
            print(f"找到 {len(table_rows)} 个表格行")
            
            for i, row in enumerate(table_rows[:3]):
                try:
                    is_displayed = row.is_displayed()
                    row_text = row.text[:100]
                    print(f"  行 {i+1}: 显示={is_displayed}, 文本='{row_text}...'")
                    
                    if is_displayed:
                        # 查找行内的所有可点击元素
                        clickable_elements = row.find_elements(By.XPATH, ".//*[@class or @onclick or contains(@class, 'checkbox') or contains(@class, 'el-')]")
                        print(f"    行内可点击元素: {len(clickable_elements)} 个")
                        
                        for j, elem in enumerate(clickable_elements[:3]):
                            tag = elem.tag_name
                            class_name = elem.get_attribute("class")
                            print(f"      元素 {j+1}: <{tag} class='{class_name}'>")
                            
                except Exception as e:
                    print(f"  行 {i+1}: 分析失败 - {e}")
            
            return True
            
        except Exception as e:
            print(f"❌ 深度分析失败: {e}")
            return False
    
    def try_alternative_selection_methods(self):
        """尝试其他选择方法"""
        try:
            print("\n🖱️ 尝试其他选择方法...")
            
            # 方法1: 点击表格行的第一列
            print("\n方法1: 点击表格行第一列")
            first_cells = self.driver.find_elements(By.XPATH, "//tr[contains(@class, 'el-table__row')]//td[1]")
            
            if first_cells:
                for i, cell in enumerate(first_cells[:2]):
                    try:
                        if cell.is_displayed():
                            print(f"  尝试点击第 {i+1} 行第一列")
                            self.driver.execute_script("arguments[0].click();", cell)
                            time.sleep(1)
                            
                            # 检查是否有选中效果
                            row = cell.find_element(By.XPATH, "./ancestor::tr[1]")
                            row_class = row.get_attribute("class")
                            print(f"    点击后行class: {row_class}")
                            
                    except Exception as e:
                        print(f"  第 {i+1} 行点击失败: {e}")
            
            # 方法2: 寻找隐藏的选择机制
            print("\n方法2: 寻找隐藏的选择机制")
            
            # 执行JavaScript查找选择函数
            js_result = self.driver.execute_script("""
                // 查找Vue组件的选择方法
                var tables = document.querySelectorAll('.el-table');
                var methods = [];
                
                tables.forEach(function(table, index) {
                    if (table.__vue__) {
                        var vue = table.__vue__;
                        methods.push('表格' + (index+1) + '的Vue方法: ' + Object.keys(vue.$options.methods || {}).join(', '));
                    }
                });
                
                return methods;
            """)
            
            print(f"  JavaScript分析结果: {js_result}")
            
            return True
            
        except Exception as e:
            print(f"❌ 其他选择方法失败: {e}")
            return False
    
    def run_analysis(self):
        """运行深度分析"""
        print("🔍 深度复选框分析程序启动")
        print("=" * 50)
        
        if not self.setup_driver():
            return False
        
        try:
            if not self.login_and_navigate():
                return False
            
            if not self.analyze_hidden_checkboxes():
                return False
            
            if not self.try_alternative_selection_methods():
                return False
            
            print("\n🎉 深度分析完成！")
            
            # 保持浏览器打开以便观察
            print("浏览器将在15秒后关闭...")
            time.sleep(15)
            
            return True
            
        except Exception as e:
            print(f"❌ 分析过程中发生错误: {e}")
            return False
        finally:
            if self.driver:
                self.driver.quit()
                print("🔚 浏览器已关闭")

def main():
    """主函数"""
    analyzer = DeepCheckboxAnalyzer()
    success = analyzer.run_analysis()
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())
