# AR终端详情显示测试

## 测试信息
```yaml
test_name: "AR控制中心终端详情显示内容检查"
test_description: "检查控制中心终端详情页面的显示内容和功能"
test_timeout: 180
expected_result: "终端详情正确显示所有信息，修改功能正常工作"
test_environment: "Windows/Linux控制中心"
test_priority: "P1"
test_category: "控制中心/终端列表"
```

## 前置条件
- 已安装客户端

## 测试步骤

### 步骤1：登录控制中心
**操作描述**：使用浏览器登录AR控制中心

**目标图片**：
![登录控制中心](images/login_center_terminal_details.png)

**操作类型**：登录
**等待时间**：5秒
**预期结果**：成功登录控制中心

---

### 步骤2：进入终端列表
**操作描述**：进入控制中心-终端管理-终端列表

**目标图片**：
![进入终端列表](images/enter_terminal_list_details.png)

**操作类型**：导航
**等待时间**：3秒
**预期结果**：成功进入终端列表页面

---

### 步骤3：选择客户端查看详情
**操作描述**：点击客户端查看详细信息

**目标图片**：
![点击查看详情](images/click_view_terminal_details.png)

**操作类型**：点击
**等待时间**：3秒
**预期结果**：打开终端详情页面或对话框

---

### 步骤4：查看终端别名信息
**操作描述**：查看终端别名显示和编辑功能

**目标图片**：
![终端别名信息](images/terminal_alias_info.png)

**操作类型**：查看
**等待时间**：3秒
**预期结果**：正确显示终端别名信息

---

### 步骤5：修改终端别名
**操作描述**：测试修改终端别名功能

**目标图片**：
![修改终端别名](images/modify_terminal_alias.png)

**操作类型**：编辑
**等待时间**：5秒
**预期结果**：可修改终端别名

---

### 步骤6：查看OS版本信息
**操作描述**：查看操作系统版本信息显示

**目标图片**：
![OS版本信息](images/os_version_info.png)

**操作类型**：查看
**等待时间**：3秒
**预期结果**：正确显示OS版本信息

---

### 步骤7：查看硬件信息
**操作描述**：查看终端硬件信息显示

**目标图片**：
![硬件信息显示](images/hardware_info_display.png)

**操作类型**：查看
**等待时间**：3秒
**预期结果**：正确显示硬件信息

---

### 步骤8：查看防护策略信息
**操作描述**：查看当前终端的防护策略信息

**目标图片**：
![防护策略信息](images/protection_policy_info.png)

**操作类型**：查看
**等待时间**：3秒
**预期结果**：正确显示防护策略等信息

---

### 步骤9：查看网络信息
**操作描述**：查看终端的网络连接信息

**目标图片**：
![网络信息显示](images/network_info_display.png)

**操作类型**：查看
**等待时间**：3秒
**预期结果**：正确显示IP地址、MAC地址等网络信息

---

### 步骤10：查看软件版本信息
**操作描述**：查看AR客户端软件版本信息

**目标图片**：
![软件版本信息](images/software_version_info.png)

**操作类型**：查看
**等待时间**：3秒
**预期结果**：正确显示软件版本和病毒库版本

---

### 步骤11：保存修改
**操作描述**：如果修改了终端别名，保存修改

**目标图片**：
![保存修改](images/save_modifications.png)

**操作类型**：保存
**等待时间**：5秒
**预期结果**：修改成功保存

---

### 步骤12：验证修改效果
**操作描述**：返回终端列表验证修改是否生效

**目标图片**：
![验证修改效果](images/verify_modification_effect.png)

**操作类型**：验证
**等待时间**：5秒
**预期结果**：修改在终端列表中生效

---

### 步骤13：截图保存测试结果
**操作描述**：对终端详情显示进行截图保存

**操作类型**：截图
**保存文件名**：ar_terminal_details_test_{timestamp}.png
**预期结果**：测试结果截图保存成功

## 测试验证点
```yaml
verification_points:
  - name: "终端详情页面正常打开"
    description: "终端详情页面能够正常打开显示"
    image: "images/click_view_terminal_details.png"
    critical: true
  
  - name: "终端别名正确显示"
    description: "终端别名信息正确显示"
    image: "images/terminal_alias_info.png"
    critical: true
  
  - name: "终端别名可修改"
    description: "终端别名可以正常修改"
    image: "images/modify_terminal_alias.png"
    critical: true
    
  - name: "OS版本信息正确"
    description: "操作系统版本信息正确显示"
    image: "images/os_version_info.png"
    critical: true
    
  - name: "硬件信息完整"
    description: "硬件信息完整显示"
    image: "images/hardware_info_display.png"
    critical: true
    
  - name: "防护策略信息正确"
    description: "防护策略信息正确显示"
    image: "images/protection_policy_info.png"
    critical: true
    
  - name: "修改功能正常工作"
    description: "修改功能能够正常工作并保存"
    image: "images/save_modifications.png"
    critical: true
```

## 测试数据
```yaml
test_data:
  terminal_info_fields:
    basic_info:
      - "终端别名"
      - "IP地址"
      - "MAC地址"
      - "在线状态"
      - "最后心跳时间"
    
    system_info:
      - "操作系统类型"
      - "操作系统版本"
      - "系统架构"
      - "内核版本"
    
    hardware_info:
      - "CPU信息"
      - "内存大小"
      - "硬盘信息"
      - "网卡信息"
    
    software_info:
      - "AR客户端版本"
      - "病毒库版本"
      - "最后更新时间"
    
    policy_info:
      - "当前防护策略"
      - "策略生效时间"
      - "策略来源"
      
  editable_fields:
    - "终端别名"
    
  test_alias_change:
    original: "PC-001"
    modified: "Test-PC-001"
    
  expected_display_format:
    ip_address: "*************"
    mac_address: "00:11:22:33:44:55"
    os_info: "Windows 11 Pro"
    cpu_info: "Intel Core i7-12700K"
    memory_info: "16GB DDR4"
```
