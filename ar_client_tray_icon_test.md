# AR客户端托盘图标测试

## 测试信息
```yaml
test_name: "AR客户端托盘图标功能测试"
test_description: "测试AR客户端托盘图标的显示和功能"
test_timeout: 180
expected_result: "托盘图标正确显示，右键菜单功能正常"
test_environment: "Windows/Linux客户端"
test_priority: "P1"
test_category: "客户端功能测试"
```

## 前置条件
- 已安装AR客户端
- 客户端服务正在运行

## 测试步骤

### 步骤1：检查托盘图标显示
**操作描述**：检查系统托盘区域是否显示AR客户端图标

**目标图片**：
![托盘图标显示](images/tray_icon_display.png)

**操作类型**：查看
**等待时间**：5秒
**预期结果**：系统托盘区域显示AR客户端图标

---

### 步骤2：验证图标状态指示
**操作描述**：验证托盘图标是否正确指示客户端状态

**目标图片**：
![图标状态指示](images/icon_status_indication.png)

**操作类型**：验证
**等待时间**：5秒
**预期结果**：图标颜色或样式正确指示客户端状态

---

### 步骤3：测试左键单击功能
**操作描述**：左键单击托盘图标

**目标图片**：
![左键单击功能](images/left_click_function.png)

**操作类型**：点击
**等待时间**：3秒
**预期结果**：打开客户端主界面或显示状态信息

---

### 步骤4：测试右键菜单
**操作描述**：右键单击托盘图标，查看右键菜单

**目标图片**：
![右键菜单](images/right_click_menu.png)

**操作类型**：右键点击
**等待时间**：3秒
**预期结果**：显示右键上下文菜单

---

### 步骤5：测试打开主界面功能
**操作描述**：通过右键菜单打开主界面

**目标图片**：
![打开主界面](images/open_main_interface_from_tray.png)

**操作类型**：菜单操作
**等待时间**：5秒
**预期结果**：成功打开客户端主界面

---

### 步骤6：测试快速扫描功能
**操作描述**：通过右键菜单执行快速扫描（如果有此功能）

**目标图片**：
![快速扫描功能](images/quick_scan_from_tray.png)

**操作类型**：菜单操作
**等待时间**：30秒
**预期结果**：快速扫描功能正常执行

---

### 步骤7：测试暂停防护功能
**操作描述**：通过右键菜单暂停防护（如果有此功能）

**目标图片**：
![暂停防护功能](images/pause_protection_from_tray.png)

**操作类型**：菜单操作
**等待时间**：5秒
**预期结果**：防护功能暂停，图标状态改变

---

### 步骤8：测试恢复防护功能
**操作描述**：通过右键菜单恢复防护

**目标图片**：
![恢复防护功能](images/resume_protection_from_tray.png)

**操作类型**：菜单操作
**等待时间**：5秒
**预期结果**：防护功能恢复，图标状态恢复正常

---

### 步骤9：测试查看日志功能
**操作描述**：通过右键菜单查看安全日志（如果有此功能）

**目标图片**：
![查看日志功能](images/view_logs_from_tray.png)

**操作类型**：菜单操作
**等待时间**：5秒
**预期结果**：打开安全日志查看界面

---

### 步骤10：测试关于信息功能
**操作描述**：通过右键菜单查看关于信息

**目标图片**：
![关于信息功能](images/about_info_from_tray.png)

**操作类型**：菜单操作
**等待时间**：3秒
**预期结果**：显示客户端版本和关于信息

---

### 步骤11：测试退出功能
**操作描述**：通过右键菜单退出客户端（注意：可能只是退出界面，服务继续运行）

**目标图片**：
![退出功能](images/exit_from_tray.png)

**操作类型**：菜单操作
**等待时间**：5秒
**预期结果**：客户端界面退出，托盘图标可能消失或变化

---

### 步骤12：验证服务持续运行
**操作描述**：验证退出界面后，客户端服务是否继续运行

**目标图片**：
![服务持续运行](images/service_continues_running.png)

**操作类型**：验证
**等待时间**：10秒
**预期结果**：客户端服务继续运行，防护功能正常

---

### 步骤13：截图保存测试结果
**操作描述**：对托盘图标功能测试进行截图保存

**操作类型**：截图
**保存文件名**：ar_client_tray_icon_test_{timestamp}.png
**预期结果**：测试结果截图保存成功

## 测试验证点
```yaml
verification_points:
  - name: "托盘图标正常显示"
    description: "系统托盘区域正常显示AR客户端图标"
    image: "images/tray_icon_display.png"
    critical: true
  
  - name: "图标状态指示正确"
    description: "托盘图标正确指示客户端状态"
    image: "images/icon_status_indication.png"
    critical: true
  
  - name: "左键单击功能正常"
    description: "左键单击托盘图标功能正常"
    image: "images/left_click_function.png"
    critical: true
    
  - name: "右键菜单显示正常"
    description: "右键菜单能够正常显示"
    image: "images/right_click_menu.png"
    critical: true
    
  - name: "打开主界面功能正常"
    description: "通过托盘图标能够打开主界面"
    image: "images/open_main_interface_from_tray.png"
    critical: true
    
  - name: "快速扫描功能正常"
    description: "通过托盘图标能够执行快速扫描"
    image: "images/quick_scan_from_tray.png"
    critical: false
    
  - name: "防护控制功能正常"
    description: "通过托盘图标能够控制防护功能"
    image: "images/pause_protection_from_tray.png"
    critical: false
    
  - name: "服务持续运行"
    description: "退出界面后服务继续运行"
    image: "images/service_continues_running.png"
    critical: true
```

## 测试数据
```yaml
test_data:
  tray_icon_states:
    normal: "绿色图标"
    warning: "黄色图标"
    error: "红色图标"
    offline: "灰色图标"
    
  right_click_menu_items:
    - "打开主界面"
    - "快速扫描"
    - "暂停防护"
    - "恢复防护"
    - "查看日志"
    - "关于"
    - "退出"
    
  left_click_actions:
    - "打开主界面"
    - "显示状态气泡"
    - "切换界面显示/隐藏"
    
  status_indicators:
    protection_on: "正常防护图标"
    protection_off: "防护关闭图标"
    scanning: "扫描中图标"
    updating: "更新中图标"
    
  functionality_tests:
    - "图标显示"
    - "状态指示"
    - "菜单功能"
    - "快捷操作"
    - "服务控制"
    
  expected_behaviors:
    left_click: "打开主界面或显示状态"
    right_click: "显示上下文菜单"
    hover: "显示状态提示"
    
  service_verification:
    windows: "检查mr_mainsvc服务状态"
    linux: "检查mr_mainsvc进程状态"
```
