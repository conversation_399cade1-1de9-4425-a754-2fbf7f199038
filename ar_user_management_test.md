# AR用户管理测试

## 测试信息
```yaml
test_name: "AR控制中心用户管理测试"
test_description: "测试控制中心用户管理的增删改功能"
test_timeout: 300
expected_result: "用户管理功能正常工作，用户权限正确分配"
test_environment: "Windows/Linux控制中心"
test_priority: "P1"
test_category: "中心管理/用户管理"
```

## 前置条件
- 已登录控制中心
- 具有用户管理权限

## 测试步骤

### 步骤1：登录控制中心
**操作描述**：使用管理员账号登录AR控制中心

**目标图片**：
![登录控制中心](images/login_center_user_mgmt.png)

**操作类型**：登录
**等待时间**：5秒
**预期结果**：成功登录控制中心

---

### 步骤2：进入用户管理
**操作描述**：进入控制中心-中心管理-用户管理

**目标图片**：
![进入用户管理](images/enter_user_management.png)

**操作类型**：导航
**等待时间**：3秒
**预期结果**：成功进入用户管理页面

---

### 步骤3：点击新增用户
**操作描述**：点击新增按钮，填写对应的账号、密码、名称、邮箱、选择对应的角色

**目标图片**：
![新增用户](images/add_new_user.png)

**操作类型**：创建
**等待时间**：10秒
**预期结果**：成功创建新用户

---

### 步骤4：验证用户创建
**操作描述**：验证创建的用户出现在用户管理中

**目标图片**：
![验证用户创建](images/verify_user_creation.png)

**操作类型**：验证
**等待时间**：5秒
**预期结果**：创建的用户出现在用户管理中，邮箱、账号、角色、创建时间都是和创建时所对应的相同，且权限与创建时选择的是对应的

---

### 步骤5：删除用户
**操作描述**：点击要删除的用户

**目标图片**：
![删除用户](images/delete_user.png)

**操作类型**：删除
**等待时间**：5秒
**预期结果**：删除的用户从用户管理中消失

---

### 步骤6：检查操作日志
**操作描述**：查看中心操作日志中的删除操作记录

**目标图片**：
![检查操作日志](images/check_delete_operation_log.png)

**操作类型**：验证
**等待时间**：10秒
**预期结果**：可以在中心操作日志中，找到对应的删除操作日志

---

### 步骤7：截图保存测试结果
**操作描述**：对用户管理测试进行截图保存

**操作类型**：截图
**保存文件名**：ar_user_management_test_{timestamp}.png
**预期结果**：测试结果截图保存成功

## 测试验证点
```yaml
verification_points:
  - name: "用户管理界面正常"
    description: "用户管理界面能够正常打开"
    image: "images/enter_user_management.png"
    critical: true
  
  - name: "新增用户功能正常"
    description: "能够正常创建新用户"
    image: "images/save_new_user.png"
    critical: true
  
  - name: "用户信息显示正确"
    description: "用户信息在列表中正确显示"
    image: "images/verify_user_creation.png"
    critical: true
    
  - name: "用户编辑功能正常"
    description: "能够正常编辑用户信息"
    image: "images/edit_user_info.png"
    critical: true
    
  - name: "用户登录功能正常"
    description: "新创建的用户能够正常登录"
    image: "images/test_user_login.png"
    critical: true
    
  - name: "用户权限分配正确"
    description: "用户权限与分配的角色一致"
    image: "images/verify_user_permissions.png"
    critical: true
    
  - name: "用户删除功能正常"
    description: "能够正常删除用户"
    image: "images/delete_test_user.png"
    critical: true
    
  - name: "操作日志正确记录"
    description: "用户管理操作被正确记录"
    image: "images/check_user_operation_log.png"
    critical: true
```

## 测试数据
```yaml
test_data:
  user_roles:
    - "超级管理员"
    - "普通管理员"
    - "审计员"
    - "操作员"
    
  test_user:
    username: "testuser001"
    password: "Test@123456"
    display_name: "测试用户001"
    email: "<EMAIL>"
    role: "普通管理员"
    
  user_fields:
    required:
      - "用户名"
      - "密码"
      - "显示名称"
      - "角色"
    optional:
      - "邮箱地址"
      - "电话号码"
      - "描述"
      
  role_permissions:
    super_admin:
      - "所有功能权限"
    admin:
      - "终端管理"
      - "策略管理"
      - "日志查看"
      - "用户管理"
    auditor:
      - "日志查看"
      - "报表查看"
    operator:
      - "终端管理"
      - "扫描任务"
      
  user_operations:
    - "新增用户"
    - "编辑用户"
    - "删除用户"
    - "重置密码"
    - "启用/禁用用户"
    
  validation_rules:
    username: "3-20个字符，字母数字下划线"
    password: "8-20个字符，包含大小写字母数字特殊字符"
    email: "有效的邮箱格式"
```
