#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AR控制中心简化页面扫描器
只扫描主要页面，生成核心UI规则库
"""

import time
import json
import os
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options

class ARSimpleScanner:
    def __init__(self):
        self.driver = None
        self.wait = None
        self.scan_results = {}
        
        self.config = {
            'url': 'http://172.16.103.202:8088',
            'username': 'admin',
            'password': 'Admin.2022',
            'timeout': 10
        }
        
        # 定义要扫描的主要页面
        self.target_pages = [
            "首页",
            "终端管理",
            "终端列表", 
            "扫描任务",
            "升级任务",
            "计划任务",
            "防护策略",
            "分组策略",
            "白名单",
            "事件日志",
            "病毒日志",
            "文件实时防护日志",
            "终端运行日志",
            "中心操作日志",
            "用户管理",
            "系统配置"
        ]
        
        # 创建输出目录
        self.output_dir = "ar_ui_rules"
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
    
    def setup_driver(self):
        """设置浏览器"""
        try:
            chrome_options = Options()
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--window-size=1920,1080")
            
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.maximize_window()
            self.wait = WebDriverWait(self.driver, self.config['timeout'])
            
            print("✅ 浏览器设置成功")
            return True
        except Exception as e:
            print(f"❌ 浏览器设置失败: {e}")
            return False
    
    def login(self):
        """登录控制中心"""
        try:
            print("🔄 登录控制中心...")
            
            self.driver.get(self.config['url'])
            time.sleep(3)
            
            username_input = self.wait.until(EC.element_to_be_clickable((By.NAME, "username")))
            username_input.clear()
            username_input.send_keys(self.config['username'])
            
            password_input = self.driver.find_element(By.ID, "password")
            password_input.clear()
            password_input.send_keys(self.config['password'])
            
            login_button = self.driver.find_element(By.XPATH, "//button[@type='submit']")
            login_button.click()
            time.sleep(5)
            
            print("✅ 登录成功")
            return True
            
        except Exception as e:
            print(f"❌ 登录失败: {e}")
            return False
    
    def navigate_to_page(self, page_name):
        """导航到指定页面"""
        try:
            print(f"🔄 导航到页面: {page_name}")
            
            # 查找菜单项
            menu_selectors = [
                f"//span[contains(text(), '{page_name}')]",
                f"//a[contains(text(), '{page_name}')]",
                f"//*[text()='{page_name}']"
            ]
            
            for selector in menu_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    for elem in elements:
                        if elem.is_displayed():
                            self.driver.execute_script("arguments[0].click();", elem)
                            time.sleep(3)
                            print(f"✅ 成功导航到: {page_name}")
                            return True
                except:
                    continue
            
            print(f"⚠️ 未找到页面: {page_name}")
            return False
            
        except Exception as e:
            print(f"❌ 导航失败: {e}")
            return False
    
    def analyze_page_elements(self, page_name):
        """分析页面元素"""
        try:
            print(f"🔍 分析页面: {page_name}")
            
            page_data = {
                'page_name': page_name,
                'page_url': self.driver.current_url,
                'scan_time': datetime.now().isoformat(),
                'selectors': {}
            }
            
            # 分析按钮
            buttons = self._get_buttons()
            if buttons:
                page_data['selectors']['buttons'] = buttons
                print(f"   找到 {len(buttons)} 个按钮")
            
            # 分析输入框
            inputs = self._get_inputs()
            if inputs:
                page_data['selectors']['inputs'] = inputs
                print(f"   找到 {len(inputs)} 个输入框")
            
            # 分析表格
            tables = self._get_tables()
            if tables:
                page_data['selectors']['tables'] = tables
                print(f"   找到 {len(tables)} 个表格")
            
            # 分析复选框
            checkboxes = self._get_checkboxes()
            if checkboxes:
                page_data['selectors']['checkboxes'] = checkboxes
                print(f"   找到 {len(checkboxes)} 个复选框")
            
            return page_data
            
        except Exception as e:
            print(f"❌ 分析页面失败: {e}")
            return None
    
    def _get_buttons(self):
        """获取按钮选择器"""
        buttons = {}
        try:
            elements = self.driver.find_elements(By.XPATH, "//button")
            for elem in elements:
                if elem.is_displayed() and elem.text.strip():
                    text = elem.text.strip()
                    if text and len(text) < 20:  # 过滤掉太长的文本
                        buttons[text] = {
                            'xpath': f"//button[contains(text(), '{text}')]",
                            'class': elem.get_attribute('class')
                        }
        except:
            pass
        return buttons
    
    def _get_inputs(self):
        """获取输入框选择器"""
        inputs = {}
        try:
            elements = self.driver.find_elements(By.XPATH, "//input")
            for elem in elements:
                if elem.is_displayed():
                    name = elem.get_attribute('name')
                    placeholder = elem.get_attribute('placeholder')
                    input_type = elem.get_attribute('type')
                    
                    key = name or placeholder or f"input_{input_type}"
                    if key and key not in inputs:
                        inputs[key] = {
                            'xpath': f"//input[@name='{name}']" if name else f"//input[@placeholder='{placeholder}']" if placeholder else f"//input[@type='{input_type}']",
                            'type': input_type,
                            'name': name,
                            'placeholder': placeholder
                        }
        except:
            pass
        return inputs
    
    def _get_tables(self):
        """获取表格选择器"""
        tables = {}
        try:
            elements = self.driver.find_elements(By.XPATH, "//table[contains(@class, 'el-table')]")
            for i, elem in enumerate(elements):
                if elem.is_displayed():
                    tables[f"main_table_{i+1}"] = {
                        'xpath': f"(//table[contains(@class, 'el-table')])[{i+1}]",
                        'class': elem.get_attribute('class')
                    }
        except:
            pass
        return tables
    
    def _get_checkboxes(self):
        """获取复选框选择器"""
        checkboxes = {}
        try:
            # 终端复选框（基于我们之前的发现）
            checkboxes['terminal_checkboxes'] = {
                'xpath': "//span[contains(@class, 'el-checkbox__inner')]",
                'description': "终端列表复选框",
                'usage': "用于选择终端，需要结合行内容判断是否为终端复选框"
            }
            
            # 其他复选框
            elements = self.driver.find_elements(By.XPATH, "//input[@type='checkbox']")
            for i, elem in enumerate(elements):
                if elem.is_displayed():
                    checkboxes[f"checkbox_{i+1}"] = {
                        'xpath': f"(//input[@type='checkbox'])[{i+1}]",
                        'name': elem.get_attribute('name')
                    }
        except:
            pass
        return checkboxes
    
    def scan_target_pages(self):
        """扫描目标页面"""
        try:
            print(f"\n🚀 开始扫描 {len(self.target_pages)} 个目标页面...")
            
            for i, page_name in enumerate(self.target_pages):
                print(f"\n{'='*20} 扫描页面: {page_name} ({i+1}/{len(self.target_pages)}) {'='*20}")
                
                if self.navigate_to_page(page_name):
                    page_data = self.analyze_page_elements(page_name)
                    if page_data:
                        safe_key = page_name.replace(' ', '_').replace('/', '_')
                        self.scan_results[safe_key] = page_data
                else:
                    print(f"   ⚠️ 跳过页面: {page_name}")
            
            print(f"\n✅ 扫描完成！共扫描了 {len(self.scan_results)} 个页面")
            return True
            
        except Exception as e:
            print(f"❌ 扫描失败: {e}")
            return False
    
    def generate_rules(self):
        """生成规则库"""
        try:
            print("\n📝 生成规则库...")
            
            # 生成JSON格式规则
            json_file = os.path.join(self.output_dir, "ar_ui_selectors.json")
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(self.scan_results, f, ensure_ascii=False, indent=2)
            
            # 生成Python选择器类
            py_file = os.path.join(self.output_dir, "ar_selectors.py")
            self._generate_python_selectors(py_file)
            
            print(f"✅ 规则库生成完成:")
            print(f"   JSON规则: {json_file}")
            print(f"   Python选择器: {py_file}")
            
            return True
            
        except Exception as e:
            print(f"❌ 生成规则库失败: {e}")
            return False
    
    def _generate_python_selectors(self, py_file):
        """生成Python选择器类"""
        try:
            with open(py_file, 'w', encoding='utf-8') as f:
                f.write('#!/usr/bin/env python3\n')
                f.write('# -*- coding: utf-8 -*-\n')
                f.write('"""\n')
                f.write('AR控制中心UI选择器规则库\n')
                f.write(f'自动生成时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}\n')
                f.write('"""\n\n')
                
                f.write('class ARSelectors:\n')
                f.write('    """AR控制中心UI选择器"""\n\n')
                
                # 生成常用选择器
                f.write('    # 通用选择器\n')
                f.write('    TERMINAL_CHECKBOXES = "//span[contains(@class, \'el-checkbox__inner\')]"\n')
                f.write('    MAIN_TABLE = "//table[contains(@class, \'el-table\')]"\n')
                f.write('    DIALOG = "//div[contains(@class, \'el-dialog\')]"\n')
                f.write('    CONFIRM_BUTTON = "//button[contains(text(), \'确 定\')]"\n')
                f.write('    CANCEL_BUTTON = "//button[contains(text(), \'取 消\')]"\n\n')
                
                # 为每个页面生成选择器
                for page_key, page_data in self.scan_results.items():
                    class_name = page_key.upper()
                    f.write(f'    class {class_name}:\n')
                    f.write(f'        """页面: {page_data["page_name"]}"""\n')
                    
                    # 按钮选择器
                    if 'buttons' in page_data['selectors']:
                        f.write('        # 按钮\n')
                        for btn_text, btn_info in page_data['selectors']['buttons'].items():
                            var_name = self._to_var_name(btn_text)
                            f.write(f'        {var_name} = "{btn_info["xpath"]}"\n')
                        f.write('\n')
                    
                    # 输入框选择器
                    if 'inputs' in page_data['selectors']:
                        f.write('        # 输入框\n')
                        for inp_name, inp_info in page_data['selectors']['inputs'].items():
                            var_name = self._to_var_name(inp_name)
                            f.write(f'        {var_name} = "{inp_info["xpath"]}"\n')
                        f.write('\n')
                    
                    f.write('\n')
                
        except Exception as e:
            print(f"生成Python选择器失败: {e}")
    
    def _to_var_name(self, text):
        """将文本转换为变量名"""
        import re
        clean_text = re.sub(r'[^\w\s]', '', text)
        return re.sub(r'\s+', '_', clean_text.strip()).upper()
    
    def run_scan(self):
        """运行扫描"""
        print("🔍 AR控制中心简化页面扫描器")
        print("=" * 50)
        
        if not self.setup_driver():
            return False
        
        try:
            if not self.login():
                return False
            
            if not self.scan_target_pages():
                return False
            
            if not self.generate_rules():
                return False
            
            print("\n🎉 扫描完成！")
            return True
            
        except Exception as e:
            print(f"❌ 扫描失败: {e}")
            return False
        finally:
            if self.driver:
                self.driver.quit()

def main():
    scanner = ARSimpleScanner()
    success = scanner.run_scan()
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())
