# AR Linux客户端安装测试

## 测试信息
```yaml
test_name: "AR Linux客户端手动安装测试"
test_description: "在Linux系统上手动安装AR客户端"
test_timeout: 300
expected_result: "AR客户端安装成功并在控制中心显示在线"
test_environment: "Linux生产机"
test_priority: "P0"
test_category: "客户端/安装卸载"
```

## 前置条件
- 已上传客户端安装包于Linux系统生产机上(例如安装包为:mredrclient_2.1.0.130_amd64.rpm或mredrclient_2.1.0.130_amd64.deb)

## 测试步骤

### 步骤1：SSH连接生产机
**操作描述**：通过ssh生产机IP地址连接到生产机（也可直接在生产机上调起Terminal命令执行窗口）

**目标图片**：
![SSH连接生产机](images/ssh_connect_production.png)

**操作类型**：连接
**等待时间**：5秒
**预期结果**：ssh连接生产机成功

---

### 步骤2：进入安装包目录
**操作描述**：cd到存有客户端安装包的路径

**目标图片**：
![进入安装包目录](images/cd_package_directory.png)

**操作类型**：命令执行
**等待时间**：3秒
**预期结果**：成功进入安装包所在目录

---

### 步骤3：执行安装命令（RedHat系）
**操作描述**：基于RedHat的操作系统执行命令:rpm -ivh 客户端安装包名 && bash /opt/apps/mredrclient/system_info init_app 一体机控制中心IP地址

**目标图片**：
![RedHat系统安装命令](images/redhat_install_command.png)

**操作类型**：命令执行
**等待时间**：60秒
**预期结果**：提示安装成功，该主机出现在AR控制中心终端列表中

---

### 步骤4：执行安装命令（Debian系）
**操作描述**：基于Debian的操作系统执行命令: sudo dpkg -i mredrclient_2.1.0.130_amd64.deb && sudo bash /opt/apps/mredrclient/system_info init_app ***********

**目标图片**：
![Debian系统安装命令](images/debian_install_command.png)

**操作类型**：命令执行
**等待时间**：60秒
**预期结果**：提示安装成功，该主机出现在AR控制中心终端列表中

---

### 步骤5：验证安装目录
**操作描述**：检查Linux系统中是否存在/opt/apps/mredrclient文件夹

**目标图片**：
![验证安装目录](images/verify_linux_install_directory.png)

**操作类型**：验证
**等待时间**：5秒
**预期结果**：Linux存在/opt/apps/mredrclient文件夹

---

### 步骤6：检查系统服务
**操作描述**：检查Linux系统中mr_mainsvc、mr_enginesvc的服务状态

**目标图片**：
![检查Linux服务状态](images/check_linux_services.png)

**操作类型**：验证
**等待时间**：5秒
**预期结果**：Linux有mr_mainsvc、mr_enginesvc的服务并且处于运行状态

---

### 步骤7：检查AR相关进程
**操作描述**：使用ps命令检查是否存在AR相关的进程

**目标图片**：
![检查AR进程](images/check_ar_processes_linux.png)

**操作类型**：验证
**等待时间**：5秒
**预期结果**：存在AR相关的进程

---

### 步骤8：验证控制中心终端列表
**操作描述**：登录控制中心，检查终端列表中是否显示新安装的Linux客户端

**目标图片**：
![控制中心终端列表Linux](images/center_terminal_list_linux.png)

**操作类型**：验证
**等待时间**：10秒
**预期结果**：该主机出现在AR控制中心终端列表中

---

### 步骤9：截图保存测试结果
**操作描述**：对安装完成后的状态进行截图保存

**操作类型**：截图
**保存文件名**：ar_linux_client_install_test_{timestamp}.png
**预期结果**：测试结果截图保存成功

## 测试验证点
```yaml
verification_points:
  - name: "SSH连接成功"
    description: "能够成功连接到Linux生产机"
    critical: true
  
  - name: "安装命令执行成功"
    description: "rpm或dpkg安装命令正常执行完成"
    critical: true
  
  - name: "安装目录创建成功"
    description: "客户端安装目录正确创建"
    image: "images/verify_linux_install_directory.png"
    critical: true
    
  - name: "系统服务正常运行"
    description: "mr_mainsvc、mr_enginesvc服务处于运行状态"
    image: "images/check_linux_services.png"
    critical: true
    
  - name: "AR进程正常启动"
    description: "AR相关进程正常运行"
    image: "images/check_ar_processes_linux.png"
    critical: true
    
  - name: "控制中心显示新终端"
    description: "新安装的客户端在控制中心终端列表中显示"
    image: "images/center_terminal_list_linux.png"
    critical: true
```

## 测试数据
```yaml
test_data:
  client_packages:
    redhat: "mredrclient_2.1.0.130_amd64.rpm"
    debian: "mredrclient_2.1.0.130_amd64.deb"
    install_path: "/opt/apps/mredrclient"
    
  ar_center:
    ip: "***********"  # 示例IP，实际使用时需要替换
    
  system_services:
    - "mr_mainsvc"
    - "mr_enginesvc"
    expected_status: "运行中"
    
  install_commands:
    redhat: "rpm -ivh mredrclient_2.1.0.130_amd64.rpm && bash /opt/apps/mredrclient/system_info init_app ***********"
    debian: "sudo dpkg -i mredrclient_2.1.0.130_amd64.deb && sudo bash /opt/apps/mredrclient/system_info init_app ***********"
```
