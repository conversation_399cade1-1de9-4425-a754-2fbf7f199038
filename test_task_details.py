#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试扫描任务页面的详情按钮点击
"""

import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options

class TaskDetailsTest:
    def __init__(self):
        self.driver = None
        self.wait = None
        
        self.config = {
            'url': 'http://172.16.103.202:8088',
            'username': 'admin',
            'password': 'Admin.2022',
            'timeout': 10
        }
    
    def setup_driver(self):
        """设置浏览器"""
        try:
            chrome_options = Options()
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--window-size=1920,1080")
            
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.maximize_window()
            self.wait = WebDriverWait(self.driver, self.config['timeout'])
            
            print("✅ 浏览器设置成功")
            return True
        except Exception as e:
            print(f"❌ 浏览器设置失败: {e}")
            return False
    
    def login_and_navigate_to_scan_tasks(self):
        """登录并导航到扫描任务页面"""
        try:
            print("🔄 登录控制中心...")
            
            self.driver.get(self.config['url'])
            time.sleep(3)
            
            # 登录
            username_input = self.wait.until(EC.element_to_be_clickable((By.NAME, "username")))
            username_input.clear()
            username_input.send_keys(self.config['username'])
            
            password_input = self.driver.find_element(By.ID, "password")
            password_input.clear()
            password_input.send_keys(self.config['password'])
            
            login_button = self.driver.find_element(By.XPATH, "//button[@type='submit']")
            login_button.click()
            time.sleep(5)
            
            # 导航到扫描任务
            print("🔄 导航到扫描任务页面...")
            
            # 查找扫描任务菜单
            scan_task_selectors = [
                "//span[contains(text(), '扫描任务')]",
                "//a[contains(text(), '扫描任务')]",
                "//*[contains(text(), '扫描任务')]"
            ]
            
            scan_task_menu = None
            for selector in scan_task_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    for elem in elements:
                        if elem.is_displayed():
                            scan_task_menu = elem
                            print(f"✅ 找到扫描任务菜单")
                            break
                    if scan_task_menu:
                        break
                except:
                    continue
            
            if not scan_task_menu:
                print("❌ 未找到扫描任务菜单")
                return False
            
            # 点击扫描任务菜单
            self.driver.execute_script("arguments[0].click();", scan_task_menu)
            time.sleep(3)
            
            print("✅ 成功导航到扫描任务页面")
            return True
            
        except Exception as e:
            print(f"❌ 登录或导航失败: {e}")
            return False
    
    def analyze_task_table(self):
        """分析扫描任务表格"""
        try:
            print("\n🔍 分析扫描任务表格...")
            
            # 等待页面加载
            time.sleep(3)
            
            # 查找表格
            table_selectors = [
                "//table[contains(@class, 'el-table')]",
                "//div[contains(@class, 'el-table')]",
                "//tbody",
                "//table"
            ]
            
            table = None
            for selector in table_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    for elem in elements:
                        if elem.is_displayed():
                            table = elem
                            print(f"✅ 找到表格")
                            break
                    if table:
                        break
                except:
                    continue
            
            if not table:
                print("❌ 未找到表格")
                return False
            
            # 查找表格行
            print("\n📋 分析表格行...")
            rows = self.driver.find_elements(By.XPATH, "//tbody//tr")
            print(f"找到 {len(rows)} 行数据")
            
            for i, row in enumerate(rows):
                if row.is_displayed():
                    row_text = row.text
                    print(f"   行 {i+1}: {row_text[:100]}...")
                    
                    # 查找该行中的按钮
                    buttons = row.find_elements(By.XPATH, ".//button")
                    for j, btn in enumerate(buttons):
                        if btn.is_displayed():
                            print(f"      按钮 {j+1}: 文本='{btn.text}', 类={btn.get_attribute('class')}")
            
            return True
            
        except Exception as e:
            print(f"❌ 分析表格失败: {e}")
            return False
    
    def test_details_buttons(self):
        """测试详情按钮"""
        try:
            print("\n🔍 查找详情按钮...")
            
            # 多种详情按钮选择器
            details_selectors = [
                "//button[contains(text(), '详情')]",
                "//button[contains(text(), '查看')]",
                "//span[contains(text(), '详情')]//parent::button",
                "//a[contains(text(), '详情')]",
                "//*[contains(text(), '详情')]",
                "//button[contains(@class, 'el-button') and contains(., '详情')]",
                "//tbody//tr//button[contains(text(), '详情')]",
                "//table//button[contains(text(), '详情')]"
            ]
            
            all_details_buttons = []
            
            for i, selector in enumerate(details_selectors):
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    print(f"   选择器 {i+1}: 找到 {len(elements)} 个元素")
                    
                    for j, elem in enumerate(elements):
                        if elem.is_displayed() and elem.is_enabled():
                            # 获取按钮所在行的信息
                            try:
                                parent_row = elem.find_element(By.XPATH, "./ancestor::tr[1]")
                                row_text = parent_row.text
                            except:
                                row_text = "无法获取行信息"
                            
                            all_details_buttons.append({
                                'selector_index': i,
                                'element_index': j,
                                'element': elem,
                                'text': elem.text,
                                'class': elem.get_attribute('class'),
                                'row_text': row_text[:100]
                            })
                            
                            print(f"      详情按钮 {len(all_details_buttons)}: 文本='{elem.text}', 类={elem.get_attribute('class')}")
                            print(f"         所在行: {row_text[:50]}...")
                            
                except Exception as e:
                    print(f"   选择器 {i+1} 失败: {e}")
            
            print(f"\n📊 总共找到 {len(all_details_buttons)} 个详情按钮")
            
            if not all_details_buttons:
                print("❌ 未找到任何详情按钮")
                return False
            
            # 尝试点击第一个详情按钮
            print("\n🖱️ 尝试点击第一个详情按钮...")
            first_button = all_details_buttons[0]
            
            try:
                print(f"   点击按钮: {first_button['text']}")
                print(f"   所在行: {first_button['row_text']}")
                
                # 滚动到按钮可见
                self.driver.execute_script("arguments[0].scrollIntoView(true);", first_button['element'])
                time.sleep(1)
                
                # 点击按钮
                self.driver.execute_script("arguments[0].click();", first_button['element'])
                time.sleep(3)
                
                print("   ✅ 按钮点击成功")
                
                # 检查是否出现对话框
                print("\n🔍 检查是否出现详情对话框...")
                dialog_selectors = [
                    "//div[contains(@class, 'el-dialog')]",
                    "//div[contains(@class, 'dialog')]",
                    "//*[contains(@class, 'modal')]",
                    "//div[contains(@class, 'el-dialog__wrapper')]"
                ]
                
                dialog_found = False
                for selector in dialog_selectors:
                    try:
                        elements = self.driver.find_elements(By.XPATH, selector)
                        for elem in elements:
                            if elem.is_displayed():
                                dialog_text = elem.text
                                print(f"   ✅ 找到对话框: {dialog_text[:100]}...")
                                dialog_found = True
                                break
                        if dialog_found:
                            break
                    except:
                        continue
                
                if not dialog_found:
                    print("   ⚠️ 未找到对话框，可能是页面跳转或其他形式的详情显示")
                    
                    # 检查页面是否有变化
                    current_url = self.driver.current_url
                    print(f"   当前URL: {current_url}")
                    
                    # 检查页面标题
                    page_title = self.driver.title
                    print(f"   页面标题: {page_title}")
                
                return True
                
            except Exception as e:
                print(f"   ❌ 点击按钮失败: {e}")
                return False
            
        except Exception as e:
            print(f"❌ 测试详情按钮失败: {e}")
            return False
    
    def run_test(self):
        """运行测试"""
        print("🧪 扫描任务详情按钮测试")
        print("=" * 50)
        
        if not self.setup_driver():
            return False
        
        try:
            if not self.login_and_navigate_to_scan_tasks():
                return False
            
            if not self.analyze_task_table():
                return False
            
            if not self.test_details_buttons():
                return False
            
            print("\n🎉 测试完成！")
            
            # 保持浏览器打开观察结果
            print("浏览器将在30秒后关闭，请观察详情显示...")
            time.sleep(30)
            
            return True
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            return False
        finally:
            if self.driver:
                self.driver.quit()

def main():
    test = TaskDetailsTest()
    success = test.run_test()
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())
