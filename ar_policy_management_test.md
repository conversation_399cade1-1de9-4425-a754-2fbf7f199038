# AR防护策略编辑管理测试

## 测试信息
```yaml
test_name: "AR控制中心防护策略编辑管理测试"
test_description: "测试控制中心防护策略的增删改功能"
test_timeout: 300
expected_result: "防护策略的增删改功能正常工作，操作被正确记录"
test_environment: "Windows/Linux控制中心"
test_priority: "P0"
test_category: "防护策略/策略编辑"
```

## 前置条件
- 已登录控制中心
- 具有策略管理权限

## 测试步骤

### 步骤1：登录控制中心
**操作描述**：使用浏览器登录AR控制中心

**目标图片**：
![登录控制中心](images/login_center_policy_edit.png)

**操作类型**：登录
**等待时间**：5秒
**预期结果**：成功登录控制中心

---

### 步骤2：进入策略编辑
**操作描述**：进入控制中心-防护策略-策略编辑

**目标图片**：
![进入策略编辑](images/enter_policy_editing.png)

**操作类型**：导航
**等待时间**：3秒
**预期结果**：成功进入策略编辑页面

---

### 步骤3：查看现有策略
**操作描述**：查看当前系统中已有的防护策略

**目标图片**：
![查看现有策略](images/view_existing_policies.png)

**操作类型**：查看
**等待时间**：5秒
**预期结果**：显示所有现有的防护策略

---

### 步骤4：新增防护策略
**操作描述**：点击新增按钮，创建新的防护策略

**目标图片**：
![新增防护策略](images/create_new_policy.png)

**操作类型**：创建
**等待时间**：3秒
**预期结果**：打开新策略创建界面

---

### 步骤5：配置策略基本信息
**操作描述**：设置策略名称、描述等基本信息

**目标图片**：
![配置策略基本信息](images/configure_policy_basic_info.png)

**操作类型**：配置
**等待时间**：5秒
**预期结果**：成功配置策略基本信息

---

### 步骤6：配置文件实时防护
**操作描述**：配置文件实时防护相关设置

**目标图片**：
![配置文件实时防护](images/configure_realtime_protection.png)

**操作类型**：配置
**等待时间**：5秒
**预期结果**：成功配置文件实时防护设置

---

### 步骤7：配置恶意行为监控
**操作描述**：配置恶意行为监控相关设置

**目标图片**：
![配置恶意行为监控](images/configure_behavior_monitoring.png)

**操作类型**：配置
**等待时间**：5秒
**预期结果**：成功配置恶意行为监控设置

---

### 步骤8：配置勒索诱捕设置
**操作描述**：配置勒索诱捕功能相关设置

**目标图片**：
![配置勒索诱捕](images/configure_ransomware_honeypot.png)

**操作类型**：配置
**等待时间**：5秒
**预期结果**：成功配置勒索诱捕设置

---

### 步骤9：保存新策略
**操作描述**：保存新创建的防护策略

**目标图片**：
![保存新策略](images/save_new_policy.png)

**操作类型**：保存
**等待时间**：5秒
**预期结果**：新策略保存成功

---

### 步骤10：修改已有策略
**操作描述**：选择已有策略进行修改

**目标图片**：
![修改已有策略](images/modify_existing_policy.png)

**操作类型**：编辑
**等待时间**：5秒
**预期结果**：能够修改已有策略的设置

---

### 步骤11：保存策略修改
**操作描述**：保存对已有策略的修改

**目标图片**：
![保存策略修改](images/save_policy_modification.png)

**操作类型**：保存
**等待时间**：5秒
**预期结果**：策略修改保存成功

---

### 步骤12：删除测试策略
**操作描述**：删除不需要的测试策略

**目标图片**：
![删除测试策略](images/delete_test_policy.png)

**操作类型**：删除
**等待时间**：5秒
**预期结果**：测试策略删除成功

---

### 步骤13：验证策略列表更新
**操作描述**：验证策略列表是否正确反映所有操作

**目标图片**：
![验证策略列表](images/verify_policy_list_update.png)

**操作类型**：验证
**等待时间**：5秒
**预期结果**：操作成功，策略列表正确更新

---

### 步骤14：检查中心操作日志
**操作描述**：查看中心操作日志，确认所有策略操作被记录

**目标图片**：
![检查操作日志](images/check_policy_operation_log.png)

**操作类型**：查看
**等待时间**：10秒
**预期结果**：中心操作日志，记录本次操作

---

### 步骤15：截图保存测试结果
**操作描述**：对策略管理测试进行截图保存

**操作类型**：截图
**保存文件名**：ar_policy_management_test_{timestamp}.png
**预期结果**：测试结果截图保存成功

## 测试验证点
```yaml
verification_points:
  - name: "策略编辑界面正常"
    description: "策略编辑管理界面能够正常打开"
    image: "images/enter_policy_editing.png"
    critical: true
  
  - name: "新增策略功能正常"
    description: "能够正常创建新的防护策略"
    image: "images/create_new_policy.png"
    critical: true
  
  - name: "策略配置功能完整"
    description: "能够完整配置策略的各项设置"
    image: "images/configure_policy_basic_info.png"
    critical: true
    
  - name: "策略保存功能正常"
    description: "新建和修改的策略能够正常保存"
    image: "images/save_new_policy.png"
    critical: true
    
  - name: "策略修改功能正常"
    description: "能够正常修改已有策略"
    image: "images/modify_existing_policy.png"
    critical: true
    
  - name: "策略删除功能正常"
    description: "能够正常删除不需要的策略"
    image: "images/delete_test_policy.png"
    critical: true
    
  - name: "操作日志正确记录"
    description: "所有策略操作被正确记录到日志"
    image: "images/check_policy_operation_log.png"
    critical: true
```

## 测试数据
```yaml
test_data:
  policy_types:
    - "默认策略"
    - "严格策略"
    - "宽松策略"
    - "自定义策略"
    
  policy_components:
    realtime_protection:
      - "开启/关闭"
      - "处置方式"
      - "扫描类型"
      - "排除路径"
    
    behavior_monitoring:
      - "开启/关闭"
      - "监控级别"
      - "处置方式"
      - "白名单设置"
    
    ransomware_honeypot:
      - "开启/关闭"
      - "诱捕路径"
      - "处置方式"
      - "告警设置"
    
    scan_policy:
      - "定时扫描"
      - "扫描范围"
      - "扫描深度"
      - "处理方式"
      
  test_policy:
    name: "测试策略001"
    description: "用于功能测试的策略"
    settings:
      realtime_protection: "开启"
      behavior_monitoring: "开启"
      ransomware_honeypot: "关闭"
      
  policy_operations:
    - "新增策略"
    - "修改策略"
    - "删除策略"
    - "复制策略"
    - "导入策略"
    - "导出策略"
    
  validation_items:
    - "策略名称唯一性"
    - "必填字段验证"
    - "配置项有效性"
    - "策略依赖关系"
```
