# AR客户端状态显示测试

## 测试信息
```yaml
test_name: "AR客户端状态显示测试"
test_description: "检查AR客户端状态显示的准确性和完整性"
test_timeout: 180
expected_result: "客户端状态信息正确完整显示"
test_environment: "Windows/Linux客户端"
test_priority: "P1"
test_category: "客户端功能测试"
```

## 前置条件
- 已安装AR客户端

## 测试步骤

### 步骤1：打开AR客户端
**操作描述**：启动AR客户端应用程序

**目标图片**：
![打开AR客户端](images/open_ar_client_status.png)

**操作类型**：启动应用
**等待时间**：5秒
**预期结果**：成功打开AR客户端

---

### 步骤2：查看客户端主界面状态
**操作描述**：查看客户端主界面的状态显示

**目标图片**：
![客户端主界面状态](images/client_main_interface_status.png)

**操作类型**：查看
**等待时间**：5秒
**预期结果**：主界面状态信息显示正确

---

### 步骤3：查看防护状态显示
**操作描述**：查看各项防护功能的状态显示

**目标图片**：
![防护状态显示](images/protection_status_display.png)

**操作类型**：查看
**等待时间**：5秒
**预期结果**：防护状态显示准确

---

### 步骤4：查看连接状态显示
**操作描述**：查看与控制中心的连接状态显示

**目标图片**：
![连接状态显示](images/connection_status_display.png)

**操作类型**：查看
**等待时间**：5秒
**预期结果**：连接状态显示正确

---

### 步骤5：查看版本信息显示
**操作描述**：查看客户端版本和病毒库版本信息

**目标图片**：
![版本信息显示](images/version_info_display.png)

**操作类型**：查看
**等待时间**：3秒
**预期结果**：版本信息显示准确

---

### 步骤6：查看最后扫描时间
**操作描述**：查看最后一次扫描的时间显示

**目标图片**：
![最后扫描时间](images/last_scan_time_display.png)

**操作类型**：查看
**等待时间**：3秒
**预期结果**：最后扫描时间显示正确

---

### 步骤7：查看威胁统计信息
**操作描述**：查看发现威胁的统计信息

**目标图片**：
![威胁统计信息](images/threat_statistics_display.png)

**操作类型**：查看
**等待时间**：3秒
**预期结果**：威胁统计信息显示准确

---

### 步骤8：查看系统资源占用
**操作描述**：查看客户端系统资源占用情况

**目标图片**：
![系统资源占用](images/system_resource_usage.png)

**操作类型**：查看
**等待时间**：5秒
**预期结果**：系统资源占用信息显示正确

---

### 步骤9：验证状态实时更新
**操作描述**：验证状态信息是否实时更新

**目标图片**：
![状态实时更新](images/status_realtime_update.png)

**操作类型**：验证
**等待时间**：30秒
**预期结果**：状态信息能够实时更新

---

### 步骤10：对比控制中心显示
**操作描述**：对比客户端状态与控制中心显示的一致性

**目标图片**：
![状态一致性对比](images/status_consistency_comparison.png)

**操作类型**：对比验证
**等待时间**：30秒
**预期结果**：客户端状态与控制中心显示一致

---

### 步骤11：测试状态刷新功能
**操作描述**：测试手动刷新状态的功能（如果有）

**目标图片**：
![状态刷新功能](images/status_refresh_function.png)

**操作类型**：功能测试
**等待时间**：10秒
**预期结果**：状态刷新功能正常工作

---

### 步骤12：截图保存测试结果
**操作描述**：对客户端状态显示测试进行截图保存

**操作类型**：截图
**保存文件名**：ar_client_status_display_test_{timestamp}.png
**预期结果**：测试结果截图保存成功

## 测试验证点
```yaml
verification_points:
  - name: "客户端主界面正常"
    description: "客户端主界面能够正常显示"
    image: "images/client_main_interface_status.png"
    critical: true
  
  - name: "防护状态显示准确"
    description: "各项防护功能状态显示准确"
    image: "images/protection_status_display.png"
    critical: true
  
  - name: "连接状态显示正确"
    description: "与控制中心连接状态显示正确"
    image: "images/connection_status_display.png"
    critical: true
    
  - name: "版本信息显示准确"
    description: "客户端和病毒库版本信息显示准确"
    image: "images/version_info_display.png"
    critical: true
    
  - name: "扫描时间显示正确"
    description: "最后扫描时间显示正确"
    image: "images/last_scan_time_display.png"
    critical: true
    
  - name: "威胁统计准确"
    description: "威胁统计信息显示准确"
    image: "images/threat_statistics_display.png"
    critical: true
    
  - name: "资源占用显示正确"
    description: "系统资源占用信息显示正确"
    image: "images/system_resource_usage.png"
    critical: false
    
  - name: "状态实时更新"
    description: "状态信息能够实时更新"
    image: "images/status_realtime_update.png"
    critical: true
    
  - name: "与控制中心一致"
    description: "客户端状态与控制中心显示一致"
    image: "images/status_consistency_comparison.png"
    critical: true
```

## 测试数据
```yaml
test_data:
  status_categories:
    - "防护状态"
    - "连接状态"
    - "版本信息"
    - "扫描状态"
    - "威胁统计"
    - "系统资源"
    
  protection_status:
    realtime_protection: "开启/关闭"
    behavior_monitoring: "开启/关闭"
    ransomware_honeypot: "开启/关闭"
    
  connection_status:
    - "已连接"
    - "连接中"
    - "连接失败"
    - "离线"
    
  version_info:
    client_version: "v2.x.x.xxx"
    virus_db_version: "YYYYMMDD.HH"
    last_update_time: "YYYY-MM-DD HH:MM:SS"
    
  scan_status:
    last_scan_time: "YYYY-MM-DD HH:MM:SS"
    scan_type: "快速扫描/全盘扫描/自定义扫描"
    scan_result: "未发现威胁/发现X个威胁"
    
  threat_statistics:
    - "总检测威胁数"
    - "已处理威胁数"
    - "隔离文件数"
    - "最近威胁时间"
    
  resource_usage:
    - "CPU占用率"
    - "内存占用"
    - "磁盘占用"
    - "网络流量"
    
  status_indicators:
    normal: "绿色/正常图标"
    warning: "黄色/警告图标"
    error: "红色/错误图标"
    
  update_frequency: "实时更新/定时更新"
```
