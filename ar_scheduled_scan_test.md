# AR计划任务定期扫描测试

## 测试信息
```yaml
test_name: "AR控制中心计划任务定期扫描测试"
test_description: "测试控制中心计划任务的创建和执行功能"
test_timeout: 3600
expected_result: "计划任务能够按时自动执行病毒扫描"
test_environment: "Linux控制中心"
test_priority: "P1"
test_category: "控制中心/计划任务"
```

## 前置条件
- 控制中心已安装
- 存在在线的客户端

## 测试步骤

### 步骤1：登录控制中心
**操作描述**：使用管理员账号登录AR控制中心

**目标图片**：
![登录控制中心](images/login_center_scheduled_scan.png)

**操作类型**：登录
**等待时间**：5秒
**预期结果**：成功登录控制中心

---

### 步骤2：进入计划任务
**操作描述**：进入控制中心-终端管理-计划任务

**目标图片**：
![进入计划任务](images/enter_scheduled_tasks.png)

**操作类型**：导航
**等待时间**：3秒
**预期结果**：成功进入计划任务管理页面

---

### 步骤3：创建单次执行计划任务
**操作描述**：终端管理-计划任务中，创建单次执行的计划任务

**目标图片**：
![创建单次任务](images/create_single_execution_task.png)

**操作类型**：创建任务
**等待时间**：10秒
**预期结果**：成功创建单次执行的计划任务

---

### 步骤4：设置单次任务参数
**操作描述**：设置单次任务的执行时间、目标终端、扫描类型等参数

**目标图片**：
![设置单次任务参数](images/set_single_task_parameters.png)

**操作类型**：参数配置
**等待时间**：10秒
**预期结果**：成功配置单次任务参数

---

### 步骤5：等待单次任务执行
**操作描述**：等待到达设定时间，观察任务是否自动执行

**目标图片**：
![单次任务执行](images/single_task_execution.png)

**操作类型**：等待观察
**等待时间**：300秒
**预期结果**：到达时间后，自动进行病毒扫描，且在"扫描任务"中有记录

---

### 步骤6：创建每天定期执行任务
**操作描述**：终端管理-计划任务中，创建每天定期执行的计划任务

**目标图片**：
![创建每天定期任务](images/create_daily_recurring_task.png)

**操作类型**：创建任务
**等待时间**：10秒
**预期结果**：成功创建每天定期执行的计划任务

---

### 步骤7：设置每天任务参数
**操作描述**：设置每天任务的执行时间、重复规则等参数

**目标图片**：
![设置每天任务参数](images/set_daily_task_parameters.png)

**操作类型**：参数配置
**等待时间**：10秒
**预期结果**：成功配置每天任务参数

---

### 步骤8：验证每天任务执行
**操作描述**：验证每天定期任务是否按时执行

**目标图片**：
![每天任务执行验证](images/daily_task_execution_verification.png)

**操作类型**：执行验证
**等待时间**：300秒
**预期结果**：到达时间后，自动进行病毒扫描，且在"扫描任务"中有记录

---

### 步骤9：创建每周定期执行任务
**操作描述**：终端管理-计划任务中，创建每周定期执行的计划任务

**目标图片**：
![创建每周定期任务](images/create_weekly_recurring_task.png)

**操作类型**：创建任务
**等待时间**：10秒
**预期结果**：成功创建每周定期执行的计划任务

---

### 步骤10：设置每周任务参数
**操作描述**：设置每周任务的执行时间、星期几执行等参数

**目标图片**：
![设置每周任务参数](images/set_weekly_task_parameters.png)

**操作类型**：参数配置
**等待时间**：10秒
**预期结果**：成功配置每周任务参数

---

### 步骤11：验证扫描任务记录
**操作描述**：在扫描任务中查看自动执行的扫描记录

**目标图片**：
![扫描任务记录](images/scan_task_records.png)

**操作类型**：记录查看
**等待时间**：10秒
**预期结果**：扫描任务中正确记录了计划任务执行的扫描

---

### 步骤12：管理计划任务
**操作描述**：测试计划任务的编辑、删除、启用/禁用功能

**目标图片**：
![管理计划任务](images/manage_scheduled_tasks.png)

**操作类型**：任务管理
**等待时间**：15秒
**预期结果**：计划任务管理功能正常工作

---

### 步骤13：截图保存测试结果
**操作描述**：对计划任务测试进行截图保存

**操作类型**：截图
**保存文件名**：ar_scheduled_scan_test_{timestamp}.png
**预期结果**：测试结果截图保存成功

## 测试验证点
```yaml
verification_points:
  - name: "计划任务界面正常"
    description: "计划任务管理界面能够正常打开"
    image: "images/enter_scheduled_tasks.png"
    critical: true
  
  - name: "单次任务创建成功"
    description: "能够成功创建单次执行的计划任务"
    image: "images/create_single_execution_task.png"
    critical: true
  
  - name: "单次任务正常执行"
    description: "单次计划任务能够按时自动执行"
    image: "images/single_task_execution.png"
    critical: true
    
  - name: "每天任务创建成功"
    description: "能够成功创建每天定期执行的计划任务"
    image: "images/create_daily_recurring_task.png"
    critical: true
    
  - name: "每天任务正常执行"
    description: "每天定期任务能够按时自动执行"
    image: "images/daily_task_execution_verification.png"
    critical: true
    
  - name: "每周任务创建成功"
    description: "能够成功创建每周定期执行的计划任务"
    image: "images/create_weekly_recurring_task.png"
    critical: true
    
  - name: "扫描记录正确"
    description: "计划任务执行的扫描被正确记录"
    image: "images/scan_task_records.png"
    critical: true
    
  - name: "任务管理功能正常"
    description: "计划任务的管理功能正常工作"
    image: "images/manage_scheduled_tasks.png"
    critical: true
```

## 测试数据
```yaml
test_data:
  task_types:
    - "单次执行"
    - "每天定期执行"
    - "每周定期执行"
    - "每月定期执行"
    
  task_parameters:
    execution_time: "具体时间点"
    target_terminals: "选定的终端或分组"
    scan_type: "快速扫描/全盘扫描/自定义扫描"
    scan_scope: "扫描路径"
    
  recurring_options:
    daily:
      - "每天"
      - "工作日"
      - "周末"
    weekly:
      - "每周一"
      - "每周五"
      - "自定义星期"
    monthly:
      - "每月1号"
      - "每月最后一天"
      - "自定义日期"
      
  task_management:
    - "创建任务"
    - "编辑任务"
    - "删除任务"
    - "启用/禁用任务"
    - "查看任务历史"
    
  verification_items:
    - "任务按时执行"
    - "扫描正常进行"
    - "结果正确记录"
    - "任务状态更新"
    
  test_schedule:
    single_task: "5分钟后执行"
    daily_task: "每天同一时间"
    weekly_task: "每周同一天"
```
