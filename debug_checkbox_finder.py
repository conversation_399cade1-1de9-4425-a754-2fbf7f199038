#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试复选框查找程序
用于分析页面结构并找到正确的复选框
"""

import time
import os
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options

class CheckboxDebugger:
    def __init__(self):
        """初始化调试器"""
        self.driver = None
        self.wait = None
        
        # 测试配置
        self.config = {
            'url': 'http://172.16.103.202:8088',
            'username': 'admin',
            'password': 'Admin.2022',
            'timeout': 5
        }
    
    def setup_driver(self):
        """设置Chrome浏览器驱动"""
        try:
            chrome_options = Options()
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--window-size=1920,1080")

            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.maximize_window()
            self.wait = WebDriverWait(self.driver, self.config['timeout'])

            print("✅ 浏览器驱动设置成功")
            return True

        except Exception as e:
            print(f"❌ 浏览器驱动设置失败: {e}")
            return False
    
    def login(self):
        """登录控制中心"""
        try:
            print("\n🔄 登录控制中心...")
            
            # 打开登录页面
            self.driver.get(self.config['url'])
            time.sleep(3)

            # 输入用户名
            username_input = self.wait.until(EC.element_to_be_clickable((By.NAME, "username")))
            username_input.clear()
            username_input.send_keys(self.config['username'])

            # 输入密码
            password_input = self.driver.find_element(By.ID, "password")
            password_input.clear()
            password_input.send_keys(self.config['password'])

            # 点击登录
            login_button = self.driver.find_element(By.XPATH, "//button[@type='submit']")
            login_button.click()
            time.sleep(5)
            
            print("✅ 登录成功")
            return True
            
        except Exception as e:
            print(f"❌ 登录失败: {e}")
            return False
    
    def navigate_to_terminal_list(self):
        """导航到终端列表"""
        try:
            print("\n🔄 导航到终端列表...")
            
            # 点击终端管理
            terminal_menu = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, "//span[contains(text(), '终端管理')]"))
            )
            terminal_menu.click()
            time.sleep(2)

            # 点击终端列表
            terminal_list = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, "//span[contains(text(), '终端列表')]"))
            )
            terminal_list.click()
            time.sleep(3)
            
            print("✅ 成功进入终端列表")
            return True
            
        except Exception as e:
            print(f"❌ 导航失败: {e}")
            return False
    
    def analyze_page_structure(self):
        """分析页面结构"""
        try:
            print("\n🔍 快速分析页面结构...")

            # 1. 查找所有复选框
            print("\n1️⃣ 查找所有复选框:")
            all_checkboxes = self.driver.find_elements(By.XPATH, "//input[@type='checkbox']")
            print(f"   总共找到 {len(all_checkboxes)} 个复选框")

            visible_checkboxes = []
            for i, checkbox in enumerate(all_checkboxes):
                try:
                    if checkbox.is_displayed() and checkbox.is_enabled():
                        visible_checkboxes.append(checkbox)
                        # 获取所在行信息
                        try:
                            tr_element = checkbox.find_element(By.XPATH, "./ancestor::tr[1]")
                            tr_text = tr_element.text[:80]
                            print(f"   可见复选框 {len(visible_checkboxes)}: {tr_text}...")
                        except:
                            print(f"   可见复选框 {len(visible_checkboxes)}: 无法获取行信息")
                except:
                    continue

            print(f"   找到 {len(visible_checkboxes)} 个可见可用的复选框")

            # 2. 测试关键选择器
            print("\n2️⃣ 测试关键选择器:")
            key_selectors = [
                "//tbody//tr[1]//input[@type='checkbox']",
                "//tr[contains(@class, 'el-table__row')][1]//input[@type='checkbox']",
                "//td[1]//input[@type='checkbox']",
                "//*[contains(@class, 'el-table__body')]//tr[1]//input[@type='checkbox']"
            ]

            for selector in key_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    if elements and elements[0].is_displayed():
                        print(f"   ✅ 选择器有效: '{selector}' - 找到 {len(elements)} 个元素")
                    else:
                        print(f"   ❌ 选择器无效: '{selector}'")
                except Exception as e:
                    print(f"   ❌ 选择器错误: '{selector}' - {e}")

            return True

        except Exception as e:
            print(f"❌ 页面结构分析失败: {e}")
            return False
    
    def try_click_first_checkbox(self):
        """尝试点击第一个可见的复选框"""
        try:
            print("\n🖱️ 尝试点击第一个可见的复选框...")
            
            # 使用最可能的选择器
            selectors = [
                "//tbody//tr[1]//input[@type='checkbox']",
                "//tr[contains(@class, 'el-table__row')][1]//input[@type='checkbox']",
                "//td[1]//input[@type='checkbox']"
            ]
            
            for selector in selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    for elem in elements:
                        if elem.is_displayed() and elem.is_enabled():
                            print(f"✅ 找到可点击的复选框，使用选择器: {selector}")
                            
                            # 尝试点击
                            self.driver.execute_script("arguments[0].click();", elem)
                            time.sleep(2)
                            
                            # 验证是否选中
                            is_checked = elem.is_selected()
                            print(f"   点击后状态: {'已选中' if is_checked else '未选中'}")
                            
                            return True
                except:
                    continue
            
            print("❌ 未找到可点击的复选框")
            return False
            
        except Exception as e:
            print(f"❌ 点击复选框失败: {e}")
            return False
    
    def run_debug(self):
        """运行调试程序"""
        print("🔍 复选框调试程序启动")
        print("=" * 50)
        
        if not self.setup_driver():
            return False
        
        try:
            if not self.login():
                return False
            
            if not self.navigate_to_terminal_list():
                return False
            
            if not self.analyze_page_structure():
                return False
            
            if not self.try_click_first_checkbox():
                return False
            
            print("\n🎉 调试完成！")
            
            # 保持浏览器打开一段时间以便观察
            print("浏览器将在10秒后关闭...")
            time.sleep(10)
            
            return True
            
        except Exception as e:
            print(f"❌ 调试过程中发生错误: {e}")
            return False
        finally:
            if self.driver:
                self.driver.quit()
                print("🔚 浏览器已关闭")

def main():
    """主函数"""
    debugger = CheckboxDebugger()
    success = debugger.run_debug()
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())
