# AR控制中心首页显示测试

## 测试信息
```yaml
test_name: "AR控制中心首页显示内容检查"
test_description: "检查控制中心首页各个卡片内显示内容的准确性"
test_timeout: 120
expected_result: "首页各卡片数据显示准确，气泡提示显示正确"
test_environment: "Linux控制中心"
test_priority: "P1"
test_category: "控制中心/首页"
```

## 前置条件
- 控制中心/客户端已安装成功
- 产生过一些病毒测试数据
- 系统已使用过一段时间

## 测试步骤

### 步骤1：登录控制中心
**操作描述**：使用浏览器登录AR控制中心

**目标图片**：
![登录控制中心](images/login_ar_center.png)

**操作类型**：登录
**等待时间**：5秒
**预期结果**：成功登录控制中心

---

### 步骤2：进入控制中心首页
**操作描述**：点击进入控制中心-首页

**目标图片**：
![控制中心首页](images/ar_center_homepage.png)

**操作类型**：点击
**等待时间**：3秒
**预期结果**：成功进入首页

---

### 步骤3：查看各个卡片内显示内容
**操作描述**：检查首页各个卡片（终端、注意、风险、安全）的数据显示

**目标图片**：
![首页卡片显示](images/homepage_cards_display.png)

**操作类型**：验证
**等待时间**：5秒
**预期结果**：卡片中的数据显示准确

---

### 步骤4：检查"终端"卡片气泡提示
**操作描述**：鼠标悬停在"终端"卡片上，查看气泡提示

**目标图片**：
![终端卡片气泡提示](images/terminal_card_tooltip.png)

**操作类型**：鼠标悬停
**等待时间**：3秒
**预期结果**：气泡提示"当前在线终端数 / 总安装数"

---

### 步骤5：检查"注意"卡片气泡提示
**操作描述**：鼠标悬停在"注意"卡片上，查看气泡提示

**目标图片**：
![注意卡片气泡提示](images/attention_card_tooltip.png)

**操作类型**：鼠标悬停
**等待时间**：3秒
**预期结果**：气泡提示："最近7天内未扫描，或者文件实时防护未开启，或者勒索检测功能未开启。但最近7天未发现病毒。"

---

### 步骤6：检查"风险"卡片气泡提示
**操作描述**：鼠标悬停在"风险"卡片上，查看气泡提示

**目标图片**：
![风险卡片气泡提示](images/risk_card_tooltip.png)

**操作类型**：鼠标悬停
**等待时间**：3秒
**预期结果**：气泡提示"最近7天内发现病毒（通过扫描、文件实时防护或勒索检测发现）"

---

### 步骤7：检查"安全"卡片气泡提示
**操作描述**：鼠标悬停在"安全"卡片上，查看气泡提示

**目标图片**：
![安全卡片气泡提示](images/security_card_tooltip.png)

**操作类型**：鼠标悬停
**等待时间**：3秒
**预期结果**：气泡提示"最近7天未发现病毒，且做过扫描，且文件实时防护、勒索检测功能都已开启"

---

### 步骤8：验证数据准确性
**操作描述**：对比卡片显示的数据与实际系统状态是否一致

**目标图片**：
![数据准确性验证](images/data_accuracy_verification.png)

**操作类型**：验证
**等待时间**：10秒
**预期结果**：所有卡片数据与实际系统状态一致

---

### 步骤9：截图保存测试结果
**操作描述**：对首页显示内容进行截图保存

**操作类型**：截图
**保存文件名**：ar_homepage_display_test_{timestamp}.png
**预期结果**：测试结果截图保存成功

## 测试验证点
```yaml
verification_points:
  - name: "首页正常加载"
    description: "控制中心首页能够正常加载显示"
    image: "images/ar_center_homepage.png"
    critical: true
  
  - name: "卡片数据显示准确"
    description: "各个卡片中的数据显示准确"
    image: "images/homepage_cards_display.png"
    critical: true
  
  - name: "终端卡片气泡提示正确"
    description: "终端卡片气泡提示显示正确"
    image: "images/terminal_card_tooltip.png"
    critical: true
    
  - name: "注意卡片气泡提示正确"
    description: "注意卡片气泡提示显示正确"
    image: "images/attention_card_tooltip.png"
    critical: true
    
  - name: "风险卡片气泡提示正确"
    description: "风险卡片气泡提示显示正确"
    image: "images/risk_card_tooltip.png"
    critical: true
    
  - name: "安全卡片气泡提示正确"
    description: "安全卡片气泡提示显示正确"
    image: "images/security_card_tooltip.png"
    critical: true
```

## 测试数据
```yaml
test_data:
  homepage_cards:
    - name: "终端"
      tooltip: "当前在线终端数 / 总安装数"
    - name: "注意"
      tooltip: "最近7天内未扫描，或者文件实时防护未开启，或者勒索检测功能未开启。但最近7天未发现病毒。"
    - name: "风险"
      tooltip: "最近7天内发现病毒（通过扫描、文件实时防护或勒索检测发现）"
    - name: "安全"
      tooltip: "最近7天未发现病毒，且做过扫描，且文件实时防护、勒索检测功能都已开启"
      
  verification_items:
    - "在线终端数量"
    - "总安装终端数量"
    - "最近7天扫描状态"
    - "文件实时防护状态"
    - "勒索检测功能状态"
    - "最近7天病毒发现情况"
```
