#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试病毒扫描弹窗的快速扫描选择和确定按钮
"""

import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from terminal_selector_utils import TerminalSelector

class ScanDialogTest:
    def __init__(self):
        self.driver = None
        self.wait = None
        
        self.config = {
            'url': 'http://172.16.103.202:8088',
            'username': 'admin',
            'password': 'Admin.2022',
            'timeout': 10
        }
    
    def setup_driver(self):
        """设置浏览器"""
        try:
            chrome_options = Options()
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--window-size=1920,1080")
            
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.maximize_window()
            self.wait = WebDriverWait(self.driver, self.config['timeout'])
            
            print("✅ 浏览器设置成功")
            return True
        except Exception as e:
            print(f"❌ 浏览器设置失败: {e}")
            return False
    
    def login_and_navigate(self):
        """登录并导航到终端列表"""
        try:
            print("🔄 登录控制中心...")
            
            self.driver.get(self.config['url'])
            time.sleep(3)
            
            # 登录
            username_input = self.wait.until(EC.element_to_be_clickable((By.NAME, "username")))
            username_input.clear()
            username_input.send_keys(self.config['username'])
            
            password_input = self.driver.find_element(By.ID, "password")
            password_input.clear()
            password_input.send_keys(self.config['password'])
            
            login_button = self.driver.find_element(By.XPATH, "//button[@type='submit']")
            login_button.click()
            time.sleep(5)
            
            # 导航到终端列表
            print("🔄 导航到终端列表...")
            
            terminal_menu = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, "//span[contains(text(), '终端管理')]"))
            )
            terminal_menu.click()
            time.sleep(2)
            
            terminal_list = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, "//span[contains(text(), '终端列表')]"))
            )
            terminal_list.click()
            time.sleep(3)
            
            print("✅ 成功登录并导航到终端列表")
            return True
            
        except Exception as e:
            print(f"❌ 登录或导航失败: {e}")
            return False
    
    def select_terminal_and_open_scan_dialog(self):
        """选择终端并打开扫描对话框"""
        try:
            print("\n🎯 选择终端...")
            
            # 使用标准终端选择器
            terminal_selector = TerminalSelector(self.driver, timeout=10)
            result = terminal_selector.select_terminals(count=1)
            
            if not result['success']:
                print(f"❌ 终端选择失败: {result['message']}")
                return False
            
            print(f"✅ 成功选择终端")
            time.sleep(2)
            
            # 查找病毒扫描按钮
            print("🔍 查找病毒扫描按钮...")
            scan_button_selectors = [
                "//button[contains(text(), '病毒扫描')]",
                "//span[contains(text(), '病毒扫描')]//parent::button",
                "//*[contains(text(), '病毒扫描')]",
                "//button[contains(@class, 'el-button') and contains(., '病毒扫描')]"
            ]
            
            scan_button = None
            for selector in scan_button_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    for elem in elements:
                        if elem.is_displayed() and elem.is_enabled():
                            scan_button = elem
                            print(f"✅ 找到病毒扫描按钮")
                            break
                    if scan_button:
                        break
                except:
                    continue
            
            if not scan_button:
                print("❌ 未找到病毒扫描按钮")
                return False
            
            # 点击病毒扫描按钮
            print("🖱️ 点击病毒扫描按钮...")
            self.driver.execute_script("arguments[0].click();", scan_button)
            time.sleep(3)
            
            print("✅ 病毒扫描对话框应该已打开")
            return True
            
        except Exception as e:
            print(f"❌ 选择终端和打开扫描对话框失败: {e}")
            return False
    
    def test_scan_dialog_options(self):
        """测试扫描对话框中的选项"""
        try:
            print("\n🔍 分析扫描对话框...")
            
            # 等待对话框出现
            time.sleep(3)
            
            # 查找对话框
            dialog_selectors = [
                "//div[contains(@class, 'el-dialog')]",
                "//div[contains(@class, 'dialog')]",
                "//*[contains(@class, 'modal')]"
            ]
            
            dialog = None
            for selector in dialog_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    for elem in elements:
                        if elem.is_displayed():
                            dialog = elem
                            print(f"✅ 找到对话框")
                            break
                    if dialog:
                        break
                except:
                    continue
            
            if not dialog:
                print("❌ 未找到对话框")
                return False
            
            # 分析对话框内容
            dialog_text = dialog.text
            print(f"📋 对话框内容: {dialog_text}")
            
            # 查找快速扫描选项
            print("\n🔍 查找快速扫描选项...")
            quick_scan_selectors = [
                "//input[@type='radio' and @value='快速扫描']",
                "//input[@type='radio']//following-sibling::span[contains(text(), '快速扫描')]",
                "//label[contains(text(), '快速扫描')]",
                "//span[contains(text(), '快速扫描')]",
                "//*[contains(text(), '快速扫描')]",
                "//div[contains(@class, 'el-radio') and contains(., '快速扫描')]",
                "//span[contains(@class, 'el-radio__label') and contains(text(), '快速扫描')]"
            ]
            
            quick_scan_options = []
            for i, selector in enumerate(quick_scan_selectors):
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    for j, elem in enumerate(elements):
                        if elem.is_displayed():
                            quick_scan_options.append({
                                'selector_index': i,
                                'element_index': j,
                                'element': elem,
                                'text': elem.text,
                                'tag': elem.tag_name,
                                'class': elem.get_attribute('class'),
                                'type': elem.get_attribute('type'),
                                'value': elem.get_attribute('value')
                            })
                            print(f"   选项 {len(quick_scan_options)}: 标签={elem.tag_name}, 类型={elem.get_attribute('type')}, 文本='{elem.text}', 类={elem.get_attribute('class')}")
                except Exception as e:
                    print(f"   选择器 {i+1} 失败: {e}")
            
            print(f"\n📊 找到 {len(quick_scan_options)} 个快速扫描相关元素")
            
            # 尝试选择快速扫描
            if quick_scan_options:
                print("\n🖱️ 尝试选择快速扫描...")
                for i, option in enumerate(quick_scan_options):
                    try:
                        print(f"   尝试点击选项 {i+1}: {option['text']}")
                        self.driver.execute_script("arguments[0].click();", option['element'])
                        time.sleep(1)
                        print(f"   ✅ 点击成功")
                        break
                    except Exception as e:
                        print(f"   ❌ 点击失败: {e}")
            
            # 查找确定按钮
            print("\n🔍 查找确定按钮...")
            confirm_selectors = [
                "//button[contains(text(), '确定')]",
                "//button[contains(text(), '确认')]",
                "//button[contains(text(), '开始')]",
                "//span[contains(text(), '确定')]//parent::button",
                "//span[contains(text(), '确认')]//parent::button",
                "//button[contains(@class, 'el-button--primary')]",
                "//div[contains(@class, 'el-dialog')]//button[contains(@class, 'el-button--primary')]"
            ]
            
            confirm_buttons = []
            for i, selector in enumerate(confirm_selectors):
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    for j, elem in enumerate(elements):
                        if elem.is_displayed() and elem.is_enabled():
                            confirm_buttons.append({
                                'selector_index': i,
                                'element_index': j,
                                'element': elem,
                                'text': elem.text,
                                'class': elem.get_attribute('class')
                            })
                            print(f"   按钮 {len(confirm_buttons)}: 文本='{elem.text}', 类={elem.get_attribute('class')}")
                except Exception as e:
                    print(f"   选择器 {i+1} 失败: {e}")
            
            print(f"\n📊 找到 {len(confirm_buttons)} 个确定按钮")
            
            # 点击确定按钮
            if confirm_buttons:
                print("\n🖱️ 点击确定按钮...")
                try:
                    button = confirm_buttons[0]['element']
                    print(f"   点击按钮: {confirm_buttons[0]['text']}")
                    self.driver.execute_script("arguments[0].click();", button)
                    time.sleep(2)
                    print("   ✅ 确定按钮点击成功")
                    return True
                except Exception as e:
                    print(f"   ❌ 点击确定按钮失败: {e}")
            
            return False
            
        except Exception as e:
            print(f"❌ 测试扫描对话框失败: {e}")
            return False
    
    def run_test(self):
        """运行测试"""
        print("🧪 病毒扫描对话框测试")
        print("=" * 50)
        
        if not self.setup_driver():
            return False
        
        try:
            if not self.login_and_navigate():
                return False
            
            if not self.select_terminal_and_open_scan_dialog():
                return False
            
            if not self.test_scan_dialog_options():
                return False
            
            print("\n🎉 测试完成！")
            
            # 保持浏览器打开观察结果
            print("浏览器将在30秒后关闭...")
            time.sleep(30)
            
            return True
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            return False
        finally:
            if self.driver:
                self.driver.quit()

def main():
    test = ScanDialogTest()
    success = test.run_test()
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())
