# AR终端分组测试

## 测试信息
```yaml
test_name: "AR控制中心终端分组功能测试"
test_description: "测试控制中心终端分组的创建、编辑、删除和移动功能"
test_timeout: 300
expected_result: "终端分组功能正常工作，能够正确管理终端分组"
test_environment: "Windows/Linux控制中心"
test_priority: "P1"
test_category: "控制中心/终端列表"
```

## 前置条件
- 已安装客户端

## 测试步骤

### 步骤1：登录控制中心
**操作描述**：使用浏览器登录AR控制中心

**目标图片**：
![登录控制中心](images/login_center_grouping.png)

**操作类型**：登录
**等待时间**：5秒
**预期结果**：成功登录控制中心

---

### 步骤2：进入终端列表
**操作描述**：进入控制中心-终端管理-终端列表

**目标图片**：
![进入终端列表](images/enter_terminal_list_grouping.png)

**操作类型**：导航
**等待时间**：3秒
**预期结果**：成功进入终端列表页面

---

### 步骤3：点击终端分组设置
**操作描述**：点击终端分组设置按钮，查看分组详情

**目标图片**：
![点击分组设置](images/click_group_settings.png)

**操作类型**：点击
**等待时间**：3秒
**预期结果**：打开终端分组管理界面

---

### 步骤4：查看现有分组
**操作描述**：查看当前已存在的终端分组

**目标图片**：
![查看现有分组](images/view_existing_groups.png)

**操作类型**：查看
**等待时间**：3秒
**预期结果**：可查看现有分组

---

### 步骤5：新建终端分组，并新增子分组
**操作描述**：点击新建按钮，创建新的终端分组。在某个已有的分组右侧，有个操作图标，点击之后选择其中的“添加子分组”，输入分组名称。

**目标图片**：
![新建终端分组](images/create_new_group.png)

**操作类型**：创建
**等待时间**：5秒
**预期结果**：成功创建新的终端分组，并新增子分组

---

### 步骤6：编辑终端分组
**操作描述**：选择已有分组进行编辑

**目标图片**：
![编辑终端分组](images/edit_terminal_group.png)

**操作类型**：编辑
**等待时间**：5秒
**预期结果**：能够编辑分组名称和描述

---

### 步骤7：配置分组属性
**操作描述**：设置分组名称、描述等属性

**目标图片**：
![配置分组属性](images/configure_group_properties.png)

**操作类型**：配置
**等待时间**：5秒
**预期结果**：成功配置分组属性

---

### 步骤8：保存分组设置
**操作描述**：保存新建或编辑的分组设置

**目标图片**：
![保存分组设置](images/save_group_settings.png)

**操作类型**：保存
**等待时间**：3秒
**预期结果**：分组设置保存成功

---

### 步骤9：选择要移动的客户端
**操作描述**：在终端列表中选择需要移动分组的客户端

**目标图片**：
![选择移动客户端](images/select_clients_to_move.png)

**操作类型**：选择
**等待时间**：3秒
**预期结果**：成功选择需要移动的客户端

---

### 步骤10：执行分组移动
**操作描述**：将选中的客户端移动至指定分组

**目标图片**：
![执行分组移动](images/execute_group_move.png)

**操作类型**：移动操作
**等待时间**：5秒
**预期结果**：客户端移动至需要被移动的分组里

---

### 步骤11：验证移动结果
**操作描述**：验证客户端是否成功移动到目标分组

**目标图片**：
![验证移动结果](images/verify_move_result.png)

**操作类型**：验证
**等待时间**：5秒
**预期结果**：客户端在目标分组中显示

---

### 步骤12：测试删除分组
**操作描述**：尝试删除空的测试分组

**目标图片**：
![删除分组测试](images/delete_group_test.png)

**操作类型**：删除
**等待时间**：5秒
**预期结果**：能够删除空的分组

---

### 步骤13：验证分组列表更新
**操作描述**：验证分组列表是否正确更新

**目标图片**：
![验证分组列表](images/verify_group_list_update.png)

**操作类型**：验证
**等待时间**：3秒
**预期结果**：分组列表正确反映所有操作结果

---

### 步骤14：截图保存测试结果
**操作描述**：对终端分组功能测试进行截图保存

**操作类型**：截图
**保存文件名**：ar_terminal_grouping_test_{timestamp}.png
**预期结果**：测试结果截图保存成功

## 测试验证点
```yaml
verification_points:
  - name: "分组管理界面正常打开"
    description: "终端分组管理界面能够正常打开"
    image: "images/click_group_settings.png"
    critical: true
  
  - name: "查看现有分组功能正常"
    description: "能够正确查看现有分组"
    image: "images/view_existing_groups.png"
    critical: true
  
  - name: "新建分组功能正常"
    description: "能够成功新建终端分组"
    image: "images/create_new_group.png"
    critical: true
    
  - name: "编辑分组功能正常"
    description: "能够正常编辑分组属性"
    image: "images/edit_terminal_group.png"
    critical: true
    
  - name: "分组移动功能正常"
    description: "能够正确移动客户端到指定分组"
    image: "images/execute_group_move.png"
    critical: true
    
  - name: "删除分组功能正常"
    description: "能够正确删除空的分组"
    image: "images/delete_group_test.png"
    critical: true
    
  - name: "分组状态正确更新"
    description: "所有操作后分组状态正确更新"
    image: "images/verify_group_list_update.png"
    critical: true
```

## 测试数据
```yaml
test_data:
  default_groups:
    - "未分组终端"
    - "默认分组"
    
  test_groups:
    - name: "测试分组1"
      description: "用于测试的分组1"
    - name: "Windows终端"
      description: "Windows系统终端分组"
    - name: "Linux终端"
      description: "Linux系统终端分组"
    - name: "服务器组"
      description: "服务器终端分组"
      
  group_operations:
    - "新建分组"
    - "编辑分组"
    - "删除分组"
    - "移动终端到分组"
    
  group_properties:
    - "分组名称"
    - "分组描述"
    - "创建时间"
    - "终端数量"
    
  move_operations:
    source_group: "未分组终端"
    target_group: "测试分组1"
    
  validation_points:
    - "分组创建成功"
    - "分组编辑生效"
    - "终端移动成功"
    - "分组删除成功"
    - "分组列表更新正确"
```
